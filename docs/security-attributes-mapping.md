# Security Attributes Mapping Table

## Complete camelCase → snake_case conversion for DS-160 Security Questions

### Security Part 2 (Criminal)
| ❌ Wrong (camelCase in code) | ✅ Correct (snake_case in data model) |
|---|---|
| `hasViolatedControlledSubstanceLaws` | `has_violated_controlled_substance_laws` |
| `engagedInProstitution` | `engaged_in_prostitution` |
| `engagedInMoneyLaundering` | `engaged_in_money_laundering` |
| `committedHumanTrafficking` | `committed_human_trafficking` |
| `aidedHumanTrafficking` | `aided_human_trafficking` |
| `isFamilyOfHumanTrafficker` | `is_family_of_human_trafficker` |

### Security Part 3 (Security)
| ❌ Wrong (camelCase in code) | ✅ Correct (snake_case in data model) |
|---|---|
| `engagedInEspionageOrSabotage` | `engaged_in_espionage_or_sabotage` |
| `engagedInTerroristActivities` | `engaged_in_terrorist_activities` |
| `providedSupportToTerrorists` | `provided_support_to_terrorists` |
| `isMemberOfTerroristOrganization` | `is_member_of_terrorist_organization` |
| `participatedInGenocide` | `participated_in_genocide` |
| `participatedInTorture` | `participated_in_torture` |
| `participatedInExtrajudicialKillings` | `participated_in_extrajudicial_killings` |
| `violatedReligiousFreedoms` | `violated_religious_freedoms` |
| `involvedInForcedPopulationControl` | `involved_in_forced_population_control` |
| `involvedInCoerciveOrganTransplantation` | `involved_in_coercive_organ_transplantation` |

### Security Part 4 (Immigration Violations)
| ❌ Wrong (camelCase in code) | ✅ Correct (snake_case in data model) |
|---|---|
| `soughtVisaByFraud` | `sought_visa_by_fraud` |
| `failedToAttendRemovalHearings` | `failed_to_attend_removal_hearings` |
| `beenUnlawfullyPresentOrOverstayed` | `been_unlawfully_present_or_overstayed` |
| `beenRemovedOrDeported` | `been_removed_or_deported` |

### Security Part 5 (Miscellaneous)
| ❌ Wrong (camelCase in code) | ✅ Correct (snake_case in data model) |
|---|---|
| `withheldCustodyOfUSCitizenChild` | `withheld_custody_of_us_citizen_child` |
| `votedInUSIllegally` | `voted_in_us_illegally` |
| `renouncedUSCitizenshipForTax` | `renounced_us_citizenship_for_tax` |
| `attendedPublicSchoolOnStudentVisaWithoutReimbursement` | `attended_public_school_on_student_visa_without_reimbursement` |

## Root Cause Analysis

### Why this happened:
1. **Code-first development**: Form filler was written before data model
2. **No validation**: No tests to catch AttributeError early
3. **Mixed conventions**: Different files use different naming conventions
4. **Copy-paste errors**: camelCase patterns copied across all Security parts

### Impact:
- **100% failure rate** for Security Parts 2-5
- **50+ attribute mismatches** across all Security forms
- **Complete process termination** on any Security question

### Prevention:
1. **Always check data model first** before writing form filler code
2. **Use IDE autocomplete** to avoid typos in attribute names
3. **Add validation tests** for all data model attributes
4. **Standardize on snake_case** for all Pydantic models (already done)

## Fix Strategy

### Phase 1: Bulk Replace (High Priority)
Replace all instances using search-replace patterns:
```
hasViolatedControlledSubstanceLaws → has_violated_controlled_substance_laws
engagedInProstitution → engaged_in_prostitution
engagedInMoneyLaundering → engaged_in_money_laundering
# ... etc for all mappings above
```

### Phase 2: Verify Data Paths (High Priority)
Ensure all security_data.attribute references use correct snake_case names

### Phase 3: Test All Security Parts (High Priority)
Run integration tests for all Security pages to catch remaining issues

### Phase 4: Add Validation (Medium Priority)
Add runtime validation to catch future naming mismatches