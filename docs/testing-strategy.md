# Стратегия тестирования DS-160 Bot без реального сайта

## 🎯 Цель

Создать comprehensive testing framework для проверки всех выявленных проблем DS-160 бота без необходимости обращения к реальному сайту ceac.state.gov.

---

## 🏗️ Архитектура тестирования

### 1. **Mock HTML Framework**

#### A. Статические HTML templates
```
tests/html_templates/
├── ds160_personal_page.html      # Страница личной информации
├── ds160_contact_page.html       # Страница контактов  
├── ds160_travel_page.html        # Страница путешествий
├── ds160_family_page.html        # Страница семьи
├── ds160_security_page.html      # Страница безопасности
├── fragments/
│   ├── social_media_section.html # Секция социальных сетей
│   ├── nationality_section.html  # Секция гражданства
│   └── sponsor_section.html      # Секция спонсора
```

#### B. Dynamic HTML generator
```python
# tests/html_generator.py
class DS160HTMLGenerator:
    def create_contact_page(self, 
                          social_media_slots=2, 
                          additional_emails=False,
                          additional_phones=False):
        """Генерирует HTML страницу контактов с нужными элементами"""
        
    def create_nationality_page(self, other_nationalities_count=3):
        """Генерирует страницу с множественным гражданством"""
        
    def create_travel_companions_page(self, companions_count=5):
        """Генерирует страницу с несколькими компаньонами"""
```

### 2. **Test Data Framework**

#### A. Проблемно-ориентированные тестовые данные
```
tests/test_data/
├── issues/
│   ├── dual_nationality_3_countries.json
│   ├── social_media_2_instagram.json  
│   ├── travel_companions_5_people.json
│   ├── us_visit_history_yes.json
│   ├── visa_refused_yes_with_explanation.json
│   ├── mailing_address_different.json
│   ├── parents_both_in_us.json
│   ├── multiple_residents_3_countries.json
│   ├── additional_education_2_schools.json
│   ├── passport_lost_stolen.json
│   ├── us_contact_org_only.json
│   └── immediate_relatives_in_us.json
├── edge_cases/
│   ├── empty_optional_fields.json
│   ├── maximum_entries.json
│   └── minimum_required_data.json
└── regression/
    ├── single_nationality.json      # Регрессия: одно гражданство работает
    ├── basic_contact_info.json      # Базовая контактная информация
    └── simple_travel_info.json     # Простая информация о поездке
```

### 3. **Playwright Mock Integration**

#### A. Mock Page Factory
```python
# tests/conftest.py
@pytest.fixture
async def mock_ds160_page():
    """Создает mock страницу с DS-160 HTML"""
    
    class MockDS160Page:
        def __init__(self):
            self.html_content = ""
            self.filled_fields = {}
            self.clicked_elements = []
            
        async def set_content(self, html):
            self.html_content = html
            
        async def fill(self, selector, value):
            self.filled_fields[selector] = value
            return True
            
        async def click(self, selector):
            self.clicked_elements.append(selector)
            return True
            
        async def select_option(self, selector, value):
            self.filled_fields[selector] = value
            return True
            
        def get_filled_value(self, selector):
            return self.filled_fields.get(selector)
            
        def was_clicked(self, selector):
            return selector in self.clicked_elements
    
    return MockDS160Page()
```

#### B. Realistic Response Simulation
```python
# tests/mock_responses.py
class DS160MockResponses:
    """Симулирует ответы сервера DS-160"""
    
    @staticmethod
    def personal_info_page():
        return {
            "status": 200,
            "html": load_template("ds160_personal_page.html")
        }
    
    @staticmethod  
    def contact_info_page_with_social_media():
        return {
            "status": 200,
            "html": load_template("ds160_contact_page.html",
                                context={"social_media_slots": 2})
        }
```

---

## 🧪 Типы тестов

### 1. **Unit Tests - Функции заполнения**

```python
# tests/test_form_filling_units.py

class TestSocialMediaFilling:
    """Тестирует заполнение социальных сетей"""
    
    async def test_fill_single_social_media(self, mock_page):
        """Тест заполнения одной социальной сети"""
        # Arrange
        data = create_test_data(social_media=[
            {"platform": "INSTAGRAM", "identifier": "test123"}
        ])
        
        # Act  
        result = await fill_social_media_section(mock_page, data.contact_info)
        
        # Assert
        assert result.success
        assert mock_page.get_filled_value(
            "select[name*='ddlSocialMedia']") == "INSTAGRAM"
        assert mock_page.get_filled_value(
            "input[name*='tbxSocialMediaIdent']") == "test123"
    
    async def test_fill_multiple_social_media(self, mock_page):
        """Тест заполнения нескольких социальных сетей"""
        # Arrange
        data = create_test_data(social_media=[
            {"platform": "INSTAGRAM", "identifier": "test123"},
            {"platform": "INSTAGRAM", "identifier": "test456"}
        ])
        
        # Act
        result = await fill_social_media_section(mock_page, data.contact_info)
        
        # Assert
        assert result.success
        assert len(result.filled_entries) == 2
        
        # Проверяем что заполнены оба слота (ctl00 и ctl01)
        assert mock_page.get_filled_value(
            "select[name='ctl00$SiteContentPlaceHolder$FormView1$dtlSocial$ctl00$ddlSocialMedia']") == "INSTAGRAM"
        assert mock_page.get_filled_value(
            "select[name='ctl00$SiteContentPlaceHolder$FormView1$dtlSocial$ctl01$ddlSocialMedia']") == "INSTAGRAM"
```

### 2. **Integration Tests - Полные сценарии**

```python
# tests/test_form_integration.py

class TestMultipleNationalityFlow:
    """Интеграционные тесты множественного гражданства"""
    
    async def test_dual_nationality_complete_flow(self, mock_browser):
        """Полный тест заполнения двойного гражданства"""
        # Arrange
        test_data = load_test_data("dual_nationality_3_countries.json")
        form_filler = FormFiller(mock_browser.page, selectors_config)
        
        # Act - эмулируем полный процесс заполнения
        result = await form_filler.fill_personal_info_page2(test_data)
        
        # Assert
        assert result.success
        
        # Проверяем что выбрано "Yes" для other nationalities
        assert mock_browser.was_clicked(
            "input[id='ctl00_SiteContentPlaceHolder_FormView1_rblAPP_OTH_NATL_IND_0']")
        
        # Проверяем что заполнены все 3 гражданства
        assert len(result.filled_nationalities) == 3
        
        # Проверяем что кликнуты кнопки "Add Nationality" 
        assert mock_browser.click_count("a[id*='AddNationality']") == 2
```

### 3. **Selector Validation Tests**

```python
# tests/test_selectors_validation.py

class TestSelectorsValidity:
    """Проверяет что селекторы корректны и актуальны"""
    
    def test_us_visit_history_selectors(self):
        """Проверяет селекторы для US visit history"""
        selectors = [
            "input[id='ctl00_SiteContentPlaceHolder_FormView1_rblPREV_US_TRAVEL_IND_0']",
            "input[id='ctl00_SiteContentPlaceHolder_FormView1_rblPREV_US_TRAVEL_IND_1']"
        ]
        
        for selector in selectors:
            assert is_valid_css_selector(selector)
            assert selector_exists_in_template("ds160_previous_travel.html", selector)
    
    def test_visa_refusal_selectors(self):
        """Проверяет селекторы для visa refusal"""
        selectors = [
            "input[id='ctl00_SiteContentPlaceHolder_FormView1_rblPREV_VISA_REFUSED_IND_0']",
            "input[id='ctl00_SiteContentPlaceHolder_FormView1_rblPREV_VISA_REFUSED_IND_1']"
        ]
        
        for selector in selectors:
            assert is_valid_css_selector(selector)
```

### 4. **Data Model Tests**

```python
# tests/test_data_models_issues.py

class TestDataModelIssues:
    """Тестирует проблемы в data models"""
    
    def test_multiple_nationalities_loading(self):
        """Тест загрузки множественного гражданства"""
        json_data = {
            "nationalityAndResidence": {
                "otherNationalities": [
                    {"country": "TURKEY", "hasPassport": "Yes", "passportNumber": "123"},
                    {"country": "RUSSIA", "hasPassport": "No"},
                    {"country": "GEORGIA", "hasPassport": "Yes", "passportNumber": "456"}
                ]
            }
        }
        
        loader = DataLoader.from_dict(json_data)
        data = loader.load_data()
        
        assert len(data.nationality_and_residence.other_nationalities) == 3
        assert data.nationality_and_residence.other_nationalities[0].country == "TURKEY"
        assert data.nationality_and_residence.other_nationalities[1].has_passport == "No"
        
    def test_us_visit_history_loading(self):
        """Тест загрузки истории визитов в США"""
        json_data = {
            "previousUSTravel": {
                "hasEverBeenInUS": "Yes",
                "previousVisits": [
                    {"dateArrived": "2024-01-15", "lengthOfStay": "30", "unitOfStay": "DAYS"}
                ]
            }
        }
        
        loader = DataLoader.from_dict(json_data)
        data = loader.load_data()
        
        assert data.previous_us_travel.has_ever_been_in_us == "Yes"
        assert len(data.previous_us_travel.previous_visits) == 1
```

### 5. **Regression Tests**

```python
# tests/test_regression.py

class TestRegressionSuite:
    """Тесты регрессии - проверяют что исправления не ломают работающую функциональность"""
    
    async def test_single_nationality_still_works(self, mock_page):
        """Проверяет что одно гражданство по-прежнему работает после исправления множественного"""
        data = create_test_data(other_nationalities=[
            {"country": "CANADA", "hasPassport": "Yes", "passportNumber": "AB123456"}
        ])
        
        result = await fill_nationality_section(mock_page, data)
        
        assert result.success
        assert len(result.filled_nationalities) == 1
        
    async def test_basic_social_media_unchanged(self, mock_page):
        """Проверяет что базовая функциональность социальных сетей не сломана"""
        data = create_test_data(social_media=[
            {"platform": "INSTAGRAM", "identifier": "basic_test"}
        ])
        
        result = await fill_social_media_section(mock_page, data)
        
        assert result.success
        assert result.filled_entries[0].platform == "INSTAGRAM"
```

---

## 🛠️ Инструменты и утилиты

### 1. **HTML Template Engine**

```python
# tests/utils/html_templates.py

class TemplateEngine:
    """Генерирует HTML шаблоны для тестирования"""
    
    @staticmethod
    def render_contact_page(**kwargs):
        """Рендерит страницу контактов с параметрами"""
        template = """
        <form>
            <!-- Social Media Section -->
            {% for i in range(social_media_slots) %}
            <select name='ctl00$SiteContentPlaceHolder$FormView1$dtlSocial$ctl{{i:02d}}$ddlSocialMedia'>
                <option value="INSTAGRAM">Instagram</option>
                <option value="FACEBOOK">Facebook</option>
            </select>
            <input name='ctl00$SiteContentPlaceHolder$FormView1$dtlSocial$ctl{{i:02d}}$tbxSocialMediaIdent' />
            {% endfor %}
            
            <!-- Additional Social Media Question -->
            <input name='ctl00$SiteContentPlaceHolder$FormView1$rblAddSocial' value='N' type='radio' />
            <input name='ctl00$SiteContentPlaceHolder$FormView1$rblAddSocial' value='Y' type='radio' />
        </form>
        """
        
        return jinja2.Template(template).render(**kwargs)
```

### 2. **Test Data Factory**

```python
# tests/utils/test_data_factory.py

class TestDataFactory:
    """Создает тестовые данные для различных сценариев"""
    
    @staticmethod
    def create_dual_nationality_case():
        """Создает данные с двойным гражданством"""
        return {
            "nationalityAndResidence": {
                "countryOfOrigin": "KAZAKHSTAN",
                "otherNationalities": [
                    {"country": "TURKEY", "hasPassport": "Yes", "passportNumber": "T123456"},
                    {"country": "RUSSIA", "hasPassport": "No"}
                ]
            }
        }
    
    @staticmethod  
    def create_multiple_social_media_case():
        """Создает данные с множественными социальными сетями"""
        return {
            "contactInfo": {
                "socialMedia": [
                    {"platform": "INSTAGRAM", "identifier": "test_account_1"},
                    {"platform": "INSTAGRAM", "identifier": "test_account_2"}
                ]
            }
        }
        
    @staticmethod
    def create_us_visit_history_case():
        """Создает данные с историей визитов в США"""
        return {
            "previousUSTravel": {
                "hasEverBeenInUS": "Yes",
                "previousVisits": [
                    {"dateArrived": "2023-06-15", "lengthOfStay": "60", "unitOfStay": "DAYS"}
                ]
            }
        }
```

### 3. **Assertion Helpers**

```python
# tests/utils/assertions.py

class DS160Assertions:
    """Специальные assertions для DS-160 тестов"""
    
    @staticmethod
    def assert_field_filled(mock_page, selector, expected_value):
        """Проверяет что поле заполнено правильным значением"""
        actual = mock_page.get_filled_value(selector)
        assert actual == expected_value, f"Field {selector}: expected '{expected_value}', got '{actual}'"
    
    @staticmethod
    def assert_radio_selected(mock_page, name, value):
        """Проверяет что выбран правильный radio button"""
        selector = f"input[name='{name}'][value='{value}']"
        assert mock_page.was_clicked(selector), f"Radio button {selector} was not selected"
    
    @staticmethod
    def assert_multiple_entries_filled(mock_page, base_selector, entries):
        """Проверяет заполнение множественных записей"""
        for i, entry in enumerate(entries):
            selector = base_selector.format(index=f"{i:02d}")
            DS160Assertions.assert_field_filled(mock_page, selector, entry)
```

---

## 🎯 Тестовые сценарии по проблемам

### Сценарий 1: Двойное гражданство
```python
async def test_dual_nationality_complete():
    """
    Тест: Пользователь имеет 3 гражданства
    Ожидание: Все 3 должны быть заполнены
    """
    # Given: данные с 3 гражданствами
    # When: заполняем форму
    # Then: все 3 гражданства заполнены, кнопки "Add" кликнуты
```

### Сценарий 2: Социальные сети
```python
async def test_multiple_instagram_accounts():
    """
    Тест: Пользователь имеет 2 Instagram аккаунта
    Ожидание: Оба должны быть заполнены
    """
    # Given: данные с 2 Instagram
    # When: заполняем социальные сети
    # Then: оба аккаунта заполнены в ctl00 и ctl01
```

### Сценарий 3: История визитов в США
```python
async def test_us_visit_history_yes():
    """
    Тест: Пользователь был в США
    Ожидание: Выбрано "Yes" и заполнены детали визитов
    """
    # Given: hasEverBeenInUS = "Yes"
    # When: заполняем previous US travel
    # Then: выбрано "Yes", заполнены даты и детали
```

---

## 📊 Metrics и Coverage

### 1. **Test Coverage Goals**
- **Unit Tests**: 95% coverage функций заполнения
- **Integration Tests**: 90% coverage полных сценариев  
- **Selector Tests**: 100% coverage всех селекторов
- **Data Model Tests**: 95% coverage валидации данных

### 2. **Performance Metrics**
- **Test Speed**: <5 секунд для полной test suite
- **Mock Accuracy**: 98% соответствие реальному поведению
- **False Positives**: <2% ложных срабатываний

### 3. **Quality Metrics**
- **Bug Detection Rate**: >95% выявления проблем
- **Regression Protection**: 100% защита от регрессий
- **Maintainability**: тесты обновляются <1 дня после изменений

---

## 🚀 Запуск тестов

### 1. **Полная test suite**
```bash
# Все тесты
pytest tests/ -v

# Только тесты проблем
pytest tests/test_form_filling_issues.py -v

# Только unit tests
pytest tests/ -m unit -v

# Только integration tests  
pytest tests/ -m integration -v
```

### 2. **Тесты конкретных проблем**
```bash
# Тест двойного гражданства
pytest -k "dual_nationality" -v

# Тест социальных сетей
pytest -k "social_media" -v  

# Тест истории визитов
pytest -k "us_visit_history" -v
```

### 3. **Coverage report**
```bash
# HTML coverage report
pytest --cov=src --cov-report=html

# Terminal coverage
pytest --cov=src --cov-report=term-missing
```

---

## 🎯 Преимущества такого подхода

### ✅ **Скорость**
- Тесты без браузера работают в 100x быстрее
- Нет зависимости от сетевого соединения
- Параллельное выполнение тестов

### ✅ **Надежность**  
- Детерминированные результаты
- Нет флакирующих тестов из-за сети
- Полный контроль над тестовой средой

### ✅ **Полнота**
- Тестирование всех edge cases
- 100% coverage проблемных сценариев
- Regression protection

### ✅ **Простота поддержки**
- Локальные HTML шаблоны легко обновлять
- Тестовые данные в JSON формате
- Четкое разделение ответственности

---

*Документ создан: 2025-08-03*
*Версия: 1.0*
*Статус: Ready for implementation*