# 🤖 Adaptive Form Filling System

## Overview

Адаптивная система заполнения форм для DS-160, которая динамически анализирует страницы и адаптируется к изменениям в форме, делая бота более устойчивым и гибким.

## 🚀 Key Features

### 1. Dynamic Page Analysis
- **Автоматическое обнаружение вопросов** на странице
- **Паттерн-матчинг** для сопоставления вопросов с полями данных
- **Динамический поиск селекторов** вместо жёстко закодированных

### 2. Intelligent Question Matching
- **Regex patterns** для распознавания различных формулировок
- **Fallback selectors** с несколькими вариантами для каждого элемента
- **Автоматическое определение типа вопроса** (radio, text, select)

### 3. Robust Error Handling
- **Graceful degradation** - откат к традиционным методам при сбое
- **Подробное логирование** всех действий и ошибок
- **Debug mode** для анализа неизвестных страниц

## 🏗️ Architecture

```
┌─────────────────────────────────────────┐
│           DS160PageAnalyzer             │
│  • Сканирует страницу                  │
│  • Находит все элементы формы          │
│  • Сопоставляет с паттернами           │
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│        AdaptiveFormFiller               │
│  • Использует результаты анализа       │
│  • Заполняет формы динамически         │
│  • Обрабатывает ошибки                 │
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│         Traditional FormFiller          │
│  • Fallback при сбое адаптивной        │
│  • Проверенные жёстко закодированные   │
│  • Селекторы                           │
└─────────────────────────────────────────┘
```

## 📝 Implementation Example

### Current Problem
```python
# ❌ Жёстко закодированные селекторы
visa_yes_selector = "input[id='ctl00_SiteContentPlaceHolder_FormView1_rblPREV_VISA_IND_0']"

# Что если селектор изменится?
# Что если вопрос не появляется на странице?
# Что если добавятся новые вопросы?
```

### Adaptive Solution
```python
# ✅ Адаптивный подход
detected_questions = await analyzer.analyze_page()

for question in detected_questions:
    if question.data_field == "previous_us_travel.has_ever_been_issued_us_visa":
        # Автоматически найденные селекторы
        success = await filler.fill_question_adaptive(question, data)
```

## 🎯 Question Pattern Examples

### Previous US Travel Patterns
```python
QUESTION_PATTERNS = {
    "us_visa": {
        "pattern": r"Have you ever been issued a (?:U\.S\.|United States) (?:Visa|visa)",
        "type": "radio",
        "data_field": "previous_us_travel.has_ever_been_issued_us_visa",
        "radio_base": "rblPREV_VISA_IND"
    },
    "travel_authorization": {
        "pattern": r"Have you ever been denied.*travel authorization",
        "type": "radio", 
        "data_field": "previous_us_travel.has_ever_been_denied_travel_authorization",
        "radio_base": "rblTRAVEL_AUTH.*DENIED",
        "explanation_base": "tbxTRAVEL_AUTH.*DENIED"
    }
}
```

## 🔍 Dynamic Selector Discovery

### How it Works
1. **Сканирование страницы**: Находим все radio buttons, text inputs, selects
2. **Паттерн-матчинг**: Сопоставляем найденные элементы с известными паттернами
3. **Построение селекторов**: Создаём список fallback селекторов для каждого вопроса
4. **Заполнение**: Пробуем селекторы по приоритету до первого успешного

### Example Discovery Process
```python
# 1. Найденные radio buttons на странице
found_radios = [
    {"name": "ctl00$SiteContentPlaceHolder$FormView1$rblPREV_VISA_IND", "value": "Y", "id": "...0"},
    {"name": "ctl00$SiteContentPlaceHolder$FormView1$rblPREV_VISA_IND", "value": "N", "id": "...1"},
    {"name": "ctl00$SiteContentPlaceHolder$FormView1$rblTRAVEL_AUTH_DENIED_IND", "value": "Y", "id": "...0"}
]

# 2. Паттерн matching
if re.search("rblPREV_VISA_IND", radio_name):
    # Сопоставлено с "us_visa" паттерном
    
# 3. Автоматическое построение селекторов
selectors = {
    "yes": ["input[id='ctl00_SiteContentPlaceHolder_FormView1_rblPREV_VISA_IND_0']"],
    "no": ["input[id='ctl00_SiteContentPlaceHolder_FormView1_rblPREV_VISA_IND_1']"]
}
```

## 🛡️ Benefits

### 1. Resilience to Changes
- **Автоматическая адаптация** к изменениям в селекторах DS-160
- **Обнаружение новых вопросов** без изменения кода
- **Handling conditional questions** - вопросы, которые появляются только при определённых условиях

### 2. Better Debugging
- **Полный анализ страницы** в debug режиме
- **Отчёты о найденных элементах** для разработчиков
- **Автоматические скриншоты** при ошибках

### 3. Maintainability
- **Меньше жёстко закодированных селекторов**
- **Централизованные паттерны** в одном месте
- **Easier to add new question types**

## 🚀 Future Enhancements

### 1. Machine Learning Integration
```python
# Потенциально в будущем
question_classifier = QuestionClassifier()
question_type = await question_classifier.classify(question_text)
```

### 2. Visual Element Recognition
```python
# Распознавание элементов по визуальным признакам
visual_analyzer = VisualElementAnalyzer()
buttons = await visual_analyzer.find_yes_no_buttons()
```

### 3. Self-Learning Selectors
```python
# Система обучается на успешных селекторах
selector_learner = SelectorLearner()
await selector_learner.learn_from_success(question, successful_selector)
```

## 📊 Usage Statistics

### Expected Improvements
- **95%+ success rate** for known question patterns
- **Automatic handling** of new conditional questions
- **50% reduction** in maintenance time for selector updates
- **Better error reporting** with specific question context

## 🔧 Integration Plan

### Phase 1: Testing (Current)
- [x] Implement `DS160PageAnalyzer` 
- [x] Implement `AdaptiveFormFiller`
- [x] Create comprehensive question patterns
- [ ] Test on Previous US Travel page

### Phase 2: Gradual Rollout
- [ ] Add adaptive mode flag to config
- [ ] Implement for high-priority sections (Contact, Security)
- [ ] A/B testing against traditional methods

### Phase 3: Full Integration
- [ ] Replace traditional methods where adaptive performs better  
- [ ] Add more question patterns for all DS-160 sections
- [ ] Performance optimization and caching

## 🎯 How to Test

### Manual Testing
```python
# В bot.py можно добавить:
from src.adaptive_form_filler import AdaptiveFormFiller

adaptive_filler = AdaptiveFormFiller(page)
success = await adaptive_filler.fill_previous_us_travel_adaptive(data)
```

### Debug Mode
```python
# Для анализа неизвестных страниц
analyzer = DS160PageAnalyzer(page)
await analyzer.debug_page_elements()  # Показывает все найденные элементы
detected = await analyzer.analyze_page()  # Показывает сопоставленные вопросы
```

## 💡 Key Innovation

> **Вместо того чтобы бот знал какие селекторы искать, он теперь понимает какие вопросы отвечать!**

Это принципиальная смена подхода:
- **Было**: "Найди элемент с ID xyz и кликни"
- **Стало**: "Найди вопрос о визах США и ответь на основе данных"

Такой подход делает бота намного более интеллектуальным и адаптивным к изменениям в DS-160 форме.