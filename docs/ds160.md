# DS-160 Form Data Types and Enums

This document describes the data types, enums, and validation rules from the DS-160 form automation system based on the Pydantic models in `data_loader.py`.

## Table of Contents
- [Core Enums](#core-enums)
- [Personal Information](#personal-information)
- [Nationality and Residence](#nationality-and-residence)
- [Passport Information](#passport-information)
- [Contact Information](#contact-information)
- [Travel Information](#travel-information)
- [Previous US Travel](#previous-us-travel)
- [US Contact Information](#us-contact-information)
- [Sponsor Information](#sponsor-information)
- [Family Information](#family-information)
- [Work and Education](#work-and-education)
- [Security and Background](#security-and-background)
- [Data Structure](#data-structure)
- [Validation Rules](#validation-rules)

## Core Enums

### Gender (Required)
```python
class Gender(str, Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"
```

### Marital Status (Required)
**DS-160 form uses single-letter codes:**
```python
class MaritalStatus(str, Enum):
    SINGLE = "S"              # Single/Never Married
    MARRIED = "M"             # Married
    DIVORCED = "D"            # Divorced
    WIDOWED = "W"             # Widowed
    LEGALLY_SEPARATED = "L"   # Legally Separated
    COMMON_LAW = "C"          # Common Law
    CIVIL_UNION = "P"         # Civil Union/Domestic Partnership
    OTHER = "O"               # Other
```

**Legacy Support**: Accepts full names (e.g., "MARRIED") and converts to codes automatically.

### Visa Purpose
```python
class VisaPurpose(str, Enum):
    BUSINESS_TOURISM = "TEMP. BUSINESS OR PLEASURE VISITOR (B)"
    STUDY = "STUDENT (F)"
    WORK = "TEMPORARY WORKER (H)"
    TRANSIT = "TRANSIT (C)"
    OTHER = "OTHER"
```

### Primary Occupation (DS-160 Categories - Actual Form Data)
**DS-160 uses broad occupation categories, not specific job titles. Select by value:**

```python
class PrimaryOccupation(str, Enum):
    AGRICULTURE = "A"                    # Agriculture/Farming
    ARTIST_PERFORMER = "AP"              # Artist/Performer
    BUSINESS = "B"                       # Business/Management/Finance
    COMMUNICATIONS = "CM"                # Communications/Media/PR
    COMPUTER_SCIENCE = "CS"              # Computer Science/IT/Tech
    CULINARY_FOOD_SERVICES = "C"         # Culinary/Food Services
    EDUCATION = "ED"                     # Education/Teaching
    ENGINEERING = "EN"                   # Engineering (all types)
    GOVERNMENT = "G"                     # Government/Civil Service
    HOMEMAKER = "H"                      # Homemaker
    LEGAL_PROFESSION = "LP"              # Legal Profession
    MEDICAL_HEALTH = "MH"                # Medical/Health
    MILITARY = "M"                       # Military
    NATURAL_SCIENCE = "NS"               # Natural Science/Biology
    NOT_EMPLOYED = "N"                   # Not Employed
    PHYSICAL_SCIENCES = "PS"             # Physical Sciences/Physics/Chemistry
    RELIGIOUS_VOCATION = "RV"            # Religious Vocation
    RESEARCH = "R"                       # Research
    RETIRED = "RT"                       # Retired
    SOCIAL_SCIENCE = "SS"                # Social Science/Psychology
    STUDENT = "S"                        # Student
    OTHER = "O"                          # Other
```

### Modern Occupation → DS-160 Category Mappings
**The form uses broad categories, not specific jobs. Here's how to map modern occupations:**

| Modern Occupation | DS-160 Category | Code |
|------------------|-----------------|------|
| **Business & Management** |
| Social Media Manager | BUSINESS | B |
| Digital Marketing Manager | BUSINESS | B |
| Marketing Manager | BUSINESS | B |
| Project Manager | BUSINESS | B |
| Business Analyst | BUSINESS | B |
| Consultant | BUSINESS | B |
| CEO/Executive | BUSINESS | B |
| Sales Representative | BUSINESS | B |
| Accountant | BUSINESS | B |
| **Technology** |
| Software Engineer | COMPUTER SCIENCE | CS |
| Software Developer | COMPUTER SCIENCE | CS |
| Web Developer | COMPUTER SCIENCE | CS |
| Data Scientist | COMPUTER SCIENCE | CS |
| IT Specialist | COMPUTER SCIENCE | CS |
| Programmer | COMPUTER SCIENCE | CS |
| **Engineering** |
| All Engineers | ENGINEERING | EN |
| **Arts & Design** |
| Graphic Designer | ARTIST/PERFORMER | AP |
| UX Designer | ARTIST/PERFORMER | AP |
| Artist | ARTIST/PERFORMER | AP |
| Photographer | ARTIST/PERFORMER | AP |
| Writer | ARTIST/PERFORMER | AP |
| **Healthcare** |
| Doctor/Physician | MEDICAL/HEALTH | MH |
| Nurse | MEDICAL/HEALTH | MH |
| Dentist | MEDICAL/HEALTH | MH |
| **Education** |
| Teacher | EDUCATION | ED |
| Professor | EDUCATION | ED |
| **Legal** |
| Lawyer/Attorney | LEGAL PROFESSION | LP |
| **Other Categories** |
| Researcher | RESEARCH | R |
| Scientist | NATURAL SCIENCE | NS |
| Government Employee | GOVERNMENT | G |
| Student | STUDENT | S |
| Retired | RETIRED | RT |
| Homemaker | HOMEMAKER | H |
| Unemployed | NOT EMPLOYED | N |

**Key Insight**: DS-160 uses simple category codes (A, B, CS, etc.) rather than detailed job titles. The system automatically maps specific occupations to appropriate categories.

## Personal Information

### PersonalInfo Model
```python
class PersonalInfo(BaseModel):
    surnames: str                                    # Required, 1-100 chars
    given_names: str                                 # Required, 1-100 chars (alias: givenNames)
    full_name_native_alphabet: Optional[str]        # Optional (alias: fullNameNativeAlphabet)
    other_names_used: List[OtherNamesUsed]          # Optional (alias: otherNamesUsed)
    telecode_represents_name: str = "No"            # Default: "No" (alias: telecodeRepresentsName)
    sex: Gender                                      # Required
    marital_status: MaritalStatus                    # Required (alias: maritalStatus)
    marital_status_other_explanation: Optional[str] # For marital_status="O" only
    date_of_birth: date                             # Required YYYY-MM-DD (alias: dateOfBirth)
    place_of_birth: PlaceOfBirth                    # Required (alias: placeOfBirth)
    spouse: Optional[SpouseInfo]                     # For MARRIED/COMMON_LAW/CIVIL_UNION
    deceased_spouse: Optional[DeceasedSpouseInfo]    # For WIDOWED (alias: deceasedSpouse)
    divorce_info: Optional[DivorceInfo]             # For DIVORCED (alias: divorceInfo)
```

### Supporting Models
```python
class PlaceOfBirth(BaseModel):
    city: str                    # Required, 1-100 chars
    state_province: str          # Required, 1-100 chars (alias: stateProvince)
    country_region: str          # Required, 1-100 chars (alias: countryRegion)

class OtherNamesUsed(BaseModel):
    other_surnames: str          # Required, 1-100 chars
    other_given_names: str       # Required, 1-100 chars

class SpouseInfo(BaseModel):
    surnames: str                # Required, 1-100 chars
    given_names: str             # Required, 1-100 chars (alias: givenNames)
    date_of_birth: date          # Required (alias: dateOfBirth)
    place_of_birth: PlaceOfBirth # Required (alias: placeOfBirth)
    nationality: str             # Required, 1-100 chars
    address_type: str = "H"      # H=Home, M=Mailing, U=US, D=Don't Know, O=Other
    address: Optional[Address]   # Only if address_type="O"
```

### Native Alphabet Names
**Required for countries using non-Latin scripts (e.g., Kazakhstan, Russia, China)**
- Field: `fullNameNativeAlphabet`
- Example: "ТЕСТОВ МУЛЬТИ НЕШЕНАЛИТИ" (Cyrillic for Kazakhstan)
- If not provided, bot uses romanized fallback

## Nationality and Residence

### NationalityAndResidence Model
```python
class NationalityAndResidence(BaseModel):
    country_of_origin: str                           # Required, 1-100 chars
    other_nationalities: List[OtherNationality]      # Optional, list of additional nationalities
    is_permanent_resident_of_other_country: str      # "Yes"/"No", default "No"
    permanent_resident_countries: List[str]          # Optional, list of countries
    national_identification_number: Optional[str]   # Optional, max 50 chars
    us_social_security_number: Optional[str]         # Optional, max 15 chars
    us_taxpayer_id_number: Optional[str]            # Optional, max 15 chars

class OtherNationality(BaseModel):
    country: str                     # Required, 1-100 chars
    has_passport: str = "No"         # "Yes"/"No", default "No"
    passport_number: Optional[str]   # Required if has_passport="Yes"
```

### Country Names
Standard country names as they appear in DS-160 dropdowns (case-sensitive):
```
AFGHANISTAN, ALBANIA, ALGERIA, ANDORRA, ANGOLA, ANTIGUA AND BARBUDA,
ARGENTINA, ARMENIA, AUSTRALIA, AUSTRIA, AZERBAIJAN, BAHAMAS, BAHRAIN,
BANGLADESH, BARBADOS, BELARUS, BELGIUM, BELIZE, BENIN, BHUTAN, BOLIVIA,
BOSNIA AND HERZEGOVINA, BOTSWANA, BRAZIL, BRUNEI, BULGARIA, BURKINA FASO,
BURUNDI, CAMBODIA, CAMEROON, CANADA, CAPE VERDE, CENTRAL AFRICAN REPUBLIC,
CHAD, CHILE, CHINA, COLOMBIA, COMOROS, CONGO, COSTA RICA, COTE D'IVOIRE,
CROATIA, CUBA, CYPRUS, CZECH REPUBLIC, DEMOCRATIC REPUBLIC OF THE CONGO,
DENMARK, DJIBOUTI, DOMINICA, DOMINICAN REPUBLIC, ECUADOR, EGYPT,
EL SALVADOR, EQUATORIAL GUINEA, ERITREA, ESTONIA, ETHIOPIA, FIJI, FINLAND,
FRANCE, GABON, GAMBIA, GEORGIA, GERMANY, GHANA, GREECE, GRENADA, GUATEMALA,
GUINEA, GUINEA-BISSAU, GUYANA, HAITI, HONDURAS, HUNGARY, ICELAND, INDIA,
INDONESIA, IRAN, IRAQ, IRELAND, ISRAEL, ITALY, JAMAICA, JAPAN, JORDAN,
KAZAKHSTAN, KENYA, KIRIBATI, KUWAIT, KYRGYZSTAN, LAOS, LATVIA, LEBANON,
LESOTHO, LIBERIA, LIBYA, LIECHTENSTEIN, LITHUANIA, LUXEMBOURG, MACEDONIA,
MADAGASCAR, MALAWI, MALAYSIA, MALDIVES, MALI, MALTA, MARSHALL ISLANDS,
MAURITANIA, MAURITIUS, MEXICO, MICRONESIA, MOLDOVA, MONACO, MONGOLIA,
MONTENEGRO, MOROCCO, MOZAMBIQUE, MYANMAR, NAMIBIA, NAURU, NEPAL,
NETHERLANDS, NEW ZEALAND, NICARAGUA, NIGER, NIGERIA, NORTH KOREA, NORWAY,
OMAN, PAKISTAN, PALAU, PANAMA, PAPUA NEW GUINEA, PARAGUAY, PERU,
PHILIPPINES, POLAND, PORTUGAL, QATAR, ROMANIA, RUSSIA, RWANDA, SAINT KITTS AND NEVIS,
SAINT LUCIA, SAINT VINCENT AND THE GRENADINES, SAMOA, SAN MARINO,
SAO TOME AND PRINCIPE, SAUDI ARABIA, SENEGAL, SERBIA, SEYCHELLES,
SIERRA LEONE, SINGAPORE, SLOVAKIA, SLOVENIA, SOLOMON ISLANDS, SOMALIA,
SOUTH AFRICA, SOUTH KOREA, SOUTH SUDAN, SPAIN, SRI LANKA, SUDAN, SURINAME,
SWAZILAND, SWEDEN, SWITZERLAND, SYRIA, TAJIKISTAN, TANZANIA, THAILAND,
TIMOR-LESTE, TOGO, TONGA, TRINIDAD AND TOBAGO, TUNISIA, TURKEY, TURKMENISTAN,
TUVALU, UGANDA, UKRAINE, UNITED ARAB EMIRATES, UNITED KINGDOM, UNITED STATES,
URUGUAY, UZBEKISTAN, VANUATU, VATICAN CITY, VENEZUELA, VIETNAM, YEMEN, ZAMBIA, ZIMBABWE
```

### Multiple Nationality Pattern
- Uses repeater pattern: `ctl00`, `ctl01`, `ctl02`
- Requires "Add Another" button clicks between entries
- Maximum supported: varies by form version (typically 3-5)

## Passport Information

### PassportInfo Model
```python
class PassportInfo(BaseModel):
    passport_type: str = "REGULAR"                   # Default "REGULAR"
    passport_number: str                             # Required, 1-50 chars
    passport_book_number: Optional[str]              # Optional, max 50 chars
    issuing_country: str                             # Required, 1-100 chars
    issuing_city: str                                # Required, 1-100 chars
    issuing_state_province: str                      # Required, 1-100 chars
    issuance_date: date                              # Required YYYY-MM-DD
    expiration_date: date                            # Required YYYY-MM-DD
    has_lost_or_stolen_passport: Optional[LostPassportInfo]  # Optional

class LostPassportInfo(BaseModel):
    answer: str                          # "Yes"/"No"
    lost_passport_number: Optional[str]  # If answer="Yes"
    lost_passport_issuing_country: Optional[str]  # If answer="Yes"
    explanation: Optional[str]           # If answer="Yes"
```

## Contact Information

### ContactInfo Model
```python
class ContactInfo(BaseModel):
    mailing_address: Address         # Required
    phone_numbers: PhoneNumbers      # Required
    email_addresses: EmailAddresses  # Required
    social_media: List[SocialMedia]  # Optional

class Address(BaseModel):
    street_line1: str                # Required, 1-200 chars
    street_line2: Optional[str]      # Optional, max 200 chars
    city: str                        # Required, 1-100 chars
    state_province: Optional[str]    # Optional, max 100 chars
    postal_zone_zip_code: str        # Required, 1-20 chars
    country_region: str              # Required, 1-100 chars

class PhoneNumbers(BaseModel):
    primary: str                     # Required (Home phone)
    secondary: Optional[str]         # Optional (Mobile phone)
    work: Optional[str]              # Optional (Business phone)
    other: Optional[str]             # Optional

class EmailAddresses(BaseModel):
    primary: EmailStr                # Required (validated email)
    additional: Optional[EmailStr]   # Optional

class SocialMedia(BaseModel):
    platform: str                    # Required (from DS-160 dropdown)
    identifier: str                  # Required (username/handle)
```

**Important**: All phone numbers must be different - form validates uniqueness.

### Social Media Platforms (Exact Values)
Available options in DS-160 dropdown:
```
ASK.FM
DOUBAN
FACEBOOK
FLICKR
GOOGLE+
INSTAGRAM
LINKEDIN
MYSPACE
PINTEREST
QZONE (QQ)
REDDIT
SINA WEIBO
TENCENT WEIBO
TUMBLR
TWITTER
TWOO
VINE
VKONTAKTE (VK)
YOUKU
YOUTUBE
NONE
```

### Social Media Logic
1. **Standard Social Media** (dropdown selection)
   - First platform goes to main dropdown
   - Limited to 2 entries: `ctl00`, `ctl01`

2. **Additional Social Media** (text fields)
   - Appears when answering "Yes" to presence question
   - Uses text input fields, not dropdowns
   - Fields: `tbxAddSocialPlat`, `tbxAddSocialHand`

## Travel Information

### TravelInfo Model
```python
class TravelInfo(BaseModel):
    purpose_of_trip: str                 # Required (from VisaPurpose enum)
    visa_class: str                      # Required
    has_specific_travel_plans: str = "Yes"  # "Yes"/"No", default "Yes"
    arrival_date: date                   # Required YYYY-MM-DD
    arrival_flight: Optional[str]        # Optional
    arrival_city: str                    # Required
    departure_date: Optional[date]       # REQUIRED if has_specific_travel_plans="Yes"
    departure_flight: Optional[str]      # Optional
    departure_city: Optional[str]        # REQUIRED if has_specific_travel_plans="Yes"
    locations_to_visit_in_us: List[str]  # REQUIRED if has_specific_travel_plans="Yes"
    us_stay_address: USAddress           # Required
    person_entity_paying: str            # Required

class USAddress(BaseModel):
    street_line1: str    # Required, 1-200 chars
    city: str           # Required, 1-100 chars
    state: str          # Required, 1-100 chars (US states only)
    zip_code: str       # Required, 1-20 chars

class TravelCompanions(BaseModel):
    are_other_persons_traveling: str = "No"    # "Yes"/"No"
    persons_traveling: List[TravelCompanion]   # If above="Yes"
    is_traveling_as_part_of_group: str = "No"  # "Yes"/"No"
    group_name: Optional[str]                  # If above="Yes"
```

### Person Entity Paying Options
```
S - SELF
P - OTHER PERSON  
C - OTHER COMPANY/ORGANIZATION
U - U.S. PETITIONER
E - PRESENT EMPLOYER
F - EMPLOYER IN THE U.S.
```

## Previous US Travel

### PreviousUSTravel Model
```python
class PreviousUSTravel(BaseModel):
    has_ever_been_in_us: str = "No"                        # "Yes"/"No"
    previous_visits: List[PreviousVisit]                    # If above="Yes"
    has_ever_held_us_license: str = "No"                   # "Yes"/"No"
    drivers_licenses: List[DriversLicense]                  # If above="Yes"
    has_ever_been_issued_us_visa: str = "No"               # "Yes"/"No"
    previous_visa_info: Optional[PreviousVisaInfo]         # If above="Yes"
    has_ever_been_refused_admission_or_visa: Optional[RefusalInfo]     # Optional
    has_immigrant_petition_been_filed: Optional[RefusalInfo]           # Optional
    has_ever_been_denied_travel_authorization: Optional[RefusalInfo]   # Optional
```

## US Contact Information

### USContact Model
```python
class USContact(BaseModel):
    contact_person_surnames: str         # Required, 1-100 chars
    contact_person_given_names: str      # Required, 1-100 chars
    organization_name: Optional[str]     # RECOMMENDED - may be required by form
    relationship_to_you: str             # Required (from relationship codes)
    address: USAddress                   # Required
    phone: str                           # Required
    email: EmailStr                     # Required (validated email)
```

### Relationship Types (with codes)
```
R - RELATIVE
S - SPOUSE  
C - FRIEND
B - BUSINESS ASSOCIATE    # ← Use this instead of "BUSINESS CONTACT"
P - EMPLOYER
H - SCHOOL OFFICIAL
O - OTHER
```

### Organization Requirements
- **BUSINESS ASSOCIATE**: Organization required
- **EMPLOYER**: Organization required  
- **FRIEND**: Organization may still be required by form
- **RELATIVE**: Organization typically not required

**Best Practice**: Always provide organization name to avoid validation errors.

## Sponsor Information

### SponsorInfo Model (Conditional based on person_entity_paying)
```python
class SponsorInfo(BaseModel):
    person_sponsor: Optional[PersonSponsor]           # For person_entity_paying="P"
    company_sponsor: Optional[CompanySponsor]         # For person_entity_paying="C"
    us_petitioner_sponsor: Optional[USPetitionerSponsor]  # For person_entity_paying="U"
    employer_sponsor: Optional[EmployerSponsor]       # For person_entity_paying="E"/"F"

class PersonSponsor(BaseModel):
    surnames: str                    # Required, 1-100 chars
    given_names: str                 # Required, 1-100 chars
    relationship: str                # Required
    address: USAddress               # Required
    phone: str                       # Required
    email: EmailStr                  # Required

class CompanySponsor(BaseModel):
    company_name: str                # Required, 1-200 chars
    contact_person_surnames: str     # Required, 1-100 chars
    contact_person_given_names: str  # Required, 1-100 chars
    relationship: str                # Required
    address: USAddress               # Required
    phone: str                       # Required
    email: EmailStr                  # Required
```

## Family Information

### FamilyInfo Model
```python
class FamilyInfo(BaseModel):
    father: FamilyMember                            # Required
    mother: FamilyMember                            # Required
    has_immediate_relatives_in_us: str = "No"       # "Yes"/"No"
    immediate_relatives: List[ImmediateRelative]    # If above="Yes"

class FamilyMember(BaseModel):
    surnames: str            # Required, 1-100 chars
    given_names: str         # Required, 1-100 chars
    date_of_birth: date      # Required YYYY-MM-DD
    is_in_the_us: str = "No" # "Yes"/"No"
    status: str              # Required (see status options below)

class ImmediateRelative(BaseModel):
    surnames: str        # Required, 1-100 chars
    given_names: str     # Required, 1-100 chars
    relationship: str    # Required (SPOUSE, CHILD, PARENT, SIBLING, etc.)
    status: str          # Required
```

### Family Member Status Options
```
FOREIGN NATIONAL
U.S. CITIZEN
U.S. LAWFUL PERMANENT RESIDENT
NONIMMIGRANT
OTHER
```

## Work and Education

### WorkAndEducation Model
```python
class WorkAndEducation(BaseModel):
    present: PresentWork                        # Required
    previous_work: List[PreviousWork]           # Optional
    previous_education: List[PreviousEducation] # Optional
    additional_info: Optional[AdditionalInfo]   # Optional

class PresentWork(BaseModel):
    primary_occupation: str          # Required
    employer_or_school_name: str     # Required
    address: WorkEducationAddress    # Required
    start_date: date                 # Required YYYY-MM-DD
    monthly_income_local: Optional[str]  # Optional
    duties: str                      # Required

class AdditionalInfo(BaseModel):
    clan_or_tribe_name: Optional[str]                       # Optional
    languages_spoken: List[str]                             # Optional
    countries_visited_last_five_years: List[str]            # Optional
    charitable_organizations_worked_for: Optional[CharitableOrganization]  # Optional
    specialized_skills: Optional[YesNoAnswer]               # Optional
    has_served_in_military: Optional[MilitaryService]       # Optional
    has_been_in_rebel_group: Optional[YesNoAnswer]          # Optional
```

## Security and Background

### SecurityAndBackground Model
```python
class SecurityAndBackground(BaseModel):
    part1_medical_and_health: Optional[SecurityPart1]      # Optional
    part2_criminal: Optional[SecurityPart2]                # Optional
    part3_security: Optional[SecurityPart3]                # Optional
    part4_immigration_violations: Optional[SecurityPart4]  # Optional
    part5_miscellaneous: Optional[SecurityPart5]           # Optional

# All YesNoAnswer fields follow the same pattern:
class YesNoAnswer(BaseModel):
    answer: str                     # "Yes"/"No" - REQUIRED
    explanation: Optional[str]      # Required if answer="Yes", max 4000 chars

# Examples of Security Parts:
class SecurityPart1(BaseModel):  # Medical and Health
    has_communicable_disease: Optional[YesNoAnswer]
    has_mental_or_physical_disorder: Optional[YesNoAnswer]
    is_drug_abuser_or_addict: Optional[YesNoAnswer]

class SecurityPart2(BaseModel):  # Criminal
    has_been_arrested: Optional[YesNoAnswer]
    has_violated_controlled_substance_laws: Optional[YesNoAnswer]
    engaged_in_prostitution: Optional[YesNoAnswer]
    engaged_in_money_laundering: Optional[YesNoAnswer]
    committed_human_trafficking: Optional[YesNoAnswer]
    aided_human_trafficking: Optional[YesNoAnswer]
    is_family_of_human_trafficker: Optional[YesNoAnswer]
```

### Security Parts Structure
1. **Part 1**: Medical and Health (3 questions)
2. **Part 2**: Criminal (7 questions) 
3. **Part 3**: Security (10 questions)
4. **Part 4**: Immigration Violations (4 questions)
5. **Part 5**: Miscellaneous (4 questions)

**All security questions use snake_case attributes in data models.**

## Data Structure

### Complete DS160Data Model
```python
class DS160Data(BaseModel):
    application_info: ApplicationInfo = Field(default_factory=ApplicationInfo)  # Auto-generated
    personal_info: PersonalInfo                     # REQUIRED
    nationality_and_residence: NationalityAndResidence  # REQUIRED
    passport_info: PassportInfo                     # REQUIRED
    contact_info: ContactInfo                       # REQUIRED
    travel_info: TravelInfo                         # REQUIRED
    travel_companions: Optional[TravelCompanions]   # Optional
    previous_us_travel: Optional[PreviousUSTravel]  # Optional
    us_contact: USContact                           # REQUIRED
    sponsor_info: Optional[SponsorInfo]             # Optional (conditional)
    family_info: FamilyInfo                         # REQUIRED
    work_and_education: WorkAndEducation            # REQUIRED
    security_and_background: Optional[SecurityAndBackground]  # Optional
```

### Alias Support
The data loader supports both `camelCase` and `snake_case` field names:

```json
{
  "personalInfo": {
    "givenNames": "JOHN",        // ← camelCase (alias)
    "given_names": "JOHN"        // ← snake_case (canonical)
  }
}
```

**Common aliases:**
- `givenNames` → `given_names`
- `dateOfBirth` → `date_of_birth`
- `placeOfBirth` → `place_of_birth`
- `maritalStatus` → `marital_status`
- `fullNameNativeAlphabet` → `full_name_native_alphabet`

## Form Patterns and Conventions

### Repeater Patterns
DS-160 uses consistent repeater patterns for multiple entries:
```
ctl00, ctl01, ctl02, ctl03, ...
```

### Add Another Buttons
```javascript
// Pattern for different sections
"a[id*='_ctl{XX}_InsertButton{SECTION}']"

// Examples:
"a[id='ctl00_SiteContentPlaceHolder_FormView1_dtlOTHER_NATL_ctl00_InsertButtonOTHER_NATL']"
"a[id='ctl00_SiteContentPlaceHolder_FormView1_dtlSocial_ctl00_InsertButtonSocial']"
```

### Field Naming Convention
```javascript
// Input fields: tbx{FIELD_NAME}
"input[name*='tbxAPP_SURNAME']"
"input[name*='tbxAPP_GIVEN_NAME']"

// Dropdowns: ddl{FIELD_NAME}  
"select[name*='ddlCountry']"
"select[name*='ddlAPP_GENDER']"

// Radio button groups: rbl{FIELD_NAME}
"input[name*='rblOtherNames'][value='N']"
"input[name*='rblSpecificTravel'][value='Y']"
```

## Validation Rules

### Common Validation Errors
1. **Missing Required Fields**: "has not been completed"
2. **Duplicate Values**: "has already been entered" (phone numbers)
3. **Invalid Dates**: "is invalid. Month, Day, and Year are required"
4. **Missing Selections**: "has not been selected"

### Field Dependencies
- **Native Alphabet**: Required for non-Latin script countries
- **Organization**: Required for business relationships
- **Passport Numbers**: Required when "Yes" selected for passport ownership
- **Departure Info**: Required when specific travel plans = "Yes"

## Best Practices

### Data Preparation
1. **Use exact enum values** from DS-160 dropdowns
2. **Provide all required fields** even if logically optional
3. **Use different values** for fields that validate uniqueness
4. **Include native script names** for applicable countries

### Error Handling
1. **Check field existence** before filling
2. **Handle dynamic field appearance** (conditional fields)
3. **Implement "Add Another" button logic** for repeater patterns
4. **Provide fallback values** for problematic fields

### Testing Strategy
1. **Test with minimal data** first
2. **Add complexity incrementally** 
3. **Verify each section** independently
4. **Test edge cases** (multiple nationalities, etc.)

## Troubleshooting

### Common Issues
1. **Selector timeouts**: Field may not exist or be conditionally hidden
2. **"Add Another" not found**: May need different selector pattern
3. **Validation errors**: Check enum values and required fields
4. **Empty field errors**: Provide fallback values

### Debug Techniques
1. **Take screenshots** at each step
2. **Log available options** in dropdowns
3. **Dump form HTML** when selectors fail
4. **Check field visibility** and enabled state

---

*Last updated: August 2025*
*Based on DS-160 form version as of 2025*