# 🌍 CIS Countries Test Cases

## Overview

Comprehensive test data files for DS-160 visa applications from major CIS (Commonwealth of Independent States) countries. Each file contains realistic data with proper national identification numbers and country-specific information.

## 📋 Available Test Cases

### 🇰🇿 Kazakhstan (`cis_kazakhstan.json`)
- **National ID Format**: ИИН (Individual Identification Number)
- **Format**: 12-digit number (YYMMDD + 6 digits)
- **Example**: `************` (Born July 15, 1985)
- **Purpose**: Business trip
- **Status**: Married

### 🇷🇺 Russia (`cis_russia.json`)
- **National ID Format**: Passport series and number / СНИЛС
- **Format**: `12 34 567890` (series + number format)
- **Purpose**: Tourism
- **Status**: Single
- **Special**: VK social media account included

### 🇺🇦 Ukraine (`cis_ukraine.json`)
- **National ID Format**: Ідентифікаційний код (Identification Code)
- **Format**: 10-digit number
- **Example**: `**********`
- **Purpose**: Business/Tech conference
- **Status**: Married

### 🇧🇾 Belarus (`cis_belarus.json`)
- **National ID Format**: Личный номер (Personal Number)
- **Format**: 14-character alphanumeric
- **Example**: `3200682A123PB4`
- **Purpose**: International trade
- **Status**: Married

### 🇺🇿 Uzbekistan (`cis_uzbekistan.json`)
- **National ID Format**: ПИНФЛ (Personal Identification Number)
- **Format**: 14-digit number
- **Example**: `**************`
- **Purpose**: Education (University program)
- **Status**: Single

## 🎯 Key Features

### Realistic Data
- **Authentic names** in local languages with Latin transliteration
- **Real city names** and addresses from each country
- **Proper phone number formats** for each country
- **Realistic occupations** and educational backgrounds

### National ID Accuracy
- **Correct formats** for each country's identification system
- **Valid date encoding** where applicable (Kazakhstan, Belarus)
- **Proper length** and character types

### Diverse Scenarios
- **Different visa purposes**: Business, Tourism, Education, Trade
- **Various marital statuses**: Single, Married
- **Multiple age groups**: From students to business professionals
- **Different backgrounds**: IT, Trade, Education, Petroleum

## 🔧 Usage Examples

### Run with Kazakhstan data:
```bash
python main.py run --data-file data/cis_kazakhstan.json --headful
```

### Run with Russia data:
```bash
python main.py run --data-file data/cis_russia.json --headful
```

### Run with Ukraine data:
```bash
python main.py run --data-file data/cis_ukraine.json --headful
```

### Validate any CIS data file:
```bash
python main.py validate --data-file data/cis_belarus.json
```

## 📊 National ID Reference

| Country | ID Name | Format | Length | Example |
|---------|---------|--------|--------|---------|
| 🇰🇿 Kazakhstan | ИИН | YYMMDD + 6 digits | 12 | `************` |
| 🇷🇺 Russia | Passport/СНИЛС | Series + Number | Variable | `12 34 567890` |
| 🇺🇦 Ukraine | Ідент. код | Numeric | 10 | `**********` |
| 🇧🇾 Belarus | Личный номер | Alphanumeric | 14 | `3200682A123PB4` |
| 🇺🇿 Uzbekistan | ПИНФЛ | Numeric | 14 | `**************` |

## 🛡️ Security & Privacy

### Data Safety
- **No real personal data** - all information is fictional
- **Realistic but fake** phone numbers, emails, and addresses
- **Generic business names** and organizations
- **Safe for testing** without privacy concerns

### Common Patterns
- All test cases default to **"No"** for security questions
- **Standard visa application** scenarios
- **No criminal history** or complications
- **Clean travel records**

## 🔄 Customization

### Modify for your needs:
1. **Change personal info** (names, dates, addresses)
2. **Update contact details** (phones, emails)
3. **Adjust travel purposes** and dates
4. **Modify work/education** background
5. **Keep national ID format** correct for the country

### Field to customize:
```json
{
  "nationalityAndResidence": {
    "countryOfOrigin": "KAZAKHSTAN",  // Keep correct
    "nationalIdentificationNumber": "************"  // Update format
  }
}
```

## 🚀 Testing Strategy

### Comprehensive Coverage
- **Test each country** individually to verify specific handling
- **Compare results** across different countries
- **Check national ID** field handling
- **Verify address formats** work correctly

### Validation Workflow
1. **Validate data file**: `python main.py validate --data-file data/cis_[country].json`
2. **Dry run test**: Add `--dry-run` flag for testing
3. **Debug mode**: Add `--headful` to see browser actions
4. **Save state**: Add `--save-state` for resumable sessions

## 📈 Success Metrics

### Expected Results
- ✅ **All fields filled** correctly for each country
- ✅ **National ID accepted** by DS-160 form
- ✅ **No validation errors** for country-specific data
- ✅ **Consistent behavior** across all CIS countries

### Common Issues
- **National ID format** rejected → Check format correctness
- **Address validation** fails → Verify city/state names
- **Phone number** issues → Check country code format
- **Date formats** → Ensure YYYY-MM-DD format

## 🔗 Related Files

- `data/example_data.json` - Original example (Kazakhstan-based)
- `data/test_case_*.json` - Other scenario-based test cases
- `src/data_loader.py` - Data validation and loading logic
- `docs/universal-section-handler.md` - Adaptive form handling

---

*All test data is fictional and created for testing purposes only. Do not use real personal information in test files.*

**Created**: 2025-08-02  
**Status**: Ready for testing  
**Countries**: 5 CIS countries covered