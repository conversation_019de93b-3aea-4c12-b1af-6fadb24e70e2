# План исправления Travel Companions

## Проблема
Бот выбирает "Yes" для "other persons traveling", но не может найти поля для заполнения companions, из-за чего получает validation errors.

## Что происходит
1. JSON данные содержат 3 travel companions в `sample_application.json`
2. Код пытается найти поля с селекторами типа:
   ```
   input[name='ctl00$SiteContentPlaceHolder$FormView1$dlTravelCompanions$ctl00$tbxSurname']
   ```
3. Поля не найдены → код truncates до 0 companions
4. Но форма показывает пустые поля и требует их заполнения
5. Результат: validation error "Please correct all areas in error"

## Логи ошибки
```
2025-08-07 10:20:22 | ERROR | ❌ Companion field ctl00 does not exist on page
2025-08-07 10:20:22 | WARNING | 💡 DS-160 may only support 0 travel companions
2025-08-07 10:20:22 | INFO | 🔄 Truncating to 0 companions and continuing...
```

## Визуальная проблема
На скриншоте `travel_companions_validation_error_attempt_1.png` видно:
- Поля "Surnames", "Given Names", "Relationship" существуют на странице
- Поля пустые и выделены красным (validation error)
- Есть кнопка "Add Another" для добавления companions

## Простое решение

### 1. Добавить универсальные селекторы
Создать множественные fallback селекторы:
```javascript
// Вместо жесткого селектора
"input[name='ctl00$SiteContentPlaceHolder$FormView1$dlTravelCompanions$ctl00$tbxSurname']"

// Добавить fallback варианты
[
  "input[name*='dlTravelCompanions'][name*='tbxSurname']",
  "input[name*='Surname']",
  "input[placeholder*='Surname']",
  "input[id*='Surname']"
]
```

### 2. Исправить truncation логику
```python
# Проблема: truncate до 0 если первое поле не найдено
if not field_exists:
    logger.info(f"🔄 Truncating to {i} companions and continuing...")
    break  # ← Это происходит на i=0

# Решение: не truncate до 0 если выбрали "Yes"
if not field_exists and i == 0:
    logger.warning("First companion field not found, trying alternative selectors")
    # Попробовать альтернативные селекторы
elif not field_exists:
    break  # Truncate только если нашли хотя бы одно поле
```

### 3. Добавить debug функцию
```python
async def debug_page_fields(page):
    """Вывести все input/select поля для debugging"""
    fields = await page.evaluate('''() => {
        const inputs = Array.from(document.querySelectorAll('input, select'));
        return inputs.map(el => ({
            name: el.name,
            id: el.id, 
            type: el.type,
            placeholder: el.placeholder
        }));
    }''')
    logger.info(f"🐛 Available fields on page: {fields}")
    return fields
```

### 4. Fallback стратегия
```python
# Если основные селекторы не работают
if not any_companion_filled:
    logger.warning("No companion fields found, switching to 'No' for other persons traveling")
    # Переключить на "No" и продолжить
    await safe_click(page, "input[value='No']")
```

### 5. Учесть ограничения DS-160
- Возможно DS-160 поддерживает только определенное количество companions
- Добавить проверку реального количества доступных полей через DOM
- Адаптировать количество companions к возможностям формы

## Конкретные шаги реализации

1. **Обновить селекторы** в `fill_travel_companions_info()`:
   - Заменить одиночные селекторы на массивы fallback селекторов
   - Добавить более общие паттерны поиска

2. **Исправить логику truncation**:
   - Не позволять truncate до 0 если выбрали "Yes"
   - Попробовать альтернативные способы поиска полей

3. **Добавить debug**:
   - Функцию для вывода всех полей страницы
   - Более детальное логирование попыток заполнения

4. **Тестирование**:
   - Запустить бот с текущими данными
   - Проверить что поля заполняются без validation errors

## Ожидаемый результат
Более гибкое заполнение полей companions с fallback стратегиями вместо жесткого поиска конкретных селекторов. Если поля не найдены - переключение на "No companions" автоматически.