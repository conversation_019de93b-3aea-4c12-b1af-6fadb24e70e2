#!/usr/bin/env python3
"""
Test script to verify the navigation fix for family handler.

This script tests the navigate_to_next_page function with various selector patterns
to ensure it can handle ASP.NET WebForm postback navigation properly.
"""

import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from utils import (
    navigate_to_next_page, 
    take_screenshot, 
    human_like_delay,
    dump_form_elements_to_console
)

async def test_navigation_patterns():
    """Test various navigation selector patterns."""
    
    async with async_playwright() as p:
        # Launch browser in headful mode for debugging
        browser = await p.chromium.launch(
            headless=False,
            args=['--start-maximized']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        page = await context.new_page()
        
        try:
            # Navigate to DS-160 start page for testing
            logger.info("🌐 Navigating to DS-160 portal...")
            await page.goto("https://ceac.state.gov/genniv/", timeout=30000)
            await human_like_delay(2, 3)
            
            # Take screenshot
            await take_screenshot(page, "navigation_test_start.png")
            
            # Test the navigation utility on current page
            logger.info("🧪 Testing navigation utility on current page...")
            
            # Dump form elements to understand structure
            await dump_form_elements_to_console(page, "DS-160 Portal Form Analysis")
            
            # Test navigation patterns (this should gracefully fail on portal page)
            nav_result = await navigate_to_next_page(
                page,
                current_section="test_portal_page",
                timeout=10000
            )
            
            logger.info(f"Navigation test result: {nav_result}")
            
            if nav_result["success"]:
                logger.info("✅ Navigation utility executed successfully")
                logger.info(f"   Method used: {nav_result['method_used']}")
                logger.info(f"   URL change: {nav_result['old_url']} → {nav_result['new_url']}")
            else:
                logger.info("⚠️ Navigation failed as expected (no valid navigation buttons on portal page)")
                logger.info(f"   Errors: {nav_result['errors']}")
            
            # Wait for manual inspection
            logger.info("🔍 Pausing for manual inspection...")
            logger.info("   Check screenshots/ directory for captured images")
            logger.info("   Press Ctrl+C to exit when done")
            
            await asyncio.sleep(10)  # Give time to inspect
            
        except Exception as e:
            logger.error(f"Test error: {e}")
            await take_screenshot(page, "navigation_test_error.png")
            
        finally:
            await browser.close()

async def test_selector_patterns():
    """Test specific DS-160 selector patterns."""
    
    # Family navigation selectors to test
    family_selectors = [
        "input[name='ctl00$ucNavigateOption$ucNavPanel$ctl01$btnNextPageComplete']",
        "input[id='ctl00_ucNavigateOption_ucNavPanel_ctl01_btnNextPageComplete']",
        "input[name*='btnNextPageComplete']",
        "input[value='Next: Family']",
        "input[value*='Next']"
    ]
    
    # Generic navigation selectors to test
    generic_selectors = [
        "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnContinue']",
        "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']",
        "input[value='Continue']",
        "input[value='Next']"
    ]
    
    logger.info("🧪 Testing selector patterns...")
    logger.info(f"Family selectors: {len(family_selectors)} patterns")
    for i, selector in enumerate(family_selectors, 1):
        logger.info(f"  {i}. {selector}")
    
    logger.info(f"Generic selectors: {len(generic_selectors)} patterns")
    for i, selector in enumerate(generic_selectors, 1):
        logger.info(f"  {i}. {selector}")
    
    # JavaScript postback patterns
    js_patterns = [
        "__doPostBack('ctl00$ucNavigateOption$ucNavPanel$ctl01$btnNextPageComplete','')",
        "__doPostBack('ctl00$SiteContentPlaceHolder$FormView1$btnContinue','')",
        "__doPostBack('ctl00$SiteContentPlaceHolder$FormView1$btnNext','')"
    ]
    
    logger.info(f"JavaScript patterns: {len(js_patterns)} patterns")
    for i, pattern in enumerate(js_patterns, 1):
        logger.info(f"  {i}. {pattern}")
    
    total_patterns = len(family_selectors) + len(generic_selectors) + len(js_patterns)
    logger.info(f"Total navigation patterns: {total_patterns}")

async def main():
    """Main test function."""
    logger.info("🚀 Starting DS-160 Navigation Fix Test")
    logger.info("=" * 60)
    
    # Test 1: Selector patterns
    await test_selector_patterns()
    
    logger.info("\n" + "=" * 60)
    
    # Test 2: Live navigation test
    try:
        await test_navigation_patterns()
    except KeyboardInterrupt:
        logger.info("👋 Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
    
    logger.info("🏁 Navigation fix test completed")
    logger.info("📸 Check screenshots/ directory for captured images")

if __name__ == "__main__":
    asyncio.run(main())