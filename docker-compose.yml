version: '3.8'

services:
  ds160-bot:
    build: .
    container_name: ds160-bot
    environment:
      - HEADLESS=true
      - LOG_LEVEL=INFO
      - DATA_FILE=/app/data/your_data.json
      - AUTO_SUBMIT=false
    volumes:
      # Mount your data file
      - ./data:/app/data:ro
      # Mount config for customization
      - ./config:/app/config:ro
      # Persist logs and screenshots
      - ./logs:/app/logs
      - ./screenshots:/app/screenshots
      - ./session_data:/app/session_data
    command: ["run", "--data-file", "/app/data/your_data.json"]
    restart: "no"
    
  # Optional: headful mode for debugging
  ds160-bot-debug:
    build: .
    container_name: ds160-bot-debug
    environment:
      - HEADLESS=false
      - LOG_LEVEL=DEBUG
      - DATA_FILE=/app/data/your_data.json
      - AUTO_SUBMIT=false
    volumes:
      - ./data:/app/data:ro
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./screenshots:/app/screenshots
      - ./session_data:/app/session_data
      # For X11 forwarding (Linux/macOS)
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${DISPLAY}
    command: ["run", "--headful", "--verbose", "--data-file", "/app/data/your_data.json"]
    restart: "no"
    profiles:
      - debug

# To run:
# docker-compose up ds160-bot          # Normal headless mode
# docker-compose --profile debug up ds160-bot-debug  # Debug mode with GUI 