#!/usr/bin/env python3
"""
Debug Former Spouse Data Access
Test script to verify former spouse data is properly loaded and accessible.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from data_loader import DataLoader

async def debug_former_spouse_data():
    """Debug former spouse data loading and access."""
    
    print("🔍 FORMER SPOUSE DATA ACCESS DEBUG")
    print("=" * 50)
    
    # Test with sample_application.json that has divorced spouse data
    data_file = "data/sample_application.json"
    print(f"📄 Loading data from: {data_file}")
    
    try:
        # Load data
        loader = DataLoader(data_file)
        data = await loader.load_data()
        print(f"✅ Data loaded successfully")
        
        # Check personal_info
        print(f"\n📋 Personal Info:")
        print(f"   Marital Status: {data.personal_info.marital_status}")
        print(f"   Attributes: {[attr for attr in dir(data.personal_info) if not attr.startswith('_')]}")
        
        # Check for divorce_info
        print(f"\n👥 Divorce Info Check:")
        if hasattr(data.personal_info, 'divorce_info'):
            divorce_info = data.personal_info.divorce_info
            if divorce_info:
                print(f"✅ Found divorce_info!")
                print(f"   Number of former spouses: {divorce_info.number_of_former_spouses}")
                print(f"   Former spouses count: {len(divorce_info.former_spouses)}")
                
                # Check each former spouse
                for i, spouse in enumerate(divorce_info.former_spouses):
                    print(f"\n🔍 Former Spouse {i+1}:")
                    print(f"   Surnames: '{spouse.surnames}'")
                    print(f"   Given Names: '{spouse.given_names}'")
                    print(f"   Date of Birth: {spouse.date_of_birth}")
                    print(f"   Date of Marriage: {spouse.date_of_marriage}")
                    print(f"   Marriage Termination Country: {spouse.marriage_termination_country}")
                    
                    # Check birth place
                    if hasattr(spouse, 'place_of_birth') and spouse.place_of_birth:
                        print(f"   Birth Place City: {spouse.place_of_birth.city}")
                        print(f"   Birth Place Country: {spouse.place_of_birth.country_region}")
                    else:
                        print(f"   Birth Place: Not found")
                    
            else:
                print(f"❌ divorce_info is None")
        else:
            print(f"❌ No 'divorce_info' attribute found")
            
        # Additional debug: Show all personal_info attributes
        print(f"\n🔧 All personal_info attributes:")
        for attr in dir(data.personal_info):
            if not attr.startswith('_'):
                try:
                    value = getattr(data.personal_info, attr)
                    print(f"   {attr}: {type(value).__name__} = {value}")
                except Exception as e:
                    print(f"   {attr}: ERROR - {e}")
                    
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_former_spouse_data())