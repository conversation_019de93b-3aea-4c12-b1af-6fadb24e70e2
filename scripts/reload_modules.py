#!/usr/bin/env python3
"""
Module reload script to force Python to reload cached modules.
Use this when developing and testing changes to ensure new code is executed.
"""

import sys
import importlib
from pathlib import Path

def force_reload_modules():
    """Force reload all DS-160 bot modules to pick up code changes."""
    
    # Modules to reload in dependency order
    modules_to_reload = [
        'src.utils',
        'src.data_loader', 
        'src.selectors',
        'src.form_handlers.family_handler',
        'src.form_filler',
        'src.bot'
    ]
    
    print("🔄 Force reloading DS-160 bot modules...")
    
    for module_name in modules_to_reload:
        if module_name in sys.modules:
            print(f"   ♻️  Reloading {module_name}")
            try:
                importlib.reload(sys.modules[module_name])
                print(f"   ✅ Successfully reloaded {module_name}")
            except Exception as e:
                print(f"   ❌ Failed to reload {module_name}: {e}")
        else:
            print(f"   ⚠️  Module {module_name} not loaded, skipping")
    
    print("✅ Module reload complete")

if __name__ == "__main__":
    # Add project root to path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))
    
    force_reload_modules()