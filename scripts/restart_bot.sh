#!/bin/bash

# DS-160 Bot Restart Script
# Forces module reload and restarts bot to ensure new code is executed

echo "🛑 Stopping any running DS-160 bot processes..."
pkill -f "python.*main.py.*run" || echo "   No running bot processes found"

echo "🔄 Clearing Python cache..."
find /Users/<USER>/Apps/visa-ds160 -name "*.pyc" -delete
find /Users/<USER>/Apps/visa-ds160 -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

echo "♻️  Reloading modules..."
cd /Users/<USER>/Apps/visa-ds160
python scripts/reload_modules.py

echo "🚀 Restarting bot with --resume..."
python main.py run --resume --headful --data-file data/test_divorced_spouse.json

echo "✅ Bot restart complete"