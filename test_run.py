#!/usr/bin/env python3
"""Test script for DS-160 bot with automatic acceptance of legal warning."""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import get_config
from src.bot import DS160Bot
from loguru import logger


async def main():
    """Test the improved CAPTCHA and button detection logic."""
    logger.info("🤖 Starting DS-160 Bot Test with Enhanced CAPTCHA Logic...")
    
    try:
        # Load configuration
        config = get_config()
        
        # Override for testing
        config.browser.headless = False  # Show browser
        config.logging.level = "DEBUG"   # Verbose logging
        config.security.auto_submit = False  # Dry run
        config.data_file = "data/my_comprehensive_data.json"
        
        logger.info("⚙️  Configuration loaded:")
        logger.info(f"  Browser: {config.browser.browser_type} (headless={config.browser.headless})")
        logger.info(f"  Data file: {config.data_file}")
        logger.info(f"  Auto-submit: {config.security.auto_submit}")
        
        # Run the bot
        async with DS160Bot(config) as bot:
            logger.info("🚀 Starting bot execution...")
            success = await bot.run(config.data_file)
            
            if success:
                logger.info("✅ Test completed successfully!")
                logger.info("📋 Check screenshots/ directory for debugging images")
                return True
            else:
                logger.error("❌ Test failed")
                logger.info("🔍 Keeping browser open for 60 seconds to inspect error...")
                await asyncio.sleep(60)  # Keep browser open for inspection
                return False
                
    except KeyboardInterrupt:
        logger.info("\n🛑 Test interrupted by user")
        return False
    except Exception as e:
        logger.error(f"💥 Test crashed: {e}")
        logger.info("🔍 Keeping browser open for 60 seconds to inspect...")
        await asyncio.sleep(60)  # Keep browser open for inspection
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)