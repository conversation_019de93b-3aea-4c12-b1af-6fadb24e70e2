#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import json
import re
from datetime import datetime
import os
import csv

class RobustDS160Converter:
    def __init__(self, column_offset=0, debug_mode=False):
        self.column_offset = column_offset
        self.debug_mode = debug_mode
        
        # Маппинг семейного статуса
        self.marital_status_map = {
            'Женат/Замужем': 'MARRIED',
            'Холост/Не замужем': 'SINGLE',
            'Вдовец/Вдова': 'WIDOWED',
            'Разведён/Разведена': 'DIVORCED',
            'Гражданский брак': 'DOMESTIC_PARTNERSHIP',
            'Помолвлен/Помолвлена': 'ENGAGED'
        }
        
        # Маппинг пола
        self.gender_map = {
            'Мужской': 'MALE',
            'Женский': 'FEMALE'
        }
        
        # Маппинг спонсоров
        self.sponsor_map = {
            'Я сам/а': 'Self',
            'Другой человек': 'Other Person',
            'Работодатель': 'Employer',
            'Работодатель в США': 'US Employer',
            'Другая компания/организация': 'Other Company/Organization'
        }

    def clean_csv_file(self, csv_file_path):
        """Очищает CSV файл от проблемных символов и кавычек"""
        print("Очистка CSV файла...")
        
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Исправляем проблемы с кавычками
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Убираем проблемные символы
            line = line.replace('\r', '')
            
            # Исправляем незакрытые кавычки
            if line.count('"') % 2 != 0:
                line += '"'
            
            # Заменяем переносы строк внутри полей на пробелы
            line = re.sub(r'(?<!")(\n|\r)(?!")', ' ', line)
            
            cleaned_lines.append(line)
        
        # Сохраняем очищенный файл
        cleaned_path = csv_file_path.replace('.csv', '_cleaned.csv')
        with open(cleaned_path, 'w', encoding='utf-8', newline='') as file:
            file.write('\n'.join(cleaned_lines))
        
        return cleaned_path

    def parse_csv_robust(self, csv_file_path):
        """Надежный парсинг CSV файла"""
        try:
            # Сначала очищаем файл
            cleaned_path = self.clean_csv_file(csv_file_path)
            
            # Читаем файл построчно
            with open(cleaned_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
            
            # Находим заголовки
            header_line = None
            header_index = -1
            
            for i, line in enumerate(lines):
                if 'Отметка времени' in line:
                    header_line = line.strip()
                    header_index = i
                    break
            
            if header_line is None:
                raise ValueError("Не найдена строка с заголовками")
            
            # Парсим заголовки
            headers = self.parse_csv_line(header_line)
            
            # Находим строки с данными
            data_rows = []
            for i in range(header_index + 1, len(lines)):
                line = lines[i].strip()
                if line and not line.startswith(';;;;') and len(line) > 10:
                    try:
                        parsed_row = self.parse_csv_line(line)
                        if len(parsed_row) > 5:  # Минимальное количество полей
                            data_rows.append(parsed_row)
                    except Exception as e:
                        print(f"Ошибка парсинга строки {i}: {e}")
                        continue
            
            return headers, data_rows
            
        except Exception as e:
            print(f"Ошибка при парсинге CSV: {e}")
            return None, None

    def parse_csv_line(self, line):
        """Парсит одну строку CSV"""
        # Используем встроенный csv reader для надежности
        import io
        reader = csv.reader(io.StringIO(line), delimiter=';', quotechar='"')
        try:
            return next(reader)
        except:
            # Если стандартный парсер не работает, используем простое разделение
            return line.split(';')

    def safe_get_by_index(self, row, index, default=None):
        """Безопасно получает значение по индексу"""
        try:
            if 0 <= index < len(row):
                value = row[index]
                if value is None or str(value).strip() == '':
                    return default
                return str(value).strip()
            return default
        except:
            return default

    def find_field_index(self, headers, field_pattern):
        """Находит индекс поля по паттерну"""
        for i, header in enumerate(headers):
            if header and field_pattern in str(header):
                return i
        return -1

    def convert_date(self, date_str):
        """Конвертирует дату из формата DD.MM.YYYY в YYYY-MM-DD"""
        if not date_str or pd.isna(date_str):
            return None
        
        try:
            date_str = str(date_str).strip()
            
            if '.' in date_str:
                parts = date_str.split('.')
                if len(parts) == 3:
                    day, month, year = parts
                    day = day.zfill(2)
                    month = month.zfill(2)
                    if len(year) == 2:
                        year = '20' + year if int(year) < 50 else '19' + year
                    return f"{year}-{month}-{day}"
            
            if len(date_str) == 10 and date_str.count('-') == 2:
                return date_str
                
        except:
            pass
        
        return None

    def transliterate_text(self, text):
        """Переводит кириллицу в латиницу"""
        if not text or pd.isna(text):
            return None
        
        cyrillic_to_latin = {
            'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'YO',
            'Ж': 'ZH', 'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M',
            'Н': 'N', 'О': 'O', 'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U',
            'Ф': 'F', 'Х': 'KH', 'Ц': 'TS', 'Ч': 'CH', 'Ш': 'SH', 'Щ': 'SHCH',
            'Ъ': '', 'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'YU', 'Я': 'YA',
            'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo',
            'ж': 'zh', 'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm',
            'н': 'n', 'о': 'o', 'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u',
            'ф': 'f', 'х': 'kh', 'ц': 'ts', 'ч': 'ch', 'ш': 'sh', 'щ': 'shch',
            'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
        }
        
        result = ''
        for char in str(text):
            result += cyrillic_to_latin.get(char, char)
        return result.upper()

    def parse_address(self, address_str):
        """Парсит адрес на компоненты"""
        if not address_str or pd.isna(address_str):
            return {
                "streetLine1": None,
                "city": None,
                "stateProvince": None,
                "postalZoneZipCode": None,
                "countryRegion": "RUSSIA"
            }
        
        parts = str(address_str).split(',')
        street = self.transliterate_text(parts[0].strip()) if len(parts) > 0 else None
        city = self.transliterate_text(parts[1].strip()) if len(parts) > 1 else None
        region = self.transliterate_text(parts[2].strip()) if len(parts) > 2 else None
        
        return {
            "streetLine1": street,
            "city": city,
            "stateProvince": region,
            "postalZoneZipCode": None,
            "countryRegion": "RUSSIA"
        }

    def convert_row_to_ds160(self, headers, row, index):
        """Конвертирует строку CSV в формат DS-160"""
        
        # Находим индексы ключевых полей
        email_idx = self.find_field_index(headers, 'Адрес электронной почты')
        surname_idx = self.find_field_index(headers, '1.1 Фамилия')
        name_idx = self.find_field_index(headers, '1.2 Имя на английском')
        full_name_idx = self.find_field_index(headers, '1.3 ФИО полное на родном языке')
        gender_idx = self.find_field_index(headers, '1.5 Пол')
        marital_idx = self.find_field_index(headers, '1.6 Семейный статус')
        birth_date_idx = self.find_field_index(headers, '1.7 Дата рождения')
        birth_place_idx = self.find_field_index(headers, '1.8 Город/село, область, страна рождения')
        citizenship_idx = self.find_field_index(headers, '1.9 Гражданином какой страны')
        inn_idx = self.find_field_index(headers, '1.12 Укажите ваш национальный идентификационный номер')
        
        # Контактная информация
        phone_idx = self.find_field_index(headers, '3.3')
        address_idx = self.find_field_index(headers, '3.1 Адрес фактического проживания')
        
        # Паспорт
        passport_idx = self.find_field_index(headers, '4.1 Номер паспорта')
        passport_country_idx = self.find_field_index(headers, '4.2 Страна выдачи паспорта')
        passport_issue_idx = self.find_field_index(headers, '4.4 Дата выдачи паспорта')
        passport_expiry_idx = self.find_field_index(headers, '4.5 Дата окончания срока действия паспорта')
        
        # Семья
        father_idx = self.find_field_index(headers, '6.1 Фамилия и Имя вашего отца')
        father_birth_idx = self.find_field_index(headers, '6.2 Дата рождения отца')
        mother_idx = self.find_field_index(headers, '6.4 Фамилия и имя вашей мамы')
        mother_birth_idx = self.find_field_index(headers, '6.5 Дата рождения вашей мамы')
        
        # Работа
        occupation_idx = self.find_field_index(headers, '7.1 Ваша сфера деятельности')
        employer_idx = self.find_field_index(headers, '7.2')
        work_address_idx = self.find_field_index(headers, '7.3 Адрес компании-работодателя')
        work_start_idx = self.find_field_index(headers, '7.5 Дата начала работы')
        salary_idx = self.find_field_index(headers, '7.6')
        position_idx = self.find_field_index(headers, '7.7 Какая у вас должность')
        duties_idx = self.find_field_index(headers, '7.8 Подробно опишите ваши обязанности')
        
        # Образование
        edu1_name_idx = self.find_field_index(headers, '9.1 Название первого учебного заведения')
        edu1_address_idx = self.find_field_index(headers, '9.2 Адрес первого учебного заведения')
        edu1_specialty_idx = self.find_field_index(headers, '9.3 На кого Вы учились')
        edu1_start_idx = self.find_field_index(headers, '9.4 Дата поступления')
        edu1_end_idx = self.find_field_index(headers, '9.5 Дата окончания учебы')
        
        # Языки
        languages_idx = self.find_field_index(headers, '10.1 Какими языками вы владеете')
        
        # Получаем данные
        surnames = self.safe_get_by_index(row, surname_idx)
        given_names = self.safe_get_by_index(row, name_idx)
        full_name_native = self.safe_get_by_index(row, full_name_idx)
        email = self.safe_get_by_index(row, email_idx)
        
        # Если нет основных данных, пропускаем
        if not surnames and not given_names and not email:
            return None
        
        birth_date = self.convert_date(self.safe_get_by_index(row, birth_date_idx))
        
        # Место рождения
        birth_place_raw = self.safe_get_by_index(row, birth_place_idx, '')
        birth_place_parts = birth_place_raw.split('\n') if birth_place_raw else ['', '', '']
        
        # Семейная информация
        father_name = self.safe_get_by_index(row, father_idx)
        father_birth_date = self.convert_date(self.safe_get_by_index(row, father_birth_idx))
        mother_name = self.safe_get_by_index(row, mother_idx)
        mother_birth_date = self.convert_date(self.safe_get_by_index(row, mother_birth_idx))
        
        # Рабочая информация
        occupation = self.transliterate_text(self.safe_get_by_index(row, occupation_idx))
        employer = self.transliterate_text(self.safe_get_by_index(row, employer_idx))
        work_start_date = self.convert_date(self.safe_get_by_index(row, work_start_idx))
        salary = self.safe_get_by_index(row, salary_idx)
        position = self.transliterate_text(self.safe_get_by_index(row, position_idx))
        duties = self.transliterate_text(self.safe_get_by_index(row, duties_idx))
        
        # Образование
        education1_name = self.transliterate_text(self.safe_get_by_index(row, edu1_name_idx))
        education1_specialty = self.transliterate_text(self.safe_get_by_index(row, edu1_specialty_idx))
        education1_start = self.convert_date(self.safe_get_by_index(row, edu1_start_idx))
        education1_end = self.convert_date(self.safe_get_by_index(row, edu1_end_idx))
        
        # Языки
        languages_raw = self.safe_get_by_index(row, languages_idx)
        languages = []
        if languages_raw:
            if 'русский' in languages_raw.lower() or 'russian' in languages_raw.lower():
                languages.append('RUSSIAN')
            if 'чечен' in languages_raw.lower() or 'chechen' in languages_raw.lower():
                languages.append('CHECHEN')
            if 'английский' in languages_raw.lower() or 'english' in languages_raw.lower():
                languages.append('ENGLISH')

        # Создаем JSON структуру
        ds160_data = {
            "applicationInfo": {
                "formType": "DS-160",
                "applicationId": f"AA00{surnames[:6] if surnames else 'UNKNOW'}{index:02d}"
            },
            "personalInfo": {
                "surnames": surnames.upper() if surnames else None,
                "givenNames": given_names.upper() if given_names else None,
                "fullNameNativeAlphabet": full_name_native,
                "telecodeRepresentsName": "No",
                "sex": self.gender_map.get(self.safe_get_by_index(row, gender_idx), "UNKNOWN"),
                "maritalStatus": self.marital_status_map.get(self.safe_get_by_index(row, marital_idx), "SINGLE"),
                "dateOfBirth": birth_date,
                "placeOfBirth": {
                    "city": self.transliterate_text(birth_place_parts[0]) if len(birth_place_parts) > 0 else None,
                    "stateProvince": self.transliterate_text(birth_place_parts[1]) if len(birth_place_parts) > 1 else None,
                    "countryRegion": "RUSSIA"
                },
                "spouse": {
                    "surnames": None,
                    "givenNames": None,
                    "dateOfBirth": None,
                    "placeOfBirth": {
                        "city": None,
                        "stateProvince": None,
                        "countryRegion": None
                    },
                    "nationality": None,
                    "addressType": None
                }
            },
            "nationalityAndResidence": {
                "countryOfOrigin": "RUSSIA",
                "isPermanentResidentOfOtherCountry": "No",
                "permanentResidentCountries": [],
                "nationalIdentificationNumber": self.safe_get_by_index(row, inn_idx),
                "usSocialSecurityNumber": None,
                "usTaxpayerIdNumber": None
            },
            "passportInfo": {
                "passportType": "REGULAR",
                "passportNumber": self.safe_get_by_index(row, passport_idx),
                "passportBookNumber": None,
                "issuingCountry": "RUSSIA",
                "issuingCity": None,
                "issuingStateProvince": None,
                "issuanceDate": self.convert_date(self.safe_get_by_index(row, passport_issue_idx)),
                "expirationDate": self.convert_date(self.safe_get_by_index(row, passport_expiry_idx))
            },
            "contactInfo": {
                "mailingAddress": self.parse_address(self.safe_get_by_index(row, address_idx)),
                "phoneNumbers": {
                    "primary": self.safe_get_by_index(row, phone_idx)
                },
                "emailAddresses": {
                    "primary": email,
                    "additional": None
                },
                "socialMedia": []
            },
            "travelInfo": {
                "purposeOfTrip": "TEMP. BUSINESS OR PLEASURE VISITOR (B)",
                "visaClass": "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)",
                "hasSpecificTravelPlans": "No",
                "arrivalDate": None,
                "arrivalFlight": None,
                "arrivalCity": None,
                "departureDate": None,
                "departureFlight": None,
                "departureCity": None,
                "locationsToVisitInUS": [],
                "usStayAddress": {
                    "streetLine1": None,
                    "city": None,
                    "state": None,
                    "zipCode": None
                },
                "personEntityPaying": "Other Person",
                "sponsorInfo": {
                    "personSponsor": {
                        "surnames": None,
                        "givenNames": None,
                        "address": {
                            "streetLine1": None,
                            "city": None,
                            "stateProvince": None,
                            "postalZoneZipCode": None,
                            "countryRegion": "RUSSIA"
                        },
                        "phone": None,
                        "email": None,
                        "relationship": "FAMILY MEMBER"
                    }
                }
            },
            "travelCompanions": {
                "areOtherPersonsTraveling": "No",
                "personsTraveling": [],
                "isTravelingAsPartOfGroup": "No",
                "groupName": None
            },
            "previousUSTravel": {
                "hasEverBeenInUS": "No",
                "previousVisits": [],
                "hasEverHeldUSLicense": "No",
                "hasEverBeenIssuedUSVisa": "No",
                "previousVisaInfo": {
                    "dateLastVisaIssued": None,
                    "visaNumber": None,
                    "isApplyingForSameType": None,
                    "isApplyingInSameCountry": None,
                    "hasBeenTenPrinted": None,
                    "hasVisaBeenLostOrStolen": {
                        "answer": "No",
                        "yearLost": None,
                        "explanation": None
                    },
                    "hasVisaBeenCancelledOrRevoked": {
                        "answer": "No",
                        "explanation": None
                    }
                },
                "hasEverBeenRefusedAdmissionOrVisa": {
                    "answer": "No",
                    "explanation": None
                },
                "hasImmigrantPetitionBeenFiled": {
                    "answer": "No",
                    "explanation": None
                }
            },
            "usContact": {
                "contactPersonSurnames": None,
                "contactPersonGivenNames": None,
                "organizationName": None,
                "relationshipToYou": None,
                "address": {
                    "streetLine1": None,
                    "city": None,
                    "state": None,
                    "zipCode": None
                },
                "phone": None,
                "email": None
            },
            "familyInfo": {
                "father": {
                    "surnames": father_name.split()[0] if father_name and len(father_name.split()) > 0 else None,
                    "givenNames": ' '.join(father_name.split()[1:]) if father_name and len(father_name.split()) > 1 else None,
                    "dateOfBirth": father_birth_date,
                    "isInTheUS": "No",
                    "status": "NOT IN U.S."
                },
                "mother": {
                    "surnames": mother_name.split()[0] if mother_name and len(mother_name.split()) > 0 else None,
                    "givenNames": ' '.join(mother_name.split()[1:]) if mother_name and len(mother_name.split()) > 1 else None,
                    "dateOfBirth": mother_birth_date,
                    "isInTheUS": "No",
                    "status": "NOT IN U.S."
                },
                "hasImmediateRelativesInUS": "No",
                "immediateRelatives": []
            },
            "workAndEducation": {
                "present": {
                    "primaryOccupation": occupation,
                    "employerOrSchoolName": employer,
                    "address": self.parse_address(self.safe_get_by_index(row, work_address_idx)),
                    "startDate": work_start_date,
                    "monthlyIncomeLocal": salary,
                    "duties": duties
                },
                "previousWork": [],
                "previousEducation": [
                    {
                        "institutionName": education1_name,
                        "address": self.parse_address(self.safe_get_by_index(row, edu1_address_idx)),
                        "courseOfStudy": education1_specialty,
                        "dateOfAttendanceFrom": education1_start,
                        "dateOfAttendanceTo": education1_end
                    }
                ] if education1_name else [],
                "additionalInfo": {
                    "clanOrTribeName": None,
                    "languagesSpoken": languages,
                    "countriesVisitedLastFiveYears": [],
                    "charitableOrganizationsWorkedFor": {
                        "answer": "No",
                        "organizationName": None
                    },
                    "specializedSkills": {
                        "answer": "Yes" if occupation else "No",
                        "explanation": occupation
                    },
                    "hasServedInMilitary": {
                        "answer": "No",
                        "country": None,
                        "branchOfService": None,
                        "rankPosition": None,
                        "militarySpecialty": None,
                        "dateOfServiceFrom": None,
                        "dateOfServiceTo": None
                    },
                    "hasBeenInRebelGroup": {
                        "answer": "No",
                        "explanation": None
                    }
                }
            },
            "securityAndBackground": {
                "part1MedicalAndHealth": {
                    "hasCommunicableDisease": {"answer": "No", "explanation": ""},
                    "hasMentalOrPhysicalDisorder": {"answer": "No", "explanation": ""},
                    "isDrugAbuserOrAddict": {"answer": "No", "explanation": ""}
                },
                "part2Criminal": {
                    "hasBeenArrested": {"answer": "No", "explanation": ""},
                    "hasViolatedControlledSubstanceLaws": {"answer": "No", "explanation": ""},
                    "engagedInProstitution": {"answer": "No", "explanation": ""},
                    "engagedInMoneyLaundering": {"answer": "No", "explanation": ""},
                    "committedHumanTrafficking": {"answer": "No", "explanation": ""},
                    "aidedHumanTrafficking": {"answer": "No", "explanation": ""},
                    "isFamilyOfHumanTrafficker": {"answer": "No", "explanation": ""}
                },
                "part3Security": {
                    "engagedInEspionageOrSabotage": {"answer": "No", "explanation": ""},
                    "engagedInTerroristActivities": {"answer": "No", "explanation": ""},
                    "providedSupportToTerrorists": {"answer": "No", "explanation": ""},
                    "isMemberOfTerroristOrganization": {"answer": "No", "explanation": ""},
                    "participatedInGenocide": {"answer": "No", "explanation": ""},
                    "participatedInTorture": {"answer": "No", "explanation": ""},
                    "participatedInExtrajudicialKillings": {"answer": "No", "explanation": ""},
                    "violatedReligiousFreedoms": {"answer": "No", "explanation": ""},
                    "involvedInForcedPopulationControl": {"answer": "No", "explanation": ""},
                    "involvedInCoerciveOrganTransplantation": {"answer": "No", "explanation": ""}
                },
                "part4ImmigrationViolations": {
                    "soughtVisaByFraud": {"answer": "No", "explanation": ""},
                    "failedToAttendRemovalHearings": {"answer": "No", "explanation": ""},
                    "beenUnlawfullyPresentOrOverstayed": {"answer": "No", "explanation": ""},
                    "beenRemovedOrDeported": {"answer": "No", "explanation": ""}
                },
                "part5Miscellaneous": {
                    "withheldCustodyOfUSCitizenChild": {"answer": "No", "explanation": ""},
                    "votedInUSIllegally": {"answer": "No", "explanation": ""},
                    "renouncedUSCitizenshipForTax": {"answer": "No", "explanation": ""},
                    "attendedPublicSchoolOnStudentVisaWithoutReimbursement": {"answer": "No", "explanation": ""}
                }
            }
        }
        
        return ds160_data

    def process_csv_file(self, csv_file_path, output_dir='output'):
        """Обрабатывает CSV файл и создает JSON файлы для каждого заявителя"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            print(f"Обработка файла: {csv_file_path}")
            
            # Надежный парсинг CSV
            headers, data_rows = self.parse_csv_robust(csv_file_path)
            
            if headers is None or data_rows is None:
                print("Ошибка: Не удалось прочитать CSV файл")
                return
            
            print(f"Найдено {len(data_rows)} записей для обработки")
            
            successful_conversions = 0
            
            # Обрабатываем каждую запись
            for index, row in enumerate(data_rows):
                try:
                    if self.debug_mode:
                        print(f"\nОбработка записи {index + 1}...")
                        print(f"Количество полей в строке: {len(row)}")
                        print(f"Количество заголовков: {len(headers)}")
                    
                    # Конвертируем в DS-160 формат
                    ds160_data = self.convert_row_to_ds160(headers, row, index + 1)
                    
                    if ds160_data is None:
                        print(f"Пропуск записи {index + 1}: недостаточно данных")
                        continue
                    
                    # Создаем имя файла
                    surnames = ds160_data['personalInfo']['surnames'] or 'UNKNOWN'
                    given_names = ds160_data['personalInfo']['givenNames'] or 'UNKNOWN'
                    filename = f"{surnames}_{given_names}_DS160.json"
                    filename = re.sub(r'[^\w\-_\.]', '_', filename)
                    
                    output_path = os.path.join(output_dir, filename)
                    
                    # Сохраняем JSON файл
                    with open(output_path, 'w', encoding='utf-8') as json_file:
                        json.dump(ds160_data, json_file, ensure_ascii=False, indent=2)
                    
                    successful_conversions += 1
                    print(f"✓ Создан файл: {output_path}")
                    
                except Exception as e:
                    print(f"✗ Ошибка при обработке записи {index + 1}: {str(e)}")
                    if self.debug_mode:
                        import traceback
                        traceback.print_exc()
                    continue
            
            print(f"\nОбработка завершена!")
            print(f"Успешно обработано: {successful_conversions} из {len(data_rows)} записей")
            print(f"Файлы сохранены в директории: {output_dir}")
            
        except Exception as e:
            print(f"Критическая ошибка при обработке файла: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """Главная функция для запуска конвертера"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Улучшенный конвертер CSV файлов визовых анкет в JSON формат DS-160')
    parser.add_argument('csv_file', help='Путь к CSV файлу с данными анкет')
    parser.add_argument('-o', '--output', default='output', help='Директория для выходных JSON файлов')
    parser.add_argument('-d', '--debug', action='store_true', help='Режим отладки')
    parser.add_argument('--offset', type=int, default=0, help='Сдвиг столбцов (если данные сдвинуты)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.csv_file):
        print(f"Ошибка: Файл {args.csv_file} не найден")
        return
    
    # Создаем конвертер и обрабатываем файл
    converter = RobustDS160Converter(column_offset=args.offset, debug_mode=args.debug)
    
    try:
        converter.process_csv_file(args.csv_file, args.output)
    except Exception as e:
        print(f"Ошибка при конвертации: {str(e)}")


def convert_csv_robust(csv_file_path, output_dir='output', debug=False):
    """Упрощенная функция для конвертации CSV файла"""
    converter = RobustDS160Converter(debug_mode=debug)
    converter.process_csv_file(csv_file_path, output_dir)


if __name__ == "__main__":
    main()


# Пример использования:
# python robust_csv_parser.py csv/visa.csv -o parsed --debug
# 
# Или из кода:
# from robust_csv_parser import convert_csv_robust
# convert_csv_robust('csv/visa.csv', 'output_directory', debug=True)