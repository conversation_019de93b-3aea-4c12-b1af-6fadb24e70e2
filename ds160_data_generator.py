#!/usr/bin/env python3
"""
DS-160 Data Generator with Full IDE Support

This module provides a type-safe Python interface for building DS-160 form data
with comprehensive IDE type hints and autocompletion. After defining your data
structure, call generate_json() to export to the format expected by the bot.

Usage:
    from ds160_data_generator import DS160DataBuilder
    
    builder = DS160DataBuilder()
    
    # Configure with full IDE support and type hints
    builder.personal_info.surnames = "SMITH"
    builder.personal_info.given_names = "JOHN MICHAEL"
    builder.personal_info.sex = Gender.MALE
    # ... etc
    
    # Export to JSON for the bot
    json_data = builder.generate_json()
    
    # Save to file
    builder.save_json("data/my_application.json")
"""

import json
from datetime import date, datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass, field
from enum import Enum

# Re-export enums for convenience
class Gender(str, Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"

class MaritalStatus(str, Enum):
    """Marital status with proper DS-160 codes."""
    SINGLE = "S"
    MARRIED = "M" 
    DIVORCED = "D"
    WIDOWED = "W"
    LEGALLY_SEPARATED = "L"
    COMMON_LAW = "C"
    CIVIL_UNION = "P"
    OTHER = "O"

class AddressType(str, Enum):
    """Address type options for DS-160 forms."""
    HOME = "H"
    MAILING = "M"
    US_CONTACT = "U"
    DO_NOT_KNOW = "D"
    OTHER = "O"

class PrimaryOccupation(str, Enum):
    """Primary occupation categories available in DS-160."""
    AGRICULTURE = "A"
    ARTIST_PERFORMER = "AP"
    BUSINESS = "B"
    COMMUNICATIONS = "CM"
    COMPUTER_SCIENCE = "CS"
    CULINARY_FOOD_SERVICES = "C"
    EDUCATION = "ED"
    ENGINEERING = "EN"
    GOVERNMENT = "G"
    HOMEMAKER = "H"
    LEGAL_PROFESSION = "LP"
    MEDICAL_HEALTH = "MH"
    MILITARY = "M"
    NATURAL_SCIENCE = "NS"
    NOT_EMPLOYED = "N"
    PHYSICAL_SCIENCES = "PS"
    RELIGIOUS_VOCATION = "RV"
    RESEARCH = "R"
    RETIRED = "RT"
    SOCIAL_SCIENCE = "SS"
    STUDENT = "S"
    OTHER = "O"

class VisaPurpose(str, Enum):
    BUSINESS_TOURISM = "TEMP. BUSINESS OR PLEASURE VISITOR (B)"
    STUDY = "STUDENT (F)"
    WORK = "TEMPORARY WORKER (H)"
    TRANSIT = "TRANSIT (C)"
    OTHER = "OTHER"

# Builder Data Classes with Full Type Hints
@dataclass
class ApplicationInfoBuilder:
    """Application information with IDE support."""
    form_type: str = "DS-160"
    application_id: Optional[str] = None
    application_action: str = "new"
    retrieve_surname: Optional[str] = None
    retrieve_year_of_birth: Optional[int] = None
    retrieve_security_answer: Optional[str] = None

@dataclass
class PlaceOfBirthBuilder:
    """Place of birth with IDE support."""
    city: str = ""
    state_province: Optional[str] = None
    country_region: str = ""

@dataclass
class OtherNamesUsedBuilder:
    """Other names used with IDE support."""
    other_surnames: str = ""
    other_given_names: str = ""

@dataclass
class SpouseInfoBuilder:
    """Spouse information with IDE support."""
    surnames: str = ""
    given_names: str = ""
    date_of_birth: Optional[date] = None
    place_of_birth: Optional[PlaceOfBirthBuilder] = None
    nationality: str = ""
    address_type: AddressType = AddressType.HOME
    address: Optional['AddressBuilder'] = None

@dataclass
class DeceasedSpouseInfoBuilder:
    """Deceased spouse information for WIDOWED status."""
    surnames: Optional[str] = None
    given_names: Optional[str] = None
    date_of_birth: Optional[date] = None
    nationality: Optional[str] = None
    place_of_birth_city: Optional[str] = None
    place_of_birth_city_unknown: bool = False
    place_of_birth_country: Optional[str] = None

@dataclass
class FormerSpouseInfoBuilder:
    """Former spouse information."""
    surnames: str = ""
    given_names: str = ""
    date_of_birth: Optional[date] = None
    nationality: str = ""
    date_of_marriage: Optional[date] = None
    date_marriage_ended: Optional[date] = None
    how_marriage_ended: str = ""
    country_marriage_terminated: str = ""

@dataclass
class DivorceInfoBuilder:
    """Divorce information for DIVORCED status."""
    number_of_former_spouses: int = 0
    former_spouses: List[FormerSpouseInfoBuilder] = field(default_factory=list)

@dataclass
class PersonalInfoBuilder:
    """Personal information with full IDE support."""
    surnames: str = ""
    given_names: str = ""
    full_name_native_alphabet: Optional[str] = None
    other_names_used: List[OtherNamesUsedBuilder] = field(default_factory=list)
    telecode_represents_name: str = "No"
    sex: Optional[Gender] = None
    marital_status: Optional[MaritalStatus] = None
    marital_status_other_explanation: Optional[str] = None
    date_of_birth: Optional[date] = None
    place_of_birth: Optional[PlaceOfBirthBuilder] = None
    spouse: Optional[SpouseInfoBuilder] = None
    deceased_spouse: Optional[DeceasedSpouseInfoBuilder] = None
    divorce_info: Optional[DivorceInfoBuilder] = None

@dataclass
class OtherNationalityBuilder:
    """Other nationality information."""
    country: str = ""
    has_passport: str = "No"
    passport_number: Optional[str] = None

@dataclass
class NationalityAndResidenceBuilder:
    """Nationality and residence with IDE support."""
    country_of_origin: str = ""
    other_nationalities: List[OtherNationalityBuilder] = field(default_factory=list)
    is_permanent_resident_of_other_country: str = "No"
    permanent_resident_countries: List[str] = field(default_factory=list)
    national_identification_number: Optional[str] = None
    us_social_security_number: Optional[str] = None
    us_taxpayer_id_number: Optional[str] = None

@dataclass
class LostPassportInfoBuilder:
    """Lost passport information."""
    answer: str = "No"
    lost_passport_number: Optional[str] = None
    lost_passport_issuing_country: Optional[str] = None
    explanation: Optional[str] = None

@dataclass
class PassportInfoBuilder:
    """Passport information with IDE support."""
    passport_type: str = "REGULAR"
    passport_number: str = ""
    passport_book_number: Optional[str] = None
    issuing_country: str = ""
    issuing_city: str = ""
    issuing_state_province: str = ""
    issuance_date: Optional[date] = None
    expiration_date: Optional[date] = None
    has_lost_or_stolen_passport: Optional[LostPassportInfoBuilder] = None

@dataclass
class AddressBuilder:
    """Address information with IDE support."""
    street_line1: str = ""
    street_line2: Optional[str] = None
    city: str = ""
    state_province: Optional[str] = None
    postal_zone_zip_code: str = ""
    country_region: str = ""

@dataclass
class USAddressBuilder:
    """US address information."""
    street_line1: str = ""
    city: str = ""
    state: str = ""
    zip_code: str = ""

@dataclass
class PhoneNumbersBuilder:
    """Phone numbers with IDE support."""
    primary: str = ""
    secondary: Optional[str] = None
    work: Optional[str] = None
    other: Optional[str] = None

@dataclass
class EmailAddressesBuilder:
    """Email addresses with IDE support."""
    primary: str = ""
    additional: Optional[str] = None

@dataclass
class SocialMediaBuilder:
    """Social media information."""
    platform: str = ""
    identifier: str = ""

@dataclass
class ContactInfoBuilder:
    """Contact information with IDE support."""
    mailing_address: Optional[AddressBuilder] = None
    phone_numbers: Optional[PhoneNumbersBuilder] = None
    email_addresses: Optional[EmailAddressesBuilder] = None
    social_media: List[SocialMediaBuilder] = field(default_factory=list)

@dataclass
class TravelCompanionBuilder:
    """Travel companion information."""
    surnames: str = ""
    given_names: str = ""
    relationship: str = ""

@dataclass
class TravelCompanionsBuilder:
    """Travel companions information."""
    are_other_persons_traveling: str = "No"
    persons_traveling: List[TravelCompanionBuilder] = field(default_factory=list)
    is_traveling_as_part_of_group: str = "No"
    group_name: Optional[str] = None

@dataclass
class TravelInfoBuilder:
    """Travel information with IDE support."""
    purpose_of_trip: str = ""
    visa_class: str = ""
    has_specific_travel_plans: str = "Yes"
    arrival_date: Optional[date] = None
    arrival_flight: Optional[str] = None
    arrival_city: str = ""
    departure_date: Optional[date] = None
    departure_flight: Optional[str] = None
    departure_city: Optional[str] = None
    locations_to_visit_in_us: List[str] = field(default_factory=list)
    us_stay_address: Optional[USAddressBuilder] = None
    person_entity_paying: str = ""

@dataclass
class PreviousVisitBuilder:
    """Previous US visit information."""
    date_arrived: Optional[date] = None
    length_of_stay: str = ""
    unit_of_stay: str = ""

@dataclass
class DriversLicenseBuilder:
    """US drivers license information."""
    license_number: str = ""
    issuing_state: str = ""

@dataclass
class VisaLostOrStolenBuilder:
    """Visa lost or stolen information."""
    answer: str = "No"
    year_lost: Optional[str] = None
    explanation: Optional[str] = None

@dataclass
class VisaCancelledOrRevokedBuilder:
    """Visa cancelled or revoked information."""
    answer: str = "No"
    explanation: Optional[str] = None

@dataclass
class PreviousVisaInfoBuilder:
    """Previous US visa information."""
    date_last_visa_issued: Optional[date] = None
    visa_number: Optional[str] = None
    is_applying_for_same_type: str = "Yes"
    is_applying_in_same_country: str = "Yes"
    has_been_ten_printed: str = "No"
    has_visa_been_lost_or_stolen: Optional[VisaLostOrStolenBuilder] = None
    has_visa_been_cancelled_or_revoked: Optional[VisaCancelledOrRevokedBuilder] = None

@dataclass
class RefusalInfoBuilder:
    """Refusal information."""
    answer: str = "No"
    explanation: Optional[str] = None

@dataclass
class PreviousUSTravelBuilder:
    """Previous US travel information."""
    has_ever_been_in_us: str = "No"
    previous_visits: List[PreviousVisitBuilder] = field(default_factory=list)
    has_ever_held_us_license: str = "No"
    drivers_licenses: List[DriversLicenseBuilder] = field(default_factory=list)
    has_ever_been_issued_us_visa: str = "No"
    previous_visa_info: Optional[PreviousVisaInfoBuilder] = None
    has_ever_been_refused_admission_or_visa: Optional[RefusalInfoBuilder] = None
    has_immigrant_petition_been_filed: Optional[RefusalInfoBuilder] = None
    has_ever_been_denied_travel_authorization: Optional[RefusalInfoBuilder] = None

@dataclass
class USContactBuilder:
    """US contact information."""
    contact_person_surnames: str = ""
    contact_person_given_names: str = ""
    organization_name: Optional[str] = None
    relationship_to_you: str = ""
    address: Optional[USAddressBuilder] = None
    phone: str = ""
    email: str = ""

@dataclass
class PersonSponsorBuilder:
    """Person sponsor information."""
    surnames: str = ""
    given_names: str = ""
    relationship: str = ""
    address: Optional[USAddressBuilder] = None
    phone: str = ""
    email: str = ""

@dataclass
class CompanySponsorBuilder:
    """Company sponsor information."""
    company_name: str = ""
    contact_person_surnames: str = ""
    contact_person_given_names: str = ""
    relationship: str = ""
    address: Optional[USAddressBuilder] = None
    phone: str = ""
    email: str = ""

@dataclass
class USPetitionerSponsorBuilder:
    """US Petitioner sponsor information."""
    petitioner_surnames: str = ""
    petitioner_given_names: str = ""
    relationship: str = ""
    address: Optional[USAddressBuilder] = None
    phone: str = ""
    email: str = ""

@dataclass
class EmployerSponsorBuilder:
    """Employer sponsor information."""
    employer_name: str = ""
    contact_person_surnames: Optional[str] = None
    contact_person_given_names: Optional[str] = None
    address: Optional[USAddressBuilder] = None
    phone: str = ""
    email: str = ""

@dataclass
class SponsorInfoBuilder:
    """Sponsor information based on person_entity_paying."""
    person_sponsor: Optional[PersonSponsorBuilder] = None
    company_sponsor: Optional[CompanySponsorBuilder] = None
    us_petitioner_sponsor: Optional[USPetitionerSponsorBuilder] = None
    employer_sponsor: Optional[EmployerSponsorBuilder] = None

@dataclass
class FamilyMemberBuilder:
    """Family member information."""
    surnames: str = ""
    given_names: str = ""
    date_of_birth: Optional[date] = None
    is_in_the_us: str = "No"
    status: str = ""

@dataclass
class ImmediateRelativeBuilder:
    """Immediate relative information."""
    surnames: str = ""
    given_names: str = ""
    relationship: str = ""
    status: str = ""

@dataclass
class FamilyInfoBuilder:
    """Family information with IDE support."""
    father: Optional[FamilyMemberBuilder] = None
    mother: Optional[FamilyMemberBuilder] = None
    has_immediate_relatives_in_us: str = "No"
    immediate_relatives: List[ImmediateRelativeBuilder] = field(default_factory=list)

@dataclass
class WorkEducationAddressBuilder:
    """Work/Education address information."""
    street_line1: str = ""
    city: str = ""
    state_province: str = ""
    postal_zone_zip_code: str = ""
    country_region: str = ""

@dataclass
class PresentWorkBuilder:
    """Present work information."""
    primary_occupation: str = ""
    employer_or_school_name: str = ""
    address: Optional[WorkEducationAddressBuilder] = None
    start_date: Optional[date] = None
    monthly_income_local: Optional[str] = None
    duties: str = ""

@dataclass
class PreviousWorkBuilder:
    """Previous work information."""
    employer_name: str = ""
    address: Optional[WorkEducationAddressBuilder] = None
    telephone_number: str = ""
    job_title: str = ""
    supervisor_surnames: str = ""
    supervisor_given_names: str = ""
    employment_date_from: Optional[date] = None
    employment_date_to: Optional[date] = None
    duties: str = ""

@dataclass
class PreviousEducationBuilder:
    """Previous education information."""
    institution_name: str = ""
    address: Optional[WorkEducationAddressBuilder] = None
    course_of_study: str = ""
    date_of_attendance_from: Optional[date] = None
    date_of_attendance_to: Optional[date] = None

@dataclass
class YesNoAnswerBuilder:
    """Yes/No answer with explanation."""
    answer: str = "No"
    explanation: Optional[str] = None

@dataclass
class CharitableOrganizationBuilder:
    """Charitable organization information."""
    answer: str = "No"
    organization_name: Optional[str] = None

@dataclass
class MilitaryServiceBuilder:
    """Military service information."""
    answer: str = "No"
    country: Optional[str] = None
    branch_of_service: Optional[str] = None
    rank_position: Optional[str] = None
    military_specialty: Optional[str] = None
    date_of_service_from: Optional[date] = None
    date_of_service_to: Optional[date] = None

@dataclass
class AdditionalInfoBuilder:
    """Additional information."""
    clan_or_tribe_name: Optional[str] = None
    languages_spoken: List[str] = field(default_factory=list)
    countries_visited_last_five_years: List[str] = field(default_factory=list)
    charitable_organizations_worked_for: Optional[CharitableOrganizationBuilder] = None
    specialized_skills: Optional[YesNoAnswerBuilder] = None
    has_served_in_military: Optional[MilitaryServiceBuilder] = None
    has_been_in_rebel_group: Optional[YesNoAnswerBuilder] = None

@dataclass
class WorkAndEducationBuilder:
    """Work and education information."""
    present: Optional[PresentWorkBuilder] = None
    previous_work: List[PreviousWorkBuilder] = field(default_factory=list)
    previous_education: List[PreviousEducationBuilder] = field(default_factory=list)
    additional_info: Optional[AdditionalInfoBuilder] = None

@dataclass
class SecurityPart1Builder:
    """Security questions part 1 - Medical and Health."""
    has_communicable_disease: Optional[YesNoAnswerBuilder] = None
    has_mental_or_physical_disorder: Optional[YesNoAnswerBuilder] = None
    is_drug_abuser_or_addict: Optional[YesNoAnswerBuilder] = None

@dataclass
class SecurityPart2Builder:
    """Security questions part 2 - Criminal."""
    has_been_arrested: Optional[YesNoAnswerBuilder] = None
    has_violated_controlled_substance_laws: Optional[YesNoAnswerBuilder] = None
    engaged_in_prostitution: Optional[YesNoAnswerBuilder] = None
    engaged_in_money_laundering: Optional[YesNoAnswerBuilder] = None
    committed_human_trafficking: Optional[YesNoAnswerBuilder] = None
    aided_human_trafficking: Optional[YesNoAnswerBuilder] = None
    is_family_of_human_trafficker: Optional[YesNoAnswerBuilder] = None

@dataclass
class SecurityPart3Builder:
    """Security questions part 3 - Security."""
    engaged_in_espionage_or_sabotage: Optional[YesNoAnswerBuilder] = None
    engaged_in_terrorist_activities: Optional[YesNoAnswerBuilder] = None
    provided_support_to_terrorists: Optional[YesNoAnswerBuilder] = None
    is_member_of_terrorist_organization: Optional[YesNoAnswerBuilder] = None
    participated_in_genocide: Optional[YesNoAnswerBuilder] = None
    participated_in_torture: Optional[YesNoAnswerBuilder] = None
    participated_in_extrajudicial_killings: Optional[YesNoAnswerBuilder] = None
    violated_religious_freedoms: Optional[YesNoAnswerBuilder] = None
    involved_in_forced_population_control: Optional[YesNoAnswerBuilder] = None
    involved_in_coercive_organ_transplantation: Optional[YesNoAnswerBuilder] = None

@dataclass
class SecurityPart4Builder:
    """Security questions part 4 - Immigration Violations."""
    sought_visa_by_fraud: Optional[YesNoAnswerBuilder] = None
    failed_to_attend_removal_hearings: Optional[YesNoAnswerBuilder] = None
    been_unlawfully_present_or_overstayed: Optional[YesNoAnswerBuilder] = None
    been_removed_or_deported: Optional[YesNoAnswerBuilder] = None

@dataclass
class SecurityPart5Builder:
    """Security questions part 5 - Miscellaneous."""
    withheld_custody_of_us_citizen_child: Optional[YesNoAnswerBuilder] = None
    voted_in_us_illegally: Optional[YesNoAnswerBuilder] = None
    renounced_us_citizenship_for_tax: Optional[YesNoAnswerBuilder] = None
    attended_public_school_on_student_visa_without_reimbursement: Optional[YesNoAnswerBuilder] = None

@dataclass
class SecurityAndBackgroundBuilder:
    """Security and background information."""
    part1_medical_and_health: Optional[SecurityPart1Builder] = None
    part2_criminal: Optional[SecurityPart2Builder] = None
    part3_security: Optional[SecurityPart3Builder] = None
    part4_immigration_violations: Optional[SecurityPart4Builder] = None
    part5_miscellaneous: Optional[SecurityPart5Builder] = None


class DS160DataBuilder:
    """
    Main DS-160 Data Builder with full IDE support.
    
    Provides type-safe access to all DS-160 form fields with autocompletion.
    After configuring data, call generate_json() to export to bot format.
    
    Example:
        builder = DS160DataBuilder()
        builder.personal_info.surnames = "SMITH"
        builder.personal_info.given_names = "JOHN MICHAEL"
        builder.personal_info.sex = Gender.MALE
        builder.personal_info.marital_status = MaritalStatus.SINGLE
        builder.personal_info.date_of_birth = date(1990, 5, 15)
        
        json_data = builder.generate_json()
        builder.save_json("data/my_application.json")
    """
    
    def __init__(self):
        """Initialize builder with all sections."""
        # Core sections (required)
        self.application_info = ApplicationInfoBuilder()
        self.personal_info = PersonalInfoBuilder()
        self.nationality_and_residence = NationalityAndResidenceBuilder()
        self.passport_info = PassportInfoBuilder()
        self.contact_info = ContactInfoBuilder()
        self.travel_info = TravelInfoBuilder()
        self.us_contact = USContactBuilder()
        self.family_info = FamilyInfoBuilder()
        self.work_and_education = WorkAndEducationBuilder()
        
        # Optional sections
        self.travel_companions: Optional[TravelCompanionsBuilder] = None
        self.previous_us_travel: Optional[PreviousUSTravelBuilder] = None
        self.sponsor_info: Optional[SponsorInfoBuilder] = None
        self.security_and_background: Optional[SecurityAndBackgroundBuilder] = None
        
        # Initialize required nested objects with defaults
        self._init_defaults()
    
    def _init_defaults(self):
        """Initialize required nested objects with defaults."""
        self.contact_info.mailing_address = AddressBuilder()
        self.contact_info.phone_numbers = PhoneNumbersBuilder()
        self.contact_info.email_addresses = EmailAddressesBuilder()
        self.travel_info.us_stay_address = USAddressBuilder()
        self.us_contact.address = USAddressBuilder()
        self.family_info.father = FamilyMemberBuilder()
        self.family_info.mother = FamilyMemberBuilder()
        self.work_and_education.present = PresentWorkBuilder()
        self.work_and_education.present.address = WorkEducationAddressBuilder()
    
    def add_travel_companions(self) -> TravelCompanionsBuilder:
        """Add travel companions section."""
        if self.travel_companions is None:
            self.travel_companions = TravelCompanionsBuilder()
        return self.travel_companions
    
    def add_previous_us_travel(self) -> PreviousUSTravelBuilder:
        """Add previous US travel section."""
        if self.previous_us_travel is None:
            self.previous_us_travel = PreviousUSTravelBuilder()
        return self.previous_us_travel
    
    def add_sponsor_info(self) -> SponsorInfoBuilder:
        """Add sponsor information section."""
        if self.sponsor_info is None:
            self.sponsor_info = SponsorInfoBuilder()
        return self.sponsor_info
    
    def add_security_and_background(self) -> SecurityAndBackgroundBuilder:
        """Add security and background section with default 'No' answers."""
        if self.security_and_background is None:
            self.security_and_background = SecurityAndBackgroundBuilder()
            
            # Initialize all parts with default 'No' answers
            self.security_and_background.part1_medical_and_health = SecurityPart1Builder(
                has_communicable_disease=YesNoAnswerBuilder(),
                has_mental_or_physical_disorder=YesNoAnswerBuilder(),
                is_drug_abuser_or_addict=YesNoAnswerBuilder()
            )
            
            self.security_and_background.part2_criminal = SecurityPart2Builder(
                has_been_arrested=YesNoAnswerBuilder(),
                has_violated_controlled_substance_laws=YesNoAnswerBuilder(),
                engaged_in_prostitution=YesNoAnswerBuilder(),
                engaged_in_money_laundering=YesNoAnswerBuilder(),
                committed_human_trafficking=YesNoAnswerBuilder(),
                aided_human_trafficking=YesNoAnswerBuilder(),
                is_family_of_human_trafficker=YesNoAnswerBuilder()
            )
            
            self.security_and_background.part3_security = SecurityPart3Builder(
                engaged_in_espionage_or_sabotage=YesNoAnswerBuilder(),
                engaged_in_terrorist_activities=YesNoAnswerBuilder(),
                provided_support_to_terrorists=YesNoAnswerBuilder(),
                is_member_of_terrorist_organization=YesNoAnswerBuilder(),
                participated_in_genocide=YesNoAnswerBuilder(),
                participated_in_torture=YesNoAnswerBuilder(),
                participated_in_extrajudicial_killings=YesNoAnswerBuilder(),
                violated_religious_freedoms=YesNoAnswerBuilder(),
                involved_in_forced_population_control=YesNoAnswerBuilder(),
                involved_in_coercive_organ_transplantation=YesNoAnswerBuilder()
            )
            
            self.security_and_background.part4_immigration_violations = SecurityPart4Builder(
                sought_visa_by_fraud=YesNoAnswerBuilder(),
                failed_to_attend_removal_hearings=YesNoAnswerBuilder(),
                been_unlawfully_present_or_overstayed=YesNoAnswerBuilder(),
                been_removed_or_deported=YesNoAnswerBuilder()
            )
            
            self.security_and_background.part5_miscellaneous = SecurityPart5Builder(
                withheld_custody_of_us_citizen_child=YesNoAnswerBuilder(),
                voted_in_us_illegally=YesNoAnswerBuilder(),
                renounced_us_citizenship_for_tax=YesNoAnswerBuilder(),
                attended_public_school_on_student_visa_without_reimbursement=YesNoAnswerBuilder()
            )
        
        return self.security_and_background
    
    def _to_dict(self, obj: Any, camel_case: bool = True) -> Any:
        """Convert builder objects to dictionaries recursively."""
        if obj is None:
            return None
        elif isinstance(obj, date):
            return obj.isoformat()
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, (str, int, float, bool)):
            return obj
        elif isinstance(obj, (list, tuple)):
            return [self._to_dict(item, camel_case) for item in obj]
        elif hasattr(obj, '__dict__'):
            # Convert dataclass to dict
            result = {}
            for key, value in obj.__dict__.items():
                # Skip empty optional fields
                if value is None or (isinstance(value, list) and len(value) == 0):
                    continue
                if isinstance(value, str) and value == "":
                    continue
                
                # Convert snake_case to camelCase for JSON export
                if camel_case:
                    if key == 'us_stay_address':
                        json_key = 'usStayAddress'
                    elif key == 'person_entity_paying':
                        json_key = 'personEntityPaying'
                    elif key == 'locations_to_visit_in_us':
                        json_key = 'locationsToVisitInUS'
                    elif key == 'previous_us_travel':
                        json_key = 'previousUSTravel'
                    elif key == 'is_in_the_us':
                        json_key = 'isInTheUS'
                    elif key == 'has_ever_been_in_us':
                        json_key = 'hasEverBeenInUS'
                    elif key == 'has_immediate_relatives_in_us':
                        json_key = 'hasImmediateRelativesInUS'
                    elif 'us_' in key:
                        # Handle other US-related fields
                        json_key = key.replace('us_', 'us')
                        parts = json_key.split('_')
                        json_key = parts[0] + ''.join(word.capitalize() for word in parts[1:])
                    else:
                        # Standard camelCase conversion
                        parts = key.split('_')
                        json_key = parts[0] + ''.join(word.capitalize() for word in parts[1:])
                else:
                    json_key = key
                
                result[json_key] = self._to_dict(value, camel_case)
            
            return result
        else:
            return obj
    
    def generate_json(self) -> Dict[str, Any]:
        """Generate JSON data compatible with DS-160 bot format."""
        return {
            "applicationInfo": self._to_dict(self.application_info),
            "personalInfo": self._to_dict(self.personal_info),
            "nationalityAndResidence": self._to_dict(self.nationality_and_residence),
            "passportInfo": self._to_dict(self.passport_info),
            "contactInfo": self._to_dict(self.contact_info),
            "travelInfo": self._to_dict(self.travel_info),
            "travelCompanions": self._to_dict(self.travel_companions) if self.travel_companions else None,
            "previousUSTravel": self._to_dict(self.previous_us_travel) if self.previous_us_travel else None,
            "usContact": self._to_dict(self.us_contact),
            "sponsorInfo": self._to_dict(self.sponsor_info) if self.sponsor_info else None,
            "familyInfo": self._to_dict(self.family_info),
            "workAndEducation": self._to_dict(self.work_and_education),
            "securityAndBackground": self._to_dict(self.security_and_background) if self.security_and_background else None
        }
    
    def save_json(self, file_path: Union[str, Path], indent: int = 2) -> None:
        """Save generated JSON to file."""
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        data = self.generate_json()
        # Remove None values from root level
        data = {k: v for k, v in data.items() if v is not None}
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)
        
        print(f"✅ DS-160 data saved to: {file_path}")
    
    def validate(self) -> List[str]:
        """Validate current data and return list of issues."""
        issues = []
        
        # Check required fields
        if not self.personal_info.surnames:
            issues.append("Personal info: surnames is required")
        
        if not self.personal_info.given_names:
            issues.append("Personal info: given_names is required")
        
        if not self.personal_info.sex:
            issues.append("Personal info: sex is required")
        
        if not self.personal_info.date_of_birth:
            issues.append("Personal info: date_of_birth is required")
        
        if not self.passport_info.passport_number:
            issues.append("Passport info: passport_number is required")
        
        if not self.passport_info.expiration_date:
            issues.append("Passport info: expiration_date is required")
        elif self.passport_info.expiration_date <= date.today():
            issues.append("Passport info: expiration_date must be in the future")
        
        if not self.contact_info.email_addresses.primary:
            issues.append("Contact info: primary email is required")
        
        if not self.travel_info.arrival_date:
            issues.append("Travel info: arrival_date is required")
        elif self.travel_info.arrival_date <= date.today():
            issues.append("Travel info: arrival_date must be in the future")
        
        return issues
    
    @classmethod
    def create_example(cls) -> 'DS160DataBuilder':
        """Create an example DS-160 data builder with sample data."""
        builder = cls()
        
        # Personal Info
        builder.personal_info.surnames = "SMITH"
        builder.personal_info.given_names = "JOHN MICHAEL"
        builder.personal_info.sex = Gender.MALE
        builder.personal_info.marital_status = MaritalStatus.SINGLE
        builder.personal_info.date_of_birth = date(1990, 5, 15)
        builder.personal_info.place_of_birth = PlaceOfBirthBuilder(
            city="NEW YORK",
            state_province="NEW YORK",
            country_region="UNITED STATES"
        )
        
        # Nationality
        builder.nationality_and_residence.country_of_origin = "UNITED STATES"
        
        # Passport
        builder.passport_info.passport_number = "*********"
        builder.passport_info.issuing_country = "UNITED STATES"
        builder.passport_info.issuing_city = "NEW YORK"
        builder.passport_info.issuing_state_province = "NEW YORK"
        builder.passport_info.issuance_date = date(2020, 1, 1)
        builder.passport_info.expiration_date = date(2030, 1, 1)
        
        # Contact Info
        builder.contact_info.mailing_address.street_line1 = "123 MAIN ST"
        builder.contact_info.mailing_address.city = "NEW YORK"
        builder.contact_info.mailing_address.state_province = "NEW YORK"
        builder.contact_info.mailing_address.postal_zone_zip_code = "10001"
        builder.contact_info.mailing_address.country_region = "UNITED STATES"
        builder.contact_info.phone_numbers.primary = "******-123-4567"
        builder.contact_info.email_addresses.primary = "<EMAIL>"
        
        # Travel Info
        builder.travel_info.purpose_of_trip = VisaPurpose.BUSINESS_TOURISM.value
        builder.travel_info.visa_class = "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)"
        builder.travel_info.arrival_date = date(2025, 12, 1)
        builder.travel_info.arrival_city = "NEW YORK"
        builder.travel_info.us_stay_address.street_line1 = "456 BROADWAY"
        builder.travel_info.us_stay_address.city = "NEW YORK"
        builder.travel_info.us_stay_address.state = "NEW YORK"
        builder.travel_info.us_stay_address.zip_code = "10001"
        builder.travel_info.person_entity_paying = "SELF"
        
        # US Contact
        builder.us_contact.contact_person_surnames = "JOHNSON"
        builder.us_contact.contact_person_given_names = "JANE"
        builder.us_contact.relationship_to_you = "FRIEND"
        builder.us_contact.address.street_line1 = "789 FIFTH AVE"
        builder.us_contact.address.city = "NEW YORK"
        builder.us_contact.address.state = "NEW YORK"
        builder.us_contact.address.zip_code = "10001"
        builder.us_contact.phone = "******-987-6543"
        builder.us_contact.email = "<EMAIL>"
        
        # Work & Education
        builder.work_and_education.present.primary_occupation = PrimaryOccupation.COMPUTER_SCIENCE.value
        builder.work_and_education.present.employer_or_school_name = "TECH CORP"
        builder.work_and_education.present.address.street_line1 = "100 TECH BLVD"
        builder.work_and_education.present.address.city = "NEW YORK"
        builder.work_and_education.present.address.state_province = "NEW YORK"
        builder.work_and_education.present.address.postal_zone_zip_code = "10001"
        builder.work_and_education.present.address.country_region = "UNITED STATES"
        builder.work_and_education.present.start_date = date(2020, 1, 1)
        builder.work_and_education.present.duties = "SOFTWARE DEVELOPMENT"
        
        # Add default security (all No)
        builder.add_security_and_background()
        
        return builder


# Convenience functions for common operations
def create_builder() -> DS160DataBuilder:
    """Create a new DS160DataBuilder instance."""
    return DS160DataBuilder()

def create_example() -> DS160DataBuilder:
    """Create an example DS160DataBuilder with sample data."""
    return DS160DataBuilder.create_example()

def load_from_json(file_path: Union[str, Path]) -> DS160DataBuilder:
    """Load DS-160 data from existing JSON file into builder."""
    # This would require reverse conversion from JSON to builder objects
    # For now, return empty builder (can be implemented if needed)
    return DS160DataBuilder()


if __name__ == "__main__":
    # Example usage
    print("DS-160 Data Generator - Example Usage")
    print("=" * 50)
    
    # Create builder
    builder = DS160DataBuilder()
    
    # Configure basic info with IDE support
    builder.personal_info.surnames = "SMITH"
    builder.personal_info.given_names = "JOHN MICHAEL"
    builder.personal_info.sex = Gender.MALE
    builder.personal_info.marital_status = MaritalStatus.SINGLE
    builder.personal_info.date_of_birth = date(1990, 5, 15)
    
    # Add passport info
    builder.passport_info.passport_number = "*********"
    builder.passport_info.issuing_country = "UNITED STATES"
    builder.passport_info.expiration_date = date(2030, 1, 1)
    
    # Generate and preview JSON
    data = builder.generate_json()
    print("Generated JSON structure:")
    print(json.dumps(data, indent=2, default=str))
    
    # Validate
    issues = builder.validate()
    if issues:
        print(f"\nValidation issues found: {len(issues)}")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("\n✅ Validation passed!")
    
    print(f"\nExample complete. Use builder.save_json('path/to/file.json') to save.")