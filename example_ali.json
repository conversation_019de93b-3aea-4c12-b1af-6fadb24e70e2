{"applicationInfo": {"formType": "DS-160", "applicationId": "AA01234567"}, "personalInfo": {"surnames": "IVANOV", "givenNames": "ALEXANDER DMITRIEVICH", "fullNameInNativeAlphabet": "ИВАНОВ АЛЕКСАНДР ДМИТРИЕВИЧ", "otherNamesUsed": [{"surnames": "PETROV", "givenNames": "ALEX", "reason": "Previous name before legal change"}], "gender": "MALE", "maritalStatus": "MARRIED", "dateOfBirth": "1985-03-15", "cityOfBirth": "MOSCOW", "stateProvinceOfBirth": "MOSCOW OBLAST", "countryOfBirth": "RUSSIA", "spouseInfo": {"surnames": "IVANOVA", "givenNames": "ELENA SERGEEVNA", "dateOfBirth": "1987-07-22", "nationalityCountryOfBirth": "RUSSIA", "cityOfBirth": "ST. PETERSBURG", "countryOfBirth": "RUSSIA", "address": {"streetLine1": "Nevsky Prospect 123, Apt 45", "city": "ST. PETERSBURG", "stateProvince": "LENINGRAD OBLAST", "postalZoneZipCode": "190000", "countryRegion": "RUSSIA"}}, "fatherInfo": {"surnames": "IVANOV", "givenNames": "DMITRI NIKOLAEVICH", "dateOfBirth": "1955-12-10"}, "motherInfo": {"surnames": "IVANOVA", "givenNames": "MARIA PETROVNA", "dateOfBirth": "1958-05-18"}}, "nationalityAndResidence": {"countryOfCitizenship": ["RUSSIA"], "nationalityCountryOfBirth": "RUSSIA", "countryOfResidence": "RUSSIA", "otherCitizenships": ["KAZAKHSTAN"], "isPermanentResidentOfOtherCountry": "Yes", "permanentResidentCountries": ["BELARUS"], "nationalIdentificationNumber": "*********0", "usSocialSecurityNumber": "***********", "usTaxpayerIdNumber": "*********"}, "passportInfo": {"passportType": "REGULAR", "passportNumber": "*********", "passportBookNumber": "A1234567", "issuingCountry": "RUSSIA", "issuingCity": "MOSCOW", "issuingStateProvince": "MOSCOW OBLAST", "issuanceDate": "2020-01-15", "expirationDate": "2030-01-15", "hasLostOrStolenPassport": {"answer": "Yes", "lostPassportNumber": "*********", "lostPassportIssuingCountry": "RUSSIA", "explanation": "Passport was stolen during travel in 2019. Police report filed."}}, "contactInfo": {"homeAddress": {"streetLine1": "Tverskaya Street 456, Apt 78", "city": "MOSCOW", "stateProvince": "MOSCOW OBLAST", "postalZoneZipCode": "101000", "countryRegion": "RUSSIA"}, "mailingAddress": {"streetLine1": "P.O. Box 123", "city": "MOSCOW", "stateProvince": "MOSCOW OBLAST", "postalZoneZipCode": "101001", "countryRegion": "RUSSIA"}, "phoneNumbers": {"primary": "******-123-4567", "secondary": "******-987-6543", "work": "******-555-0123", "other": "******-111-2233"}, "emailAddresses": {"primary": "<EMAIL>", "additional": "<EMAIL>"}, "socialMedia": [{"platform": "FACEBOOK", "identifier": "alexander.ivanov.moscow"}, {"platform": "INSTAGRAM", "identifier": "alex_moscow_85"}, {"platform": "LINKEDIN", "identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-engineer"}, {"platform": "TWITTER", "identifier": "@alex_moscow"}]}, "travelInfo": {"purposeOfTrip": "TEMP. BUSINESS OR PLEASURE VISITOR (B)", "visaClass": "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)", "hasSpecificTravelPlans": "Yes", "arrivalDate": "2025-12-01", "arrivalFlight": "SU101", "arrivalCity": "NEW YORK", "departureDate": "2025-12-15", "departureFlight": "SU102", "departureCity": "NEW YORK", "locationsToVisitInUS": ["NEW YORK", "WASHINGTON DC", "BOSTON"], "usStayAddress": {"streetLine1": "1234 Broadway", "city": "NEW YORK", "state": "NEW YORK", "zipCode": "10001"}, "personEntityPaying": "SELF"}, "travelCompanions": {"areOtherPersonsTraveling": "Yes", "personsTraveling": [{"surnames": "IVANOVA", "givenNames": "ELENA SERGEEVNA", "relationship": "SPOUSE"}, {"surnames": "IVANOV", "givenNames": "MIKHAIL ALEXANDROVICH", "relationship": "CHILD"}], "isTravelingAsPartOfGroup": "Yes", "groupName": "Family Vacation Group"}, "previousUSTravel": {"hasEverBeenInUS": "Yes", "previousVisits": [{"dateArrived": "2022-06-15", "lengthOfStay": "14", "unitOfStay": "DAY(S)"}, {"dateArrived": "2020-09-10", "lengthOfStay": "21", "unitOfStay": "DAY(S)"}], "hasEverHeldUSLicense": "No", "driversLicenses": [], "hasEverBeenIssuedUSVisa": "Yes", "previousVisas": [{"visaNumber": "12345678", "issuanceDate": "2022-05-01", "annotation": "B1/B2"}], "hasEverBeenRefusedUSVisa": "No", "refusedVisaApplications": [], "hasEverBeenRefusedUSEntry": "No", "refusedEntryDetails": [], "hasEverAppliedForUSImmigrantVisa": "No", "immigrantVisaDetails": []}, "usContacts": {"personOrganizationInUS": {"surnames": "SMITH", "givenNames": "JOHN ROBERT", "organizationName": "ABC Technologies Inc.", "relationship": "BUSINESS ASSOCIATE", "address": {"streetLine1": "500 Fifth Avenue, Suite 1200", "city": "NEW YORK", "state": "NEW YORK", "zipCode": "10110"}, "phoneNumber": "******-555-0100", "emailAddress": "<EMAIL>"}, "hasOtherPersonsInUS": "Yes", "otherPersons": [{"surnames": "BROWN", "givenNames": "MARY ELIZABETH", "relationship": "FRIEND", "status": "US CITIZEN"}], "hasImmediateRelativesInUS": "Yes", "immediateRelatives": [{"surnames": "IVANOV", "givenNames": "PETER DMITRIEVICH", "relationship": "BROTHER", "status": "PERMANENT RESIDENT"}]}, "workAndEducation": {"present": {"primaryOccupation": "COMPUTER SCIENCE", "employerOrSchoolName": "TechCorp Solutions LLC", "address": {"streetLine1": "Leninsky Prospect 789", "city": "MOSCOW", "stateProvince": "MOSCOW OBLAST", "postalZoneZipCode": "119991", "countryRegion": "RUSSIA"}, "startDate": "2020-03-01", "monthlyIncomeLocal": "250000", "duties": "Software development, system architecture design, team leadership"}, "previousWork": [{"employerName": "Digital Innovations Ltd", "address": {"streetLine1": "Arbat Street 25", "city": "MOSCOW", "stateProvince": "MOSCOW OBLAST", "postalZoneZipCode": "119002", "countryRegion": "RUSSIA"}, "telephoneNumber": "******-777-8888", "jobTitle": "Senior Software Engineer", "supervisorSurnames": "PETROV", "supervisorGivenNames": "SERGEI VLADIMIROVICH", "employmentDateFrom": "2018-01-15", "employmentDateTo": "2020-02-28", "duties": "Full-stack web development, database optimization"}, {"employerName": "StartupHub Moscow", "address": {"streetLine1": "Sadovoe Ring 101", "city": "MOSCOW", "stateProvince": "MOSCOW OBLAST", "postalZoneZipCode": "105064", "countryRegion": "RUSSIA"}, "telephoneNumber": "******-999-0000", "jobTitle": "Junior Developer", "supervisorSurnames": "VOLKOV", "supervisorGivenNames": "IGOR MIKHAILOVICH", "employmentDateFrom": "2016-06-01", "employmentDateTo": "2017-12-31", "duties": "Frontend development, user interface design"}], "previousEducation": [{"institutionName": "Moscow State University", "address": {"streetLine1": "Leninskie Gory 1", "city": "MOSCOW", "stateProvince": "MOSCOW OBLAST", "postalZoneZipCode": "119991", "countryRegion": "RUSSIA"}, "courseOfStudy": "Computer Science, Master of Science", "dateOfAttendanceFrom": "2007-09-01", "dateOfAttendanceTo": "2012-06-30"}, {"institutionName": "Moscow Technical Institute", "address": {"streetLine1": "Baumanskaya Street 5", "city": "MOSCOW", "stateProvince": "MOSCOW OBLAST", "postalZoneZipCode": "105005", "countryRegion": "RUSSIA"}, "courseOfStudy": "Advanced Programming Certificate", "dateOfAttendanceFrom": "2013-01-15", "dateOfAttendanceTo": "2013-12-20"}], "additionalInfo": {"clanOrTribeName": "Northern Slavic", "languagesSpoken": ["RUSSIAN", "ENGLISH", "GERMAN", "SPANISH"], "countriesVisitedLastFiveYears": ["GERMANY", "FRANCE", "UNITED KINGDOM", "SPAIN", "ITALY", "TURKEY", "CHINA", "JAPAN"], "charitableOrganizationsWorkedFor": {"answer": "Yes", "organizationName": "Moscow Children's Foundation, Red Cross Russia"}, "specializedSkills": {"answer": "Yes", "explanation": "Expert in cybersecurity, cloud computing, artificial intelligence systems"}, "hasServedInMilitary": {"answer": "Yes", "country": "RUSSIA", "branchOfService": "ARMY", "rankPosition": "SERGEANT", "militarySpecialty": "COMMUNICATIONS SPECIALIST", "dateOfServiceFrom": "2003-05-01", "dateOfServiceTo": "2005-04-30"}, "hasBeenInRebelGroup": {"answer": "No", "explanation": ""}}}, "securityAndBackground": {"part1MedicalAndHealth": {"hasCommunicableDisease": {"answer": "No", "explanation": ""}, "hasMentalOrPhysicalDisorder": {"answer": "No", "explanation": ""}, "isDrugAbuserOrAddict": {"answer": "No", "explanation": ""}}, "part2Criminal": {"hasBeenArrested": {"answer": "No", "explanation": ""}, "hasViolatedControlledSubstanceLaws": {"answer": "No", "explanation": ""}, "engagedInProstitution": {"answer": "No", "explanation": ""}, "engagedInMoneyLaundering": {"answer": "No", "explanation": ""}, "committedHumanTrafficking": {"answer": "No", "explanation": ""}, "aidedHumanTrafficking": {"answer": "No", "explanation": ""}, "isFamilyOfHumanTrafficker": {"answer": "No", "explanation": ""}}, "part3Security": {"engagedInEspionageOrSabotage": {"answer": "No", "explanation": ""}, "engagedInTerroristActivities": {"answer": "No", "explanation": ""}, "providedSupportToTerrorists": {"answer": "No", "explanation": ""}, "isMemberOfTerroristOrganization": {"answer": "No", "explanation": ""}, "participatedInGenocide": {"answer": "No", "explanation": ""}, "committedTorture": {"answer": "No", "explanation": ""}, "committedExtraJudicialKillings": {"answer": "No", "explanation": ""}, "engagedInChildSoldierRecruitment": {"answer": "No", "explanation": ""}, "engagedInReligiousFreedomViolations": {"answer": "No", "explanation": ""}, "engagedInPopulationControls": {"answer": "No", "explanation": ""}}, "part4ImmigrationViolations": {"beenRefusedUSVisa": {"answer": "No", "explanation": ""}, "beenRefusedUSAdmission": {"answer": "No", "explanation": ""}, "withdrawnUSVisaApplication": {"answer": "No", "explanation": ""}, "hadUSVisaCancelled": {"answer": "No", "explanation": ""}, "beenDeportedFromUS": {"answer": "No", "explanation": ""}, "overstayedUSVisa": {"answer": "No", "explanation": ""}, "workedInUSWithoutAuthorization": {"answer": "No", "explanation": ""}}, "part5Miscellaneous": {"attemptedToObtainVisaByFraud": {"answer": "No", "explanation": ""}, "assistedOthersInViolatingUSLaw": {"answer": "No", "explanation": ""}, "violatedTermsOfUSVisa": {"answer": "No", "explanation": ""}, "beenSubjectOfFinalRemovalOrder": {"answer": "No", "explanation": ""}, "attendedPublicSchoolOnStudentVisaWithoutReimbursement": {"answer": "No", "explanation": ""}, "failedToAttendOrRemainInProgram": {"answer": "No", "explanation": ""}, "engagedInUnauthorizedActivity": {"answer": "No", "explanation": ""}, "hasBeenDeportedFromCountry": {"answer": "No", "explanation": ""}, "hasChildCustodyIssues": {"answer": "No", "explanation": ""}, "owesChildSupport": {"answer": "No", "explanation": ""}, "votedInUSElection": {"answer": "No", "explanation": ""}, "renouncedUSCitizenship": {"answer": "No", "explanation": ""}}}}