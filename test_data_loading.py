#!/usr/bin/env python3

import json
import sys
from pathlib import Path

# Добавляем src директорию в путь
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from data_loader import DS160Data

def test_data_loading():
    # Загружаем JSON
    json_path = Path("data/sample_application.json")
    
    if not json_path.exists():
        print(f"❌ Файл {json_path} не найден!")
        return
    
    print(f"📁 Загружаем: {json_path}")
    
    with open(json_path, 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    print("\n🔍 JSON данные previousUSTravel:")
    if 'previousUSTravel' in json_data:
        print(json.dumps(json_data['previousUSTravel'], indent=2, ensure_ascii=False))
    else:
        print("❌ previousUSTravel не найден в JSON!")
        return
    
    # Пытаемся создать Pydantic объект
    print("\n🏗️ Парсим в Pydantic...")
    try:
        # ds160_data = DS160Data(**json_data)
        
        from src.data_loader import DataLoader
        
        loader = DataLoader(json_path)
        ds160_data = loader.load_data_sync()
        
        # Validate data
        issues = loader.validate_data()
        print("✅ Успешно спарсили!")
        
        # Показываем что получилось
        travel_data = ds160_data.previous_us_travel
        print(f"\n📊 Pydantic объект previous_us_travel:")
        print(f"has_ever_been_in_us: {travel_data.has_ever_been_in_us}")
        print(f"previous_visits: {len(travel_data.previous_visits)} визитов")
        for i, visit in enumerate(travel_data.previous_visits):
            print(f"  [{i}] date_arrived: {visit.date_arrived} (type: {type(visit.date_arrived)})")
            print(f"  [{i}] length_of_stay: {visit.length_of_stay}")
            print(f"  [{i}] unit_of_stay: {visit.unit_of_stay}")
        
        print(f"has_ever_been_issued_us_visa: {travel_data.has_ever_been_issued_us_visa}")
        print(f"previous_visas: {len(travel_data.previous_visa_info)} visas")

        for i, visa in enumerate(travel_data.previous_visa_info):
            print(f"  [{i}] visa_number: {visa.visa_number}")
            print(f"  [{i}] issuance_date: {visa.date_last_visa_issued}")
            print(f"  [{i}] annotation: {visa.annotation}")
            print(f"  [{i}] is_applying_for_same_type: {visa.is_applying_for_same_type}")
            print(f"  [{i}] is_applying_in_same_country: {visa.is_applying_in_same_country}")
            print(f"  [{i}] has_been_ten_printed: {visa.has_been_ten_printed}")
        
        print(f"has_ever_held_us_license: {travel_data.has_ever_held_us_license}")
        print(f"drivers_licenses: {len(travel_data.drivers_licenses)} лицензий")
        print(f"has_ever_been_issued_us_visa: {travel_data.has_ever_been_issued_us_visa}")
        
    except Exception as e:
        print(f"❌ Ошибка парсинга: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_loading()