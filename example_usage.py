#!/usr/bin/env python3
"""
Example usage of DS160DataBuilder - demonstrates IDE support and type safety.

Run this file to see how to build DS-160 data with full autocompletion.
"""

from datetime import date
from ds160_data_generator import (
    DS160DataBuilder, 
    Gender, 
    MaritalStatus, 
    PrimaryOccupation,
    VisaPurpose,
    PlaceOfBirthBuilder,
    YesNoAnswerBuilder,
    SpouseInfoBuilder
)

def create_sample_application():
    """Create a sample DS-160 application with full IDE support."""
    
    # Create builder - you get full type hints and autocompletion
    builder = DS160DataBuilder()
    
    # Personal Information - IDE will show all available fields
    builder.personal_info.surnames = "IVANOV"
    builder.personal_info.given_names = "IVAN IVANOVICH"
    builder.personal_info.full_name_native_alphabet = "ИВАНОВ ИВАН ИВАНОВИЧ"
    builder.personal_info.sex = Gender.MALE  # IDE shows MALE/FEMALE options
    builder.personal_info.marital_status = MaritalStatus.DIVORCED  # IDE shows all marital statuses
    builder.personal_info.date_of_birth = date(1990, 6, 15)
    
    # Spouse Information - for married persons
    builder.personal_info.spouse = SpouseInfoBuilder()
    builder.personal_info.spouse.surnames = "IVANOVA"
    builder.personal_info.spouse.given_names = "MARIA ALEXANDROVNA"
    builder.personal_info.spouse.date_of_birth = date(1992, 8, 20)
    builder.personal_info.spouse.nationality = "KAZAKHSTAN"
    builder.personal_info.spouse.place_of_birth = PlaceOfBirthBuilder(
        city="ALMATY",
        state_province="ALMATY REGION",
        country_region="KAZAKHSTAN"
    )
    
    # Place of birth with nested structure
    builder.personal_info.place_of_birth = PlaceOfBirthBuilder(
        city="ALMATY",
        state_province="ALMATY REGION", 
        country_region="KAZAKHSTAN"
    )
    
    # Nationality
    builder.nationality_and_residence.country_of_origin = "KAZAKHSTAN"
    builder.nationality_and_residence.is_in_the_us = "No"
    builder.nationality_and_residence.permanent_resident_countries = ["KAZAKHSTAN", "RUSSIA", "LITHUANIA"]
    
    # Passport Information
    builder.passport_info.passport_number = "N12345678"
    builder.passport_info.issuing_country = "KAZAKHSTAN"
    builder.passport_info.issuing_city = "ALMATY"
    builder.passport_info.issuing_state_province = "ALMATY REGION"
    builder.passport_info.issuance_date = date(2020, 3, 15)
    builder.passport_info.expiration_date = date(2030, 3, 15)
    
    # Contact Information - builder automatically initializes nested objects
    builder.contact_info.mailing_address.street_line1 = "AL-FARABI AVENUE 123"
    builder.contact_info.mailing_address.city = "ALMATY"
    builder.contact_info.mailing_address.state_province = "ALMATY REGION"
    builder.contact_info.mailing_address.postal_zone_zip_code = "050000"
    builder.contact_info.mailing_address.country_region = "KAZAKHSTAN"
    
    builder.contact_info.phone_numbers.primary = "******-123-4567"
    builder.contact_info.email_addresses.primary = "<EMAIL>"
    
    # Social media - add as needed
    from ds160_data_generator import SocialMediaBuilder
    instagram = SocialMediaBuilder()
    instagram.platform = "INSTAGRAM"
    instagram.identifier = "@ivan_ivanov"
    builder.contact_info.social_media.append(instagram)
    
    # Travel Information
    builder.travel_info.purpose_of_trip = VisaPurpose.BUSINESS_TOURISM.value
    builder.travel_info.visa_class = "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)"
    builder.travel_info.arrival_date = date(2025, 9, 1)
    builder.travel_info.arrival_city = "NEW YORK"
    builder.travel_info.departure_date = date(2025, 9, 15)
    builder.travel_info.departure_city = "NEW YORK"
    builder.travel_info.locations_to_visit_in_us = ["NEW YORK", "WASHINGTON DC"]
    builder.travel_info.person_entity_paying = "SELF"
    
    # US stay address
    builder.travel_info.us_stay_address.street_line1 = "123 BROADWAY"
    builder.travel_info.us_stay_address.city = "NEW YORK"
    builder.travel_info.us_stay_address.state = "NEW YORK"
    builder.travel_info.us_stay_address.zip_code = "10001"
    
    # US Contact
    builder.us_contact.contact_person_surnames = "SMITH"
    builder.us_contact.contact_person_given_names = "JOHN"
    builder.us_contact.relationship_to_you = "FRIEND"
    builder.us_contact.address.street_line1 = "456 FIFTH AVENUE"
    builder.us_contact.address.city = "NEW YORK"
    builder.us_contact.address.state = "NEW YORK"
    builder.us_contact.address.zip_code = "10001"
    builder.us_contact.phone = "******-123-4567"
    builder.us_contact.email = "<EMAIL>"
    
    # Family Information
    builder.family_info.father.surnames = "IVANOV"
    builder.family_info.father.given_names = "SERGEI PETROVICH"
    builder.family_info.father.date_of_birth = date(1960, 3, 20)
    builder.family_info.father.status = "FOREIGN NATIONAL"
    
    builder.family_info.mother.surnames = "IVANOVA"
    builder.family_info.mother.given_names = "MARIA ALEXANDROVNA"
    builder.family_info.mother.date_of_birth = date(1965, 8, 10)
    builder.family_info.mother.status = "FOREIGN NATIONAL"
    
    # Work and Education
    builder.work_and_education.present.primary_occupation = PrimaryOccupation.COMPUTER_SCIENCE.value
    builder.work_and_education.present.employer_or_school_name = "TECH KAZAKHSTAN LLP"
    builder.work_and_education.present.address.street_line1 = "SATPAYEV STREET 22A"
    builder.work_and_education.present.address.city = "ALMATY"
    builder.work_and_education.present.address.state_province = "ALMATY REGION"
    builder.work_and_education.present.address.postal_zone_zip_code = "050013"
    builder.work_and_education.present.address.country_region = "KAZAKHSTAN"
    builder.work_and_education.present.start_date = date(2020, 1, 15)
    builder.work_and_education.present.monthly_income_local = "500000"
    builder.work_and_education.present.duties = "SOFTWARE DEVELOPMENT AND SYSTEM ARCHITECTURE"
    
    # Security and Background - all default to "No"
    security = builder.add_security_and_background()
    
    # If you need to change any security answers from default "No":
    # security.part1_medical_and_health.has_communicable_disease.answer = "Yes"
    # security.part1_medical_and_health.has_communicable_disease.explanation = "Explanation here"
    
    return builder

def main():
    """Demonstrate the DS160DataBuilder usage."""
    print("Creating DS-160 application with full IDE support...")
    
    # Create the application
    builder = create_sample_application()
    
    # Validate the data
    print("\nValidating application data...")
    issues = builder.validate()
    
    if issues:
        print(f"❌ Found {len(issues)} validation issues:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ Validation passed!")
    
    # Generate JSON
    print("\nGenerating JSON for DS-160 bot...")
    json_data = builder.generate_json()
    
    # Save to file
    output_file = "data/sample_application.json"
    builder.save_json(output_file)
    
    print(f"\n🎉 Application generated successfully!")
    print(f"   - File saved to: {output_file}")
    print(f"   - JSON sections: {len([k for k, v in json_data.items() if v is not None])}")
    print(f"   - Total fields populated: {count_populated_fields(json_data)}")
    
    # Show sample of generated data
    print(f"\nSample of generated data:")
    print(f"   Name: {builder.personal_info.surnames}, {builder.personal_info.given_names}")
    print(f"   Passport: {builder.passport_info.passport_number}")
    print(f"   Travel dates: {builder.travel_info.arrival_date} to {builder.travel_info.departure_date}")
    print(f"   Purpose: {builder.travel_info.purpose_of_trip}")

def count_populated_fields(data, count=0):
    """Count populated fields in nested data structure."""
    if isinstance(data, dict):
        for value in data.values():
            count += count_populated_fields(value)
    elif isinstance(data, list):
        for item in data:
            count += count_populated_fields(item)
    elif data is not None and data != "" and data != {}:
        count += 1
    return count

if __name__ == "__main__":
    main()