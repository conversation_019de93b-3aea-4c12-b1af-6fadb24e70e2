# DS-160 Visa Application Bot

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

A production-ready Python bot that automates filling the US DS-160 visa application form using async Playwright. Features robust error handling, comprehensive logging, session persistence, advanced CAPTCHA handling, centralized section detection, and production deployment capabilities.

## 🆕 Recent Updates (August 2025)

- **Fixed Security Background Parts 2-5**: Complete selector overhaul with real HTML analysis
- **Centralized Section Detection**: Dynamic page detection instead of hardcoded "start" section
- **Enhanced CAPTCHA Handling**: Support for manual CAPTCHA solving scenarios
- **Improved Session Management**: Real-time session state updates with current page tracking
- **Data Model Corrections**: Fixed camelCase to snake_case conversions for government forms
- **Comprehensive Error Handling**: Better validation error reporting and recovery

## ⚠️ Legal Disclaimer

**WARNING: This tool automates government forms and may violate the US State Department's terms of service. Use at your own risk and legal responsibility. Always manually review all filled information before submission.**

- Automating DS-160 forms may result in application rejection or legal consequences
- This tool is for educational and research purposes only
- Users are responsible for compliance with all applicable laws and terms of service
- Manual verification of all filled data is strongly recommended

## ✨ Features

### 🔧 Core Functionality

- **Async Playwright Automation**: Fast, reliable browser automation with multiple browser support
- **Comprehensive Data Validation**: Pydantic-based data models with extensive validation
- **Multi-page Form Handling**: Supports all DS-160 sections (personal, travel, family, work/education, security)
- **Advanced CAPTCHA Handling**: Automatic detection with manual solving support and empty input handling
- **Smart Session Management**: Resume interrupted sessions with real-time page state tracking
- **Screenshot Documentation**: Automatic screenshot capture for debugging and records

### 🛡️ Production-Ready Features

- **Error Handling & Retries**: Exponential backoff with configurable retry policies
- **Comprehensive Logging**: Structured logging with file and console output
- **Configuration Management**: YAML configuration with environment variable overrides
- **Data Privacy**: Sensitive data masking in logs
- **Containerization**: Docker and docker-compose support
- **Security**: Non-root container execution, input validation, XSS protection

### 🧪 Quality Assurance

- **Unit Tests**: Comprehensive test coverage with pytest
- **Data Validation**: Schema validation for all input data
- **Integration Tests**: End-to-end testing capabilities
- **Type Safety**: Full type hints throughout codebase
- **Code Quality**: Follows Python best practices

## 🚀 Quick Start

### Prerequisites

- Python 3.10 or higher
- pip package manager
- Git (for cloning)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd visa-ds160
   ```

2. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers**

   ```bash
   playwright install chromium
   ```

4. **Initialize configuration**

   ```bash
   python main.py init-config
   ```

5. **Create example data file**

   ```bash
   python main.py create-example
   ```

6. **Edit your data file**
   ```bash
   # Edit data/example_data.json with your actual information
   vim data/example_data.json
   ```

### Basic Usage

1. **Validate your data**

   ```bash
   python main.py validate --data-file data/your_data.json
   ```

2. **Run the bot (dry run)**

   ```bash
   python main.py run --data-file data/your_data.json --dry-run
   ```

3. **Run with browser visible (for debugging)**
   ```bash
   python main.py run --data-file data/your_data.json --headful
   ```

## 📖 Detailed Documentation

### Data File Format

The bot uses a comprehensive JSON file containing your personal information. The structure includes all DS-160 sections with proper field mappings:

#### Basic Structure
```json
{
  "applicationInfo": {
    "formType": "DS-160",
    "applicationId": "AA00EVJQY3"
  },
  "personalInfo": {
    "surnames": "IVANOV IVANOVICH",
    "givenNames": "IVANOV IVAN IVANOVICH",
    "fullNameNativeAlphabet": "ИВАНОВ ИВАН ИВАНОВИЧ",
    "otherNamesUsed": [
      {
        "otherSurnames": "PETROV",
        "otherGivenNames": "VASILII"
      }
    ],
    "telecodeRepresentsName": "No",
    "sex": "MALE",
    "maritalStatus": "SINGLE",
    "dateOfBirth": "1999-06-12",
    "placeOfBirth": {
      "city": "ASTANA",
      "stateProvince": "AKMOLA",
      "countryRegion": "KAZAKHSTAN"
    }
  },
  "passportInfo": {
    "passportType": "REGULAR",
    "passportNumber": "N435RTB",
    "issuingCountry": "KAZAKHSTAN",
    "issuingCity": "ASTANA",
    "issuingStateProvince": "AKMOLA",
    "issuanceDate": "2025-06-10",
    "expirationDate": "2030-05-04",
    "hasLostOrStolenPassport": {
      "answer": "Yes",
      "lostPassportNumber": "2345678",
      "lostPassportIssuingCountry": "KAZAKHSTAN",
      "explanation": "Lost during travel"
    }
  }
}
```

#### Security Background Sections ⚠️ IMPORTANT

The Security Background sections use **camelCase** field names in JSON (matching DS-160 forms) that are automatically converted to snake_case internally:

```json
{
  "securityBackground": {
    "part2Criminal": {
      "hasBeenArrested": {
        "answer": "No",
        "explanation": ""
      },
      "hasViolatedControlledSubstanceLaws": {
        "answer": "No",
        "explanation": ""
      },
      "engagedInProstitution": {
        "answer": "No",
        "explanation": ""
      },
      "engagedInMoneyLaundering": {
        "answer": "No",
        "explanation": ""
      },
      "committedHumanTrafficking": {
        "answer": "No",
        "explanation": ""
      },
      "aidedHumanTrafficking": {
        "answer": "No",
        "explanation": ""
      },
      "isFamilyOfHumanTrafficker": {
        "answer": "No",
        "explanation": ""
      }
    },
    "part5Miscellaneous": {
      "withheldCustodyOfUSCitizenChild": {
        "answer": "No",
        "explanation": ""
      },
      "votedInUSIllegally": {
        "answer": "No",
        "explanation": ""
      },
      "renouncedUSCitizenshipForTax": {
        "answer": "No",
        "explanation": ""
      },
      "attendedPublicSchoolOnStudentVisaWithoutReimbursement": {
        "answer": "No",
        "explanation": ""
      }
    }
  }
}
```

**Note**: Complex field names like `withheldCustodyOfUSCitizenChild` and `votedInUSIllegally` require exact camelCase spelling to work properly with the automatic conversion system.

### CLI Commands

#### `run` - Run the DS-160 bot

```bash
python main.py run [OPTIONS]

Options:
  -d, --data-file TEXT    JSON data file path
  -r, --resume           Resume from previous session
  --dry-run              Run without submitting (recommended)
  --headful              Show browser window (for debugging)
```

#### `validate` - Validate data file

```bash
python main.py validate --data-file data/your_data.json
```

#### `create-example` - Create example data file

```bash
python main.py create-example --output data/my_data.json
```

#### `doctor` - Check system requirements

```bash
python main.py doctor
```

#### `version` - Show version information

```bash
python main.py version
```

### Configuration

Configuration is managed through YAML files and environment variables:

#### config/config.yaml

```yaml
browser:
  headless: true
  browser_type: chrome # chrome, chromium, firefox, webkit
  timeout: 30000

security:
  auto_submit: false # NEVER enable unless you understand the risks
  save_screenshots: true
  mask_sensitive_data: true

logging:
  level: INFO
  log_to_file: true
  log_file: logs/ds160_bot.log
```

#### Environment Variables (.env)

```bash
HEADLESS=true
AUTO_SUBMIT=false
DATA_FILE=data/your_data.json
LOG_LEVEL=INFO
```

### Error Handling

The bot includes comprehensive error handling:

- **Network Errors**: Automatic retries with exponential backoff
- **Element Not Found**: Robust selectors with fallback options
- **CAPTCHA Detection**: Automatic detection with manual intervention
- **Session Timeouts**: Automatic session persistence and recovery
- **Form Validation Errors**: Detailed error reporting and screenshots

### Session Management

Sessions are automatically saved with real-time page tracking and can be resumed:

```bash
# Resume from previous session
python main.py run --resume

# Session files are saved to session_data/
ls session_data/

# Check current session state
cat session_state.json
```

**Enhanced Session Features**:
- **Real-time page detection**: Session state shows actual DS-160 page (e.g., "security_background_5")
- **Automatic state updates**: Current page is updated as bot navigates through forms
- **Centralized section detection**: No more "Unknown section: start" errors
- **Smart recovery**: Bot can resume from any DS-160 page, not just the beginning

## 🐳 Docker Deployment

### Using Docker

1. **Build the image**

   ```bash
   docker build -t ds160-bot .
   ```

2. **Run with your data**
   ```bash
   docker run -v $(pwd)/data:/app/data:ro ds160-bot run --data-file /app/data/your_data.json
   ```

### Using Docker Compose

1. **Edit docker-compose.yml** to point to your data file

2. **Run in headless mode**

   ```bash
   docker-compose up ds160-bot
   ```

3. **Run in debug mode (with GUI)**
   ```bash
   docker-compose --profile debug up ds160-bot-debug
   ```

## 🧪 Testing

### Run Unit Tests

```bash
pytest tests/ -v
```

### Run Tests with Coverage

```bash
pytest tests/ --cov=src --cov-report=html
```

### Run Integration Tests

```bash
pytest tests/integration/ -v --headful
```

## 🛠️ Development

### Project Structure

```
visa-ds160/
├── src/                    # Source code
│   ├── bot.py             # Main bot class with centralized section detection
│   ├── config.py          # Configuration management
│   ├── data_loader.py     # Data loading, validation, and camelCase conversion
│   ├── form_filler.py     # Form filling logic with real HTML selectors
│   ├── selectors.py       # CSS/XPath selectors
│   └── utils.py           # Utility functions
├── tests/                  # Test suite
├── config/                 # Configuration files
├── data/                   # Data files (use my_comprehensive_data.json as template)
├── docs/                   # Documentation and analysis files
│   └── routing-analysis.md # Comprehensive routing architecture documentation
├── logs/                   # Log files
├── screenshots/            # Screenshots
├── session_data/           # Session persistence files
├── main.py                # CLI entry point
├── requirements.txt        # Python dependencies
├── Dockerfile             # Container definition
├── CLAUDE.md              # Development instructions and patterns
└── README.md              # This file
```

### Adding New Form Sections

1. **Update data models** in `src/data_loader.py` (add camelCase to snake_case conversion if needed)
2. **Add selectors** in `src/selectors.py` or `config/selectors.yaml` (use real HTML analysis)
3. **Implement form filling logic** in `src/form_filler.py` (use structured dictionary approach)
4. **Update section detection** in `bot.py` `_get_current_section()` method
5. **Add validation** and error handling
6. **Write tests** in `tests/`

**Important**: Always analyze real DS-160 HTML before adding selectors. Use browser DevTools to find exact field names and avoid assumptions.

## 🔒 Security Background Implementation Details

### Real HTML Selector Patterns

The bot uses **actual DS-160 form selectors** discovered through HTML analysis, not assumptions:

```javascript
// Security Part 2 - Criminal Background  
"input[name='ctl00$SiteContentPlaceHolder$FormView1$rblArrested'][value='N']"           // Arrested question
"input[name='ctl00$SiteContentPlaceHolder$FormView1$rblAssistedSevereTrafficking'][value='N']" // Trafficking
"textarea[name='ctl00$SiteContentPlaceHolder$FormView1$tbxArrested_EXPL']"              // Explanation field

// Security Part 5 - Miscellaneous
"input[id='ctl00_SiteContentPlaceHolder_FormView1_rblChildCustody_1']"                  // Child custody (No)
"input[id='ctl00_SiteContentPlaceHolder_FormView1_rblVotingViolation_1']"               // Illegal voting (No)  
"input[id='ctl00_SiteContentPlaceHolder_FormView1_rblRenounceExp_1']"                   // Tax avoidance (No)
```

### Structured Form Filling Approach

Instead of individual field loops, the bot uses **structured dictionaries** for reliable form filling:

```python
questions = {
    "arrested": {
        "data": security_data.has_been_arrested,
        "radio_selectors": {
            "yes": ["input[name*='rblArrested'][value='Y']", "input[id*='rblArrested_0']"],
            "no": ["input[name*='rblArrested'][value='N']", "input[id*='rblArrested_1']"]
        },
        "explanation_selectors": ["textarea[name*='tbxArrested_EXPL']", "textarea[id*='tbxArrested_EXPL']"]
    }
}
```

This approach provides:
- **Progressive fallback**: Multiple selectors per field
- **Centralized logic**: One method handles all question types  
- **Real field mapping**: Based on actual DS-160 HTML structure
- **Reliable error handling**: Built-in validation and screenshots

### CAPTCHA Handling Improvements

Enhanced CAPTCHA workflow supports real-world scenarios:

1. **Automatic Detection**: Bot detects CAPTCHA fields automatically
2. **Manual Solving**: User solves CAPTCHA in visible browser
3. **Empty Input Handling**: Press Enter if CAPTCHA already solved manually
4. **Continuation Logic**: Bot continues from current page state

```bash
# Example CAPTCHA workflow
CAPTCHA detected. Please solve it manually in the browser.
Enter CAPTCHA text (or press Enter if already solved): [Press Enter]
⏭️ No CAPTCHA text provided - assuming CAPTCHA was solved manually, continuing...
```

### Customizing Selectors

Selectors can be customized in `config/selectors.yaml`:

```yaml
personal:
  surname:
    css: "input[name='Surname']"
    wait_for: true
    timeout: 10000
  gender:
    male: "input[name='Gender'][value='MALE']"
    female: "input[name='Gender'][value='FEMALE']"
```

## 🔧 Troubleshooting

### Common Issues

1. **Browser fails to start**

   ```bash
   # Install browser dependencies
   playwright install-deps chromium
   ```

2. **Elements not found**

   - Check if selectors in `config/selectors.yaml` are up to date
   - Enable headful mode to see the browser: `--headful`
   - Check screenshots in `screenshots/` directory

3. **CAPTCHA problems**

   - CAPTCHAs require manual intervention
   - Use headful mode and solve manually
   - **NEW**: Empty CAPTCHA input is treated as "already solved manually" - just press Enter
   - When prompted for CAPTCHA text, solve it visually and enter the text, or press Enter if already solved
   - Consider CAPTCHA solving services (not recommended)

4. **Data validation errors**

   ```bash
   python main.py validate --data-file data/your_data.json
   ```

5. **Session recovery issues**
   - Check `session_data/` directory
   - Delete session files to start fresh
   - Ensure proper permissions

### Debugging

1. **Enable verbose logging**

   ```bash
   python main.py run --verbose
   ```

2. **Run in headful mode**

   ```bash
   python main.py run --headful
   ```

3. **Check log files**

   ```bash
   tail -f logs/ds160_bot.log
   ```

4. **Review screenshots**
   - Screenshots are automatically taken on errors
   - Check `screenshots/` directory

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest pytest-cov black isort mypy

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Format code
black src/ tests/
isort src/ tests/

# Type checking
mypy src/
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚖️ Legal Notice

This software is provided for educational and research purposes only. The developers are not responsible for any misuse or legal consequences resulting from the use of this software. Users must comply with all applicable laws and terms of service.

## 🙏 Acknowledgments

- [Playwright](https://playwright.dev/) for excellent browser automation
- [Pydantic](https://pydantic-docs.helpmanual.io/) for data validation
- [Click](https://click.palletsprojects.com/) for CLI framework
- [Loguru](https://github.com/Delgan/loguru) for logging

---

**Remember: Always manually review all filled information before submitting your DS-160 application!**
