#!/usr/bin/env python3
"""
Пример улучшенной PersonalInfo модели с условной валидацией на основе marital_status.

Показывает как в Pydantic сделать так, чтобы разные поля требовались в зависимости 
от выбранного семейного положения.
"""

from datetime import date
from typing import List, Optional
from pydantic import BaseModel, Field, model_validator
from enum import Enum

class Gender(str, Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"

class MaritalStatus(str, Enum):
    """Семейное положение с кодами DS-160."""
    SINGLE = "S"
    MARRIED = "M"
    DIVORCED = "D" 
    WIDOWED = "W"
    LEGALLY_SEPARATED = "L"
    COMMON_LAW = "C"
    CIVIL_UNION = "P"
    OTHER = "O"

class AddressType(str, Enum):
    HOME = "H"
    MAILING = "M"
    US_CONTACT = "U"
    DO_NOT_KNOW = "D"
    OTHER = "O"

class PlaceOfBirth(BaseModel):
    """Place of birth information."""
    city: str = Field(..., min_length=1, max_length=100)
    state_province: Optional[str] = Field(None, min_length=0)
    country_region: str = Field(..., min_length=1, max_length=100)

class OtherNamesUsed(BaseModel):
    """Other names used information."""
    other_surnames: str = Field(..., min_length=1, max_length=100)
    other_given_names: str = Field(..., min_length=1, max_length=100)

class Address(BaseModel):
    """Address information."""
    street_line1: str = Field(..., min_length=1, max_length=200)
    street_line2: Optional[str] = Field(None, max_length=200)
    city: str = Field(..., min_length=1, max_length=100)
    state_province: Optional[str] = Field(None, max_length=100)
    postal_zone_zip_code: str = Field(..., min_length=1, max_length=20)
    country_region: str = Field(..., min_length=1, max_length=100)

class SpouseInfo(BaseModel):
    """Информация о супруге для MARRIED/COMMON_LAW/CIVIL_UNION."""
    surnames: str = Field(..., min_length=1, max_length=100, description="Фамилии супруга")
    given_names: str = Field(..., min_length=1, max_length=100, description="Имена супруга")
    date_of_birth: date = Field(..., description="Дата рождения супруга")
    place_of_birth: PlaceOfBirth = Field(..., description="Место рождения супруга")
    nationality: str = Field(..., min_length=1, max_length=100, description="Национальность супруга")
    address_type: AddressType = Field(default=AddressType.HOME, description="Тип адреса супруга")
    address: Optional[Address] = Field(None, description="Адрес супруга (только если address_type=OTHER)")

    @model_validator(mode='after')
    def validate_spouse_address(self):
        """Проверяем что если address_type=OTHER, то адрес обязателен."""
        if self.address_type == AddressType.OTHER and not self.address:
            raise ValueError("Address is required when address_type is 'OTHER'")
        if self.address_type != AddressType.OTHER and self.address:
            # Автоматически устанавливаем OTHER если есть адрес
            self.address_type = AddressType.OTHER
        return self

class DeceasedSpouseInfo(BaseModel):
    """Информация об умершем супруге для WIDOWED."""
    surnames: Optional[str] = Field(None, min_length=1, max_length=100, description="Фамилии умершего супруга")
    given_names: Optional[str] = Field(None, min_length=1, max_length=100, description="Имена умершего супруга")
    date_of_birth: Optional[date] = Field(None, description="Дата рождения умершего супруга")
    nationality: Optional[str] = Field(None, min_length=1, max_length=100, description="Национальность умершего супруга")
    place_of_birth_city: Optional[str] = Field(None, max_length=100, description="Город рождения")
    place_of_birth_city_unknown: bool = Field(default=False, description="Не знаю город рождения")
    place_of_birth_country: Optional[str] = Field(None, min_length=1, max_length=100, description="Страна рождения")

class FormerSpouseInfo(BaseModel):
    """Информация о бывшем супруге."""
    surnames: str = Field(..., min_length=1, max_length=100, description="Фамилии бывшего супруга")
    given_names: str = Field(..., min_length=1, max_length=100, description="Имена бывшего супруга")
    date_of_birth: date = Field(..., description="Дата рождения бывшего супруга")
    nationality: str = Field(..., min_length=1, max_length=100, description="Национальность бывшего супруга")
    date_of_marriage: date = Field(..., description="Дата брака")
    date_marriage_ended: date = Field(..., description="Дата окончания брака")
    how_marriage_ended: str = Field(..., max_length=4000, description="Как закончился брак")
    country_marriage_terminated: str = Field(..., min_length=1, max_length=100, description="Страна где брак был расторгнут")

class DivorceInfo(BaseModel):
    """Информация о разводе для DIVORCED."""
    number_of_former_spouses: int = Field(..., ge=1, le=10, description="Количество бывших супругов")
    former_spouses: List[FormerSpouseInfo] = Field(..., min_items=1, description="Список бывших супругов")

    @model_validator(mode='after')
    def validate_former_spouses_count(self):
        """Проверяем что количество супругов соответствует списку."""
        if len(self.former_spouses) != self.number_of_former_spouses:
            raise ValueError(f"Number of former spouses ({self.number_of_former_spouses}) must match the list length ({len(self.former_spouses)})")
        return self

class PersonalInfoImproved(BaseModel):
    """
    Улучшенная Personal information с условной валидацией на основе marital_status.
    
    Автоматически требует разные поля в зависимости от семейного положения:
    - MARRIED/COMMON_LAW/CIVIL_UNION: требует spouse
    - WIDOWED: требует deceased_spouse  
    - DIVORCED: требует divorce_info
    - OTHER: требует marital_status_other_explanation
    """
    model_config = {"populate_by_name": True}
    
    # Основные поля
    surnames: str = Field(..., min_length=1, max_length=100, description="Family name/surname")
    given_names: str = Field(..., min_length=1, max_length=100, description="Given names")
    full_name_native_alphabet: Optional[str] = Field(None, description="Full name in native alphabet")
    other_names_used: List[OtherNamesUsed] = Field(default_factory=list, description="Other names used")
    telecode_represents_name: str = Field(default="No", description="Does telecode represent name")
    sex: Gender = Field(..., description="Gender")
    marital_status: MaritalStatus = Field(..., description="Marital status")
    date_of_birth: date = Field(..., description="Date of birth")
    place_of_birth: Optional[PlaceOfBirth] = Field(None, description="Place of birth")
    
    # Условные поля в зависимости от marital_status
    marital_status_other_explanation: Optional[str] = Field(
        None, 
        max_length=4000, 
        description="Объяснение семейного статуса (ОБЯЗАТЕЛЬНО для OTHER)"
    )
    spouse: Optional[SpouseInfo] = Field(
        None, 
        description="Информация о супруге (ОБЯЗАТЕЛЬНО для MARRIED/COMMON_LAW/CIVIL_UNION)"
    )
    deceased_spouse: Optional[DeceasedSpouseInfo] = Field(
        None, 
        description="Информация об умершем супруге (ОБЯЗАТЕЛЬНО для WIDOWED)"
    )
    divorce_info: Optional[DivorceInfo] = Field(
        None, 
        description="Информация о разводе (ОБЯЗАТЕЛЬНО для DIVORCED)"
    )

    @model_validator(mode='after')
    def validate_marital_status_fields(self):
        """
        Условная валидация полей на основе marital_status.
        
        Автоматически проверяет что нужные поля заполнены для каждого статуса.
        """
        errors = []
        
        # Статусы которые требуют информацию о супруге
        married_statuses = {MaritalStatus.MARRIED, MaritalStatus.COMMON_LAW, MaritalStatus.CIVIL_UNION}
        
        if self.marital_status in married_statuses:
            if not self.spouse:
                errors.append(f"Spouse information is required for marital status '{self.marital_status.name}'")
            
            # Очистка несовместимых полей
            if self.deceased_spouse:
                errors.append(f"Cannot have deceased spouse info when marital status is '{self.marital_status.name}'")
            if self.divorce_info:
                errors.append(f"Cannot have divorce info when marital status is '{self.marital_status.name}'")
        
        elif self.marital_status == MaritalStatus.WIDOWED:
            if not self.deceased_spouse:
                errors.append("Deceased spouse information is required for WIDOWED status")
            
            # Очистка несовместимых полей
            if self.spouse:
                errors.append("Cannot have current spouse info when marital status is WIDOWED")
            if self.divorce_info:
                errors.append("Cannot have divorce info when marital status is WIDOWED")
        
        elif self.marital_status == MaritalStatus.DIVORCED:
            if not self.divorce_info:
                errors.append("Divorce information is required for DIVORCED status")
            
            # Очистка несовместимых полей
            if self.spouse:
                errors.append("Cannot have current spouse info when marital status is DIVORCED")
            if self.deceased_spouse:
                errors.append("Cannot have deceased spouse info when marital status is DIVORCED")
        
        elif self.marital_status == MaritalStatus.OTHER:
            if not self.marital_status_other_explanation:
                errors.append("Explanation is required when marital status is OTHER")
            if not self.marital_status_other_explanation.strip():
                errors.append("Marital status explanation cannot be empty for OTHER status")
        
        elif self.marital_status in {MaritalStatus.SINGLE, MaritalStatus.LEGALLY_SEPARATED}:
            # Для SINGLE и LEGALLY_SEPARATED дополнительная информация не требуется
            # Но предупреждаем если есть несовместимые поля
            warnings = []
            if self.spouse:
                warnings.append(f"Current spouse info is unusual for {self.marital_status.name} status")
            if self.deceased_spouse:
                warnings.append(f"Deceased spouse info is unusual for {self.marital_status.name} status")
            if self.divorce_info:
                warnings.append(f"Divorce info is unusual for {self.marital_status.name} status")
            
            # В реальном приложении можно логировать warnings
            # print("Warnings:", warnings) if warnings else None
        
        # Общие проверки
        if self.marital_status != MaritalStatus.OTHER and self.marital_status_other_explanation:
            errors.append("Marital status explanation should only be provided for OTHER status")
        
        if errors:
            raise ValueError("; ".join(errors))
        
        return self

    @classmethod
    def create_single(cls, surnames: str, given_names: str, sex: Gender, date_of_birth: date) -> 'PersonalInfoImproved':
        """Helper: создать для одинокого человека."""
        return cls(
            surnames=surnames,
            given_names=given_names,
            sex=sex,
            marital_status=MaritalStatus.SINGLE,
            date_of_birth=date_of_birth
        )

    @classmethod
    def create_married(cls, surnames: str, given_names: str, sex: Gender, 
                      date_of_birth: date, spouse: SpouseInfo) -> 'PersonalInfoImproved':
        """Helper: создать для женатого/замужней."""
        return cls(
            surnames=surnames,
            given_names=given_names,
            sex=sex,
            marital_status=MaritalStatus.MARRIED,
            date_of_birth=date_of_birth,
            spouse=spouse
        )

# Примеры использования
def test_conditional_validation():
    """Тестируем условную валидацию."""
    print("🧪 Тестируем условную валидацию PersonalInfo...")
    
    # ✅ Правильный SINGLE
    try:
        single_person = PersonalInfoImproved.create_single(
            surnames="SMITH",
            given_names="JOHN",
            sex=Gender.MALE,
            date_of_birth=date(1990, 1, 1)
        )
        print("✅ SINGLE person - OK")
    except Exception as e:
        print(f"❌ SINGLE person failed: {e}")
    
    # ❌ MARRIED без информации о супруге
    try:
        PersonalInfoImproved(
            surnames="SMITH",
            given_names="JOHN",
            sex=Gender.MALE,
            marital_status=MaritalStatus.MARRIED,
            date_of_birth=date(1990, 1, 1)
            # spouse=None - НЕ УКАЗАН!
        )
        print("❌ MARRIED without spouse - should have failed!")
    except Exception as e:
        print(f"✅ MARRIED without spouse correctly failed: {e}")
    
    # ✅ Правильный MARRIED с информацией о супруге
    try:
        spouse = SpouseInfo(
            surnames="SMITH",
            given_names="JANE",
            date_of_birth=date(1992, 5, 10),
            place_of_birth=PlaceOfBirth(
                city="NEW YORK",
                country_region="USA"
            ),
            nationality="AMERICAN"
        )
        
        married_person = PersonalInfoImproved.create_married(
            surnames="SMITH",
            given_names="JOHN", 
            sex=Gender.MALE,
            date_of_birth=date(1990, 1, 1),
            spouse=spouse
        )
        print("✅ MARRIED with spouse - OK")
    except Exception as e:
        print(f"❌ MARRIED with spouse failed: {e}")
    
    # ❌ OTHER без объяснения
    try:
        PersonalInfoImproved(
            surnames="SMITH",
            given_names="JOHN",
            sex=Gender.MALE,
            marital_status=MaritalStatus.OTHER,
            date_of_birth=date(1990, 1, 1)
            # marital_status_other_explanation=None - НЕ УКАЗАНО!
        )
        print("❌ OTHER without explanation - should have failed!")
    except Exception as e:
        print(f"✅ OTHER without explanation correctly failed: {e}")
    
    # ✅ Правильный OTHER с объяснением
    try:
        PersonalInfoImproved(
            surnames="SMITH",
            given_names="JOHN",
            sex=Gender.MALE,
            marital_status=MaritalStatus.OTHER,
            date_of_birth=date(1990, 1, 1),
            marital_status_other_explanation="DOMESTIC PARTNERSHIP"
        )
        print("✅ OTHER with explanation - OK")
    except Exception as e:
        print(f"❌ OTHER with explanation failed: {e}")
    
    # ❌ WIDOWED без информации об умершем супруге
    try:
        PersonalInfoImproved(
            surnames="SMITH",
            given_names="JOHN",
            sex=Gender.MALE,
            marital_status=MaritalStatus.WIDOWED,
            date_of_birth=date(1990, 1, 1)
            # deceased_spouse=None - НЕ УКАЗАНО!
        )
        print("❌ WIDOWED without deceased spouse - should have failed!")
    except Exception as e:
        print(f"✅ WIDOWED without deceased spouse correctly failed: {e}")
    
    print("\n🎉 Условная валидация работает правильно!")

if __name__ == "__main__":
    test_conditional_validation()