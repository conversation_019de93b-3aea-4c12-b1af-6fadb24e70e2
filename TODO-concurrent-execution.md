# TODO: Поддержка одновременного запуска нескольких экземпляров

## Проблема
В настоящее время невозможно запустить несколько экземпляров DS-160 бота одновременно из-за конфликтов при доступе к общим ресурсам.

## Анализ причин конфликтов

### 1. 🚨 КРИТИЧНО: Жестко заданные пути к файлам сессий
**Проблема**: Все экземпляры записывают в одни и те же файлы сессий:
- `session_data/session_state.json` 
- `session_data/browser_state.json`
- `session_data/application_data.json`

**Доказательство конфликта**: Найдены разные Application ID в файлах:
- Корневой каталог: `AA24FE51C3D05E` (обновлен: 2025-08-02T09:12:17)
- session_data/: `AAD2AAF6504379` (обновлен: 2025-08-02T09:09:04)

**Местоположение в коде**: 
```python
# src/bot.py строки 64, 68 - жестко заданные пути по умолчанию
if not self.browser_options.get('browser_state_file'):
    self.browser_options['browser_state_file'] = str(Path("session_data") / "browser_state.json")

if not self.browser_options.get('session_state_file'):
    self.browser_options['session_state_file'] = str(Path("session_data") / "session_state.json")
```

### 2. Конфликт каталога скриншотов
**Проблема**: Все экземпляры сохраняют скриншоты в один каталог `screenshots/` с именованием только по времени
**Риск**: Перезапись скриншотов и путаница при отладке
**Местоположение**: `src/utils.py` строки 216-224

### 3. Общий файл логов
**Проблема**: Несколько экземпляров записывают в один `logs/ds160_bot.log`
**Риск**: Повреждение логов и сложности отладки  
**Местоположение**: `src/bot.py` строки 86-88, конфигурация: `logs/ds160_bot.log`

### 4. ✅ Управление браузерными процессами - НЕ ПРОБЛЕМА
**Анализ**: Playwright автоматически управляет процессами браузера и распределением портов
**Хорошие новости**: Жестко заданных портов не найдено - Playwright корректно это обрабатывает
**Паттерны-одиночки**: Эксклюзивных блокировок или mutex в коде не обнаружено

## Предлагаемые решения

### Решение 1: Каталоги для конкретных экземпляров (Рекомендуется)
Создание уникальных рабочих каталогов для каждого экземпляра используя PID процесса + временную метку:

```python
# Новая логика изоляции экземпляров
import os
import time

instance_id = f"{os.getpid()}_{int(time.time())}"
base_dirs = {
    'session_data': f"session_data_{instance_id}",
    'screenshots': f"screenshots_{instance_id}",
    'logs': f"logs_{instance_id}"
}
```

### Решение 2: Переопределение через параметры CLI
Добавить опции командной строки для пользовательских путей:
```bash
python main.py run --session-dir session_data_instance1 --screenshots-dir screenshots_instance1
python main.py run --session-dir session_data_instance2 --screenshots-dir screenshots_instance2
```

### Решение 3: Переопределение через переменные окружения
Использовать переменные окружения для конфигурации путей:
```bash
DS160_SESSION_DIR=session_data_1 DS160_SCREENSHOTS_DIR=screenshots_1 python main.py run
DS160_SESSION_DIR=session_data_2 DS160_SCREENSHOTS_DIR=screenshots_2 python main.py run
```

## План реализации

### Этап 1: Основные изменения
1. **Изменить `src/bot.py`**: 
   - Обновить `_setup_directories()` для путей конкретных экземпляров
   - Обновить `_setup_session_paths()` для динамических путей
   
2. **Обновить `src/utils.py`**: 
   - Сделать путь скриншотов настраиваемым
   - Добавить поддержку префикса экземпляра

3. **Расширить `main.py`**: 
   - Добавить CLI опции для переопределения каталогов
   - Добавить автоматическое создание instance_id

4. **Обновить систему конфигурации**: 
   - Поддержка переопределения через переменные окружения
   - Валидация конфигурации путей

### Этап 2: Дополнительные улучшения  
5. **Добавить обнаружение экземпляров**: 
   - Предотвращение случайного использования одного каталога
   - Предупреждения о конфликтах
   
6. **Улучшить логирование**:
   - Префиксы экземпляров в логах
   - Раздельные файлы логов

## Файлы для изменения

- `src/bot.py` - Настройка сессий и каталогов
- `src/utils.py` - Обработка путей скриншотов  
- `main.py` - Парсинг аргументов CLI
- `src/config.py` - Поддержка переменных окружения

## Ожидаемый результат

После реализации:
✅ Несколько экземпляров могут работать одновременно
✅ Каждый экземпляр имеет изолированные данные сессии
✅ Нет конфликтов файлов или перезаписи
✅ Чистая отладка с разделенными логами/скриншотами  
✅ Сохранена обратная совместимость

## Приоритет: ВЫСОКИЙ
Эта функция критически важна для:
- Обработки нескольких заявлений параллельно
- Тестирования различных сценариев одновременно
- Повышения производительности автоматизации

## Статус: 📋 ПЛАНИРУЕТСЯ
- [ ] Реализовать Решение 1 (каталоги экземпляров)
- [ ] Добавить CLI опции
- [ ] Добавить поддержку переменных окружения  
- [ ] Протестировать одновременный запуск
- [ ] Обновить документацию