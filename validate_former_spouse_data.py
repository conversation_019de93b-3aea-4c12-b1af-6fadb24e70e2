#!/usr/bin/env python3
"""
Data Model Validation Test for Former Spouse Fields
Tests that JSON data correctly maps to Python data model attributes.
"""

import json
import sys
from pathlib import Path
from typing import List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from data_loader import DataLoader, DS160Data

async def test_former_spouse_field_mapping(json_file_path: str) -> bool:
    """Test that former spouse fields map correctly from JSON to Python models."""
    
    print(f"🧪 Testing former spouse data validation for: {json_file_path}")
    
    try:
        # Load and validate data
        loader = DataLoader(json_file_path)
        data = await loader.load_data()
        
        # Check if we have former spouse data
        if not hasattr(data.personal_info, 'former_spouses') or not data.personal_info.former_spouses:
            print("ℹ️  No former spouse data found - skipping validation")
            return True
            
        former_spouses = data.personal_info.former_spouses
        print(f"📋 Found {len(former_spouses)} former spouse(s)")
        
        # Test each former spouse
        all_tests_passed = True
        
        for i, spouse in enumerate(former_spouses, 1):
            print(f"\n👥 Testing Former Spouse {i}:")
            
            # Test required fields access
            tests = [
                ("surnames", spouse.surnames),
                ("given_names", spouse.given_names), 
                ("date_of_birth", spouse.date_of_birth),
                ("nationality", spouse.nationality),
                ("date_of_marriage", spouse.date_of_marriage),
                ("date_marriage_ended", spouse.date_marriage_ended),
                ("how_marriage_ended", spouse.how_marriage_ended),
                ("marriage_termination_country", spouse.marriage_termination_country),  # KEY FIX
            ]
            
            for field_name, field_value in tests:
                if field_value is not None:
                    print(f"  ✅ {field_name}: {field_value}")
                else:
                    print(f"  ⚠️  {field_name}: None (optional field)")
            
            # Test birth place access
            if hasattr(spouse, 'place_of_birth') and spouse.place_of_birth:
                print(f"  ✅ place_of_birth.city: {spouse.place_of_birth.city}")
                print(f"  ✅ place_of_birth.country_region: {spouse.place_of_birth.country_region}")
            else:
                print(f"  ⚠️  place_of_birth: None (optional)")
                
        print(f"\n🎯 Validation Result: {'PASSED' if all_tests_passed else 'FAILED'}")
        return all_tests_passed
        
    except AttributeError as e:
        print(f"❌ FIELD ACCESS ERROR: {e}")
        if 'country_marriage_terminated' in str(e):
            print(f"💡 HINT: This error suggests the old field name is still being used")
        return False
        
    except Exception as e:
        print(f"❌ DATA VALIDATION ERROR: {e}")
        return False

def search_for_field_usage(field_name: str) -> List[str]:
    """Search for usage of old field names in JSON files."""
    
    print(f"\n🔍 Searching for '{field_name}' usage in JSON files...")
    
    data_dir = Path(__file__).parent / "data"
    if not data_dir.exists():
        print("❌ Data directory not found")
        return []
    
    found_files = []
    
    for json_file in data_dir.glob("*.json"):
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if field_name in content:
                    found_files.append(str(json_file))
                    print(f"  📄 Found in: {json_file.name}")
        except Exception as e:
            print(f"  ⚠️  Error reading {json_file.name}: {e}")
    
    if not found_files:
        print(f"  ✅ No JSON files contain '{field_name}'")
    
    return found_files

async def main():
    """Run comprehensive former spouse data validation."""
    
    print("=" * 60)
    print("🧪 FORMER SPOUSE DATA MODEL VALIDATION TEST")
    print("=" * 60)
    
    # Find JSON files to test
    data_dir = Path(__file__).parent / "data"
    json_files = list(data_dir.glob("*.json")) if data_dir.exists() else []
    
    if not json_files:
        print("❌ No JSON files found in data/ directory")
        return False
    
    print(f"📁 Testing {len(json_files)} JSON file(s):")
    for f in json_files:
        print(f"  • {f.name}")
    
    # Test each file
    all_passed = True
    
    for json_file in json_files:
        try:
            file_passed = await test_former_spouse_field_mapping(str(json_file))
            all_passed = all_passed and file_passed
        except Exception as e:
            print(f"❌ Error testing {json_file.name}: {e}")
            all_passed = False
    
    # Search for old field names
    old_field_names = [
        "country_marriage_terminated",
        "countryMarriageTerminated",
    ]
    
    for old_field in old_field_names:
        found_files = search_for_field_usage(old_field)
        if found_files:
            print(f"⚠️  Found old field name '{old_field}' - these files need updating")
            all_passed = False
    
    # Final result
    print("\n" + "=" * 60)
    print(f"🎯 OVERALL TEST RESULT: {'✅ PASSED' if all_passed else '❌ FAILED'}")
    print("=" * 60)
    
    if all_passed:
        print("🚀 All former spouse data model validations passed!")
        print("📋 Field name fixes are working correctly")
    else:
        print("🔧 Some issues found - review the output above")
        
    return all_passed

if __name__ == "__main__":
    import asyncio
    success = asyncio.run(main())
    sys.exit(0 if success else 1)