#!/usr/bin/env python3
"""
Финальный пример использования DS-160 генератора с условной валидацией.

Показывает как Pydantic автоматически требует разные поля в зависимости от выбранных значений.
"""

from datetime import date
from improved_ds160_generator import (
    ImprovedDS160DataBuilder,
    PersonalInfoBuilder,
    SpouseInfoBuilder,
    PlaceOfBirthBuilder,
    NationalityAndResidenceBuilder,
    OtherNationalityBuilder,
    SponsorInfoBuilder,
    PersonSponsorBuilder,
    USAddressBuilder,
    Gender,
    MaritalStatus,
    PersonEntityPaying
)

def demo_conditional_validation():
    """Демонстрация условной валидации в действии."""
    print("🎯 DS-160 Генератор с Условной Валидацией")
    print("=" * 60)
    
    builder = ImprovedDS160DataBuilder()
    
    print("\n1️⃣ Задаем семейное положение MARRIED...")
    print("   → Автоматически потребуется информация о супруге!")
    
    # Сначала создаем информацию о супруге
    spouse = SpouseInfoBuilder(
        surnames="ПЕТРОВА", 
        given_names="АННА ВЛАДИМИРОВНА",
        date_of_birth=date(1992, 3, 15),
        place_of_birth=PlaceOfBirthBuilder(
            city="АЛМАТЫ",
            state_province="АЛМАТИНСКАЯ ОБЛАСТЬ", 
            country_region="КАЗАХСТАН"
        ),
        nationality="КАЗАХСТАН"
    )
    
    # Теперь задаем личную информацию с супругом
    personal = builder.set_personal_info(
        surnames="ИВАНОВ",
        given_names="ИВАН ИВАНОВИЧ", 
        sex=Gender.MALE,
        marital_status=MaritalStatus.MARRIED,  # ← ЭТО ТРЕБУЕТ spouse!
        date_of_birth=date(1988, 10, 5),
        spouse=spouse  # ← Обязательно для MARRIED!
    )
    
    print("   ✅ Валидация прошла - spouse указан правильно!")
    
    print("\n2️⃣ Добавляем другое гражданство с паспортом...")
    print("   → has_passport='Yes' автоматически потребует passport_number!")
    
    nationality = builder.set_nationality_and_residence(
        country_of_origin="КАЗАХСТАН",
        other_nationalities=[
            OtherNationalityBuilder(
                country="РОССИЯ",
                has_passport="Yes",  # ← ЭТО ТРЕБУЕТ passport_number!
                passport_number="*********"  # ← Обязательно для has_passport='Yes'!
            )
        ],
        is_permanent_resident_of_other_country="No"
    )
    
    print("   ✅ Валидация прошла - passport_number указан!")
    
    print("\n3️⃣ Задаем плательщика 'OTHER PERSON'...")  
    print("   → Автоматически потребуется person_sponsor!")
    
    sponsor = builder.set_sponsor_info(
        person_entity_paying=PersonEntityPaying.OTHER_PERSON,  # ← ЭТО ТРЕБУЕТ person_sponsor!
        person_sponsor=PersonSponsorBuilder(  # ← Обязательно для OTHER_PERSON!
            surnames="ДЖОНСОН",
            given_names="ДЖЕЙН",
            relationship="ДРУГ",
            address=USAddressBuilder(
                street_line1="123 MAIN STREET",
                city="НЬЮ-ЙОРК",
                state="NY", 
                zip_code="10001"
            ),
            phone="******-123-4567",
            email="<EMAIL>"
        ),
        
    )
    
    print("   ✅ Валидация прошла - person_sponsor указан!")
    
    print("\n4️⃣ Проверяем что произойдет если забыть обязательные поля...")
    
    # Тест 1: DIVORCED без divorce_info
    print("\n   🧪 Тест: DIVORCED без divorce_info")
    try:
        test_builder = ImprovedDS160DataBuilder()
        test_builder.set_personal_info(
            surnames="СМИТ",
            given_names="ДЖОН",
            sex=Gender.MALE,
            marital_status=MaritalStatus.DIVORCED,  # ← ТРЕБУЕТ divorce_info!
            date_of_birth=date(1985, 1, 1)
            # divorce_info НЕ УКАЗАН!
        )
        print("      ❌ Должна была быть ошибка!")
    except Exception as e:
        print(f"      ✅ Правильно упал: Divorce information is required")
    
    # Тест 2: has_passport='Yes' без passport_number
    print("\n   🧪 Тест: has_passport='Yes' без passport_number")
    try:
        test_builder2 = ImprovedDS160DataBuilder()
        test_builder2.set_nationality_and_residence(
            country_of_origin="КАЗАХСТАН",
            other_nationalities=[
                OtherNationalityBuilder(
                    country="РОССИЯ",
                    has_passport="Yes"  # ← ТРЕБУЕТ passport_number!
                    # passport_number НЕ УКАЗАН!
                )
            ]
        )
        print("      ❌ Должна была быть ошибка!")
    except Exception as e:
        print(f"      ✅ Правильно упал: Passport number is required")
    
    # Тест 3: OTHER_COMPANY без company_sponsor
    print("\n   🧪 Тест: OTHER_COMPANY без company_sponsor")
    try:
        test_builder3 = ImprovedDS160DataBuilder()
        test_builder3.set_sponsor_info(
            person_entity_paying=PersonEntityPaying.OTHER_COMPANY  # ← ТРЕБУЕТ company_sponsor!
            # company_sponsor НЕ УКАЗАН!
        )
        print("      ❌ Должна была быть ошибка!")
    except Exception as e:
        print(f"      ✅ Правильно упал: Company sponsor information is required")
    
    print("\n5️⃣ Генерируем финальный JSON...")
    
    # Валидация всех данных
    issues = builder.validate()
    if issues:
        print(f"   ❌ Найдены проблемы: {issues}")
    else:
        print("   ✅ Валидация всех данных прошла успешно!")
    
    # Генерация JSON
    json_data = builder.generate_json()
    builder.save_json("data/conditional_validation_demo.json")
    
    print(f"\n   📄 JSON сгенерирован с {len([k for k,v in json_data.items() if v])} секциями")
    print(f"   💾 Сохранен в: data/conditional_validation_demo.json")
    
    print("\n🎉 Демонстрация завершена!")
    print("\n" + "="*60)
    print("📋 ИТОГО: Что дает условная валидация")
    print("="*60)
    print("✅ Автоматическая проверка связанных полей")
    print("✅ Невозможно забыть обязательные поля")
    print("✅ Четкие ошибки валидации с объяснениями")
    print("✅ IDE поддержка со всеми типами")
    print("✅ Защита от логических ошибок в данных")
    print("\n🚀 Твой генератор теперь намного надежнее!")

if __name__ == "__main__":
    demo_conditional_validation()