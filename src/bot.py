"""Main DS-160 bot controller."""
import async<PERSON>
import j<PERSON>
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from patchright.async_api import async_playwright, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rowserContex<PERSON>, Page
from loguru import logger

from .config import Config
from .data_loader import <PERSON><PERSON>oa<PERSON>, DS160Data
from .selectors import DS160Selectors
from .form_filler import FormFiller
from .utils import (
    take_screenshot, wait_for_page_load, create_session_state,
    save_session_state, load_session_state, wait_for_element, safe_click, human_like_delay,
    ApplicationData, save_application_data, load_application_data, create_application_data
)

class DS160Bot:
    """Main DS-160 automation bot."""
    
    def __init__(self, config: Config, browser_options: dict = None):
        """Initialize the bot with configuration."""
        self.config = config
        self.browser_options = browser_options or {}
        self.data_loader: Optional[DataLoader] = None
        self.selectors = DS160Selectors(config.selectors_file)
        self.form_filler: Optional[FormFiller] = None
        
        # Browser objects
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # Session state
        self.session_state = create_session_state()
        self.application_id: Optional[str] = None
        
        # Application data for resume functionality
        self.application_data = create_application_data()
        
        # Setup directories
        self._setup_directories()
        
        # Setup session file paths
        self._setup_session_paths()
        
        # Setup logging
        self._setup_logging()
    
    def _setup_directories(self) -> None:
        """Create necessary directories."""
        directories = ["logs", "screenshots", "session_data"]
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def _setup_session_paths(self) -> None:
        """Setup session and browser state file paths."""
        # Set default browser state file if not specified
        if not self.browser_options.get('browser_state_file'):
            self.browser_options['browser_state_file'] = str(Path("session_data") / "browser_state.json")
        
        # Set default session state file if not specified
        if not self.browser_options.get('session_state_file'):
            self.browser_options['session_state_file'] = str(Path("session_data") / "session_state.json")
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        log_config = self.config.logging
        
        # Remove existing loguru handlers
        logger.remove()
        
        # Console logging
        logger.add(
            lambda msg: print(msg, end=""),
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=log_config.level
        )
        
        # File logging
        if log_config.log_to_file:
            logger.add(
                log_config.log_file,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                level=log_config.level,
                rotation=log_config.max_file_size,
                retention=log_config.retention,
                compression="zip"
            )
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close_browser()
    
    async def start_browser(self) -> None:
        """Start browser and setup page."""
        logger.info("Starting browser...")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"Browser startup attempt {attempt + 1}/{max_retries}")
                
                self.playwright = await async_playwright().start()
                
                # Configure browser options
                browser_config = self.config.browser
                launch_options = {
                    "headless": browser_config.headless,
                    "channel": "chrome",
                    "timeout": 60000,  # Increase timeout for browser launch
                    "args": [
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-dev-shm-usage",
                        "--disable-extensions",
                        "--disable-plugins",
                        "--disable-web-security",
                        "--disable-features=VizDisplayCompositor",
                        "--no-sandbox",
                        "--disable-setuid-sandbox"
                    ]
                }
                
                if browser_config.user_agent:
                    launch_options["args"].append(f"--user-agent={browser_config.user_agent}")
                
                # Launch browser with fallback
                browser_launched = False
                browser_type_used = browser_config.browser_type
                
                # Launch browser with fallback
                if browser_config.browser_type == "firefox":
                    self.browser = await self.playwright.firefox.launch(**launch_options)
                    browser_launched = True
                elif browser_config.browser_type == "webkit":
                    self.browser = await self.playwright.webkit.launch(**launch_options)
                    browser_launched = True
                elif browser_config.browser_type == "chrome":
                    # Try Chrome first
                    try:
                        launch_options["channel"] = "chrome"
                        # self.browser = await self.playwright.chromium.launch(**launch_options)
                        self.browser = await self.playwright.chromium.launch_persistent_context(
                            user_data_dir="./chrome_data",
                            channel="chrome",
                            headless=False,
                            no_viewport=True,
                        )
                        logger.info("Using Chrome browser")
                    except Exception as chrome_error:
                        logger.warning(f"Chrome launch failed: {chrome_error}")
                        logger.info("Falling back to Chromium...")
                        # Fallback to Chromium
                        launch_options.pop("channel", None)
                        self.browser = await self.playwright.chromium.launch(**launch_options)
                        browser_launched = True
                        browser_type_used = "chromium (fallback)"
                else:
                    # Default to Chromium
                    self.browser = await self.playwright.chromium.launch(**launch_options)
                    browser_launched = True
                
                if not browser_launched:
                    raise Exception("Failed to launch any browser")
                
                logger.info(f"Browser launched successfully: {browser_type_used}")
                
                # Create context with retries
                context_options = {
                    "viewport": {
                        "width": browser_config.viewport_width,
                        "height": browser_config.viewport_height
                    },
                    "user_agent": browser_config.user_agent,
                    "java_script_enabled": True,
                    "accept_downloads": True,
                    "ignore_https_errors": True
                }
                
                # Load browser state if specified
                browser_state_file = self.browser_options.get('load_browser_state') or (
                    self.browser_options.get('browser_state_file') if Path(self.browser_options.get('browser_state_file', '')).exists() else None
                )
                
                if browser_state_file and Path(browser_state_file).exists():
                    context_options["storage_state"] = browser_state_file
                    logger.info(f"🔄 Loading browser state from: {browser_state_file}")
                
                self.context = await self.browser.new_context(**context_options)
                logger.info("Browser context created successfully")
                
                # Setup request/response logging
                self.context.on("request", self._log_request)
                self.context.on("response", self._log_response)
                
                # Create page
                self.page = await self.context.new_page()
                logger.info("Browser page created successfully")
                
                # Set timeouts
                self.page.set_default_timeout(browser_config.timeout)
                self.page.set_default_navigation_timeout(60000)  # 60 seconds for navigation
                
                # Test browser health
                await self._test_browser_health()
                
                # Initialize form filler
                self.form_filler = FormFiller(self.page, self.selectors, self.application_data, self)
                
                logger.info("Browser started successfully")
                return
                
            except Exception as e:
                logger.error(f"Browser startup attempt {attempt + 1} failed: {e}")
                
                # Clean up on failure
                try:
                    if hasattr(self, 'context') and self.context:
                        await self.context.close()
                    if hasattr(self, 'browser') and self.browser:
                        await self.browser.close()
                    if hasattr(self, 'playwright') and self.playwright:
                        await self.playwright.stop()
                except:
                    pass
                
                # Reset attributes
                self.page = None
                self.context = None
                self.browser = None
                self.playwright = None
                
                if attempt == max_retries - 1:
                    logger.error("All browser startup attempts failed")
                    raise Exception(f"Failed to start browser after {max_retries} attempts: {e}")
                else:
                    logger.info(f"Retrying browser startup in 2 seconds...")
                    await asyncio.sleep(2)
    
    async def _test_browser_health(self) -> None:
        """Test if browser is working properly."""
        try:
            logger.info("Testing browser health...")
            
            # Try to navigate to a simple page
            await self.page.goto("data:text/html,<html><head><title>Browser Test</title></head><body>Browser Test</body></html>")
            
            # Check if page loaded by looking for content
            try:
                content = await self.page.text_content("body")
                if "Browser Test" in content:
                    logger.info("Browser health check passed")
                else:
                    raise Exception("Browser health check failed - unexpected page content")
            except Exception as content_error:
                # Fallback - just check if page is accessible
                url = self.page.url
                if url.startswith("data:text/html"):
                    logger.info("Browser health check passed (fallback)")
                else:
                    raise Exception(f"Browser health check failed - unexpected URL: {url}")
                
        except Exception as e:
            logger.error(f"Browser health check failed: {e}")
            raise Exception(f"Browser is not functioning properly: {e}")
    
    async def navigate_to_application(self) -> bool:
        """Navigate to DS-160 application website."""
        logger.info(f"Navigating to {self.config.ds160_url}")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"Navigation attempt {attempt + 1}/{max_retries}")
                
                # Check if browser is still alive
                if not self.page or not self.context or not self.browser:
                    logger.error("Browser components are not available")
                    return False
                
                # Check if page is closed
                if self.page.is_closed():
                    logger.error("Page has been closed")
                    return False
                
                # Navigate with optimized loading - wait for DOM only, not all resources
                response = await self.page.goto(
                    self.config.ds160_url,
                    wait_until="domcontentloaded",  # Much faster than networkidle
                    timeout=60000  # 60 seconds
                )
                
                if not response:
                    raise Exception("No response received from server")
                    
                if response.status >= 400:
                    raise Exception(f"HTTP error: {response.status}")
                
                logger.info(f"Page loaded with status: {response.status}")
                
                # Wait for page to fully load
                await wait_for_page_load(self.page)
                
                # Verify we're on the right page
                current_url = self.page.url
                if "ceac.state.gov" not in current_url:
                    logger.warning(f"Unexpected URL: {current_url}")
                
                # Take screenshot of landing page
                await take_screenshot(self.page, "landing_page.png")
                
                logger.info("Successfully navigated to DS-160 website")
                return True
                
            except Exception as e:
                logger.error(f"Navigation attempt {attempt + 1} failed: {e}")
                
                # Take error screenshot
                try:
                    await take_screenshot(self.page, f"navigation_error_attempt_{attempt + 1}.png")
                except:
                    pass
                
                if attempt == max_retries - 1:
                    logger.error("All navigation attempts failed")
                    return False
                else:
                    logger.info(f"Retrying navigation in 3 seconds...")
                    await asyncio.sleep(3)
        
        return False
    
    async def close_browser(self) -> None:
        """Close browser and cleanup resources."""
        # Check if running in headful mode - if so, keep browser open
        if not self.config.browser.headless:
            logger.info("🔍 Headful mode detected - keeping browser open for inspection")
            logger.info("📋 You can manually review the filled form")
            logger.info("💡 Close the browser window manually when done")
            
            # Save session state and browser state, but don't close browser
            try:
                # Save session state
                if hasattr(self, 'session_state') and self.session_state:
                    try:
                        session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                        await save_session_state(self.session_state, session_file)
                        logger.info(f"📄 Session state saved to: {session_file}")
                    except Exception as e:
                        logger.warning(f"Failed to save session state: {e}")
                
                # Save browser state if requested
                if self.browser_options.get('save_browser_state') and self.context:
                    try:
                        browser_state_file = self.browser_options.get('browser_state_file', 'session_data/browser_state.json')
                        Path(browser_state_file).parent.mkdir(parents=True, exist_ok=True)
                        await self.context.storage_state(path=browser_state_file)
                        logger.info(f"💾 Browser state saved to: {browser_state_file}")
                    except Exception as e:
                        logger.warning(f"Failed to save browser state: {e}")
                        
                logger.info("✅ Session data saved - browser kept open for manual review")
                logger.info("🔄 Keeping Python process alive to maintain browser session...")
                logger.info("💡 Press Ctrl+C to exit and close browser")
                
                # Keep the process alive to maintain browser session
                try:
                    while True:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    logger.info("👋 Keyboard interrupt received - closing browser...")
                    # Continue to normal browser cleanup
                except Exception as e:
                    logger.warning(f"Error keeping browser alive: {e}")
                # Fall through to normal cleanup if interrupted
                
            except Exception as e:
                logger.warning(f"Error saving data in headful mode: {e}")
                return
        
        logger.info("Closing browser...")
        
        try:
            # Save session state before closing
            if hasattr(self, 'session_state') and self.session_state:
                try:
                    session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                    await save_session_state(self.session_state, session_file)
                except Exception as e:
                    logger.warning(f"Failed to save session state: {e}")
            
            # Save browser state if requested
            if self.browser_options.get('save_browser_state') and self.context:
                try:
                    browser_state_file = self.browser_options.get('browser_state_file', 'session_data/browser_state.json')
                    # Ensure directory exists
                    Path(browser_state_file).parent.mkdir(parents=True, exist_ok=True)
                    
                    await self.context.storage_state(path=browser_state_file)
                    logger.info(f"💾 Browser state saved to: {browser_state_file}")
                except Exception as e:
                    logger.warning(f"Failed to save browser state: {e}")
            
            # Close page
            if hasattr(self, 'page') and self.page and not self.page.is_closed():
                try:
                    await self.page.close()
                    logger.debug("Page closed")
                except Exception as e:
                    logger.warning(f"Error closing page: {e}")
            
            # Close context
            if hasattr(self, 'context') and self.context:
                try:
                    await self.context.close()
                    logger.debug("Context closed")
                except Exception as e:
                    logger.warning(f"Error closing context: {e}")
            
            # Close browser
            if hasattr(self, 'browser') and self.browser:
                try:
                    await self.browser.close()
                    logger.debug("Browser closed")
                except Exception as e:
                    logger.warning(f"Error closing browser: {e}")
            
            # Stop playwright
            if hasattr(self, 'playwright') and self.playwright:
                try:
                    await self.playwright.stop()
                    logger.debug("Playwright stopped")
                except Exception as e:
                    logger.warning(f"Error stopping playwright: {e}")
            
            # Reset attributes
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            
            logger.info("Browser closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing browser: {e}")
            # Force cleanup
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
    
    async def load_data(self, data_file: Optional[str] = None) -> DS160Data:
        """Load and validate form data."""
        data_file = data_file or self.config.data_file
        logger.info(f"Loading data from {data_file}")
        
        self.data_loader = DataLoader(data_file)
        data = await self.data_loader.load_data()
        
        # Validate data
        validation_errors = self.data_loader.validate_data()
        if validation_errors:
            logger.error(f"Data validation errors: {validation_errors}")
            raise ValueError(f"Data validation failed: {validation_errors}")
        
        logger.info("Data loaded and validated successfully")
        return data
    
    async def fill_application(self, data: DS160Data) -> bool:
        """Fill the DS-160 application form."""
        logger.info("Starting DS-160 application filling process")
        
        try:
            # Start application process
            if not await self.form_filler.start_application():
                logger.error("Failed to start application")
                return False
            
            await human_like_delay()
            
            # Take screenshot of current page to see where we are
            await take_screenshot(self.page, "current_page_after_start.png")
            
            # Check current section and handle accordingly
            current_url = self.page.url
            current_section = self._get_current_section()
            logger.info(f"Current section: {current_section}, URL: {current_url}")
            
            # Update session state with real current page
            await self._update_session_current_page(current_section)
            
            # If we're still on application ID page, try to find a way to continue
            if current_section == "application_id":
                logger.info("Still on application ID page, looking for continue options...")
                
                # Look for ways to proceed to the actual form
                continue_options = [
                    "input[value*='New']",
                    "button:has-text('New')",
                    "a:has-text('New')",
                    "input[type='submit']",
                    "button[type='submit']"
                ]
                
                for option in continue_options:
                    try:
                        if await wait_for_element(self.page, option, timeout=3000):
                            await safe_click(self.page, option)
                            logger.info(f"Clicked continue option: {option}")
                            await wait_for_page_load(self.page)
                            await human_like_delay()
                            break
                    except Exception as e:
                        logger.debug(f"Continue option {option} failed: {e}")
                
                # Update current section after navigation
                current_url = self.page.url
                logger.info(f"New URL after continue: {current_url}")
                
                # Re-detect section after clicking
                current_section = self._get_current_section()
            
            # Now try to fill based on detected section
            if current_section == "security_question":
                logger.info("Processing Security Question page")
                await take_screenshot(self.page, "before_security_question.png")
                
                # Call the security question handler
                if not await self.form_filler.handle_security_question_page():
                    logger.error("Failed to handle Security Question page")
                    return False
                    
                logger.info("Security Question page handled successfully")
                
                # Check current URL after processing
                current_url = self.page.url
                logger.info(f"URL after security question processing: {current_url}")
                
                # Check if we've moved to personal info or other section
                current_section = self._get_current_section()
                if current_section == "personal_info":
                    logger.info("Now processing personal information section")
                    
                    if not await self.form_filler.fill_personal_info(data):
                        logger.error("Failed to fill personal information")
                        return False
                    
                    # Update session state after successful personal info filling
                    self.session_state["current_page"] = "personal_completed"
                    self.session_state["completed_sections"].append("personal")
                    self.session_state["last_action"] = "personal_info_filled"
                    session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                    await save_session_state(self.session_state, session_file)
                    logger.info("📄 Session state updated after personal info")
                    
                    await human_like_delay()
                    
                    # Continue with all remaining sections in sequence
                    return await self._continue_form_filling(data)
                elif "Application" in current_url:
                    logger.info("Proceeding to next application section")
                    # Continue with next sections
                    return await self._continue_form_filling(data)
                else:
                    logger.info("Security question processing completed - ready for next steps")
                    # Always try to continue, regardless of URL
                    return await self._continue_form_filling(data)
                    
            elif current_section == "personal_info":
                logger.info("Filling personal information section")
                await take_screenshot(self.page, "before_personal_info.png")
                
                if not await self.form_filler.fill_personal_info(data):
                    logger.error("Failed to fill personal information")
                    return False
                
                # Update session state after successful personal info filling
                self.session_state["current_page"] = "personal_completed"
                if "personal" not in self.session_state["completed_sections"]:
                    self.session_state["completed_sections"].append("personal")
                self.session_state["last_action"] = "personal_info_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after personal info")
                
                await human_like_delay()
                
                # Continue with all remaining sections in sequence
                await self._continue_form_filling(data)
            
            elif current_section in ["security_background_1", "security_background_2", "security_background_3", "security_background_4", "security_background_5"]:
                logger.info(f"Handling {current_section} section")
                await take_screenshot(self.page, f"before_{current_section}.png")
                
                # Route to appropriate security section handler
                success = False
                if current_section == "security_background_1":
                    success = await self.form_filler.fill_security_background_part1_medical(data)
                elif current_section == "security_background_2":
                    success = await self.form_filler.fill_security_background_part2_criminal(data)
                elif current_section == "security_background_3":
                    success = await self.form_filler.fill_security_background_part3_security(data)
                elif current_section == "security_background_4":
                    success = await self.form_filler.fill_security_background_part4_immigration(data)
                elif current_section == "security_background_5":
                    success = await self.form_filler.fill_security_background_part5_miscellaneous(data)
                
                if not success:
                    logger.error(f"Failed to fill {current_section}")
                    return False
                
                logger.info(f"✅ {current_section} section completed successfully")
                
                # Update session state
                self.session_state["current_page"] = f"{current_section}_completed"
                if current_section not in self.session_state["completed_sections"]:
                    self.session_state["completed_sections"].append(current_section)
                self.session_state["last_action"] = f"{current_section}_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info(f"📄 Session state updated after {current_section}")
                
                await human_like_delay()
                
                # Continue with remaining sections
                return await self._continue_form_filling(data)
            
            elif current_section == "previous_us_travel":
                logger.info("🛂 Filling Previous US Travel Information")
                await take_screenshot(self.page, "before_previous_us_travel.png")
                
                if not await self.form_filler.fill_previous_us_travel_info(data):
                    logger.error("Failed to fill previous US travel information")
                    return False
                
                logger.info("✅ Previous US Travel section completed successfully")
                
                # Update session state
                self.session_state["current_page"] = "previous_us_travel_completed"
                if "previous_us_travel" not in self.session_state["completed_sections"]:
                    self.session_state["completed_sections"].append("previous_us_travel")
                self.session_state["last_action"] = "previous_us_travel_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after Previous US Travel")
                
                return await self._continue_form_filling(data)
            
            elif current_section == "photo_upload":
                logger.info("🖼️ Reached Photo Upload page - displaying application recovery information")
                await take_screenshot(self.page, "photo_upload_page.png")
                
                # Display application recovery information
                await self._display_application_recovery_info(data)
                
                logger.info("ℹ️ Photo upload requires manual intervention - stopping automated process")
                logger.info("📋 Please complete photo upload manually and continue with the application")
                
                # Update session state
                self.session_state["current_page"] = "photo_upload_reached"
                if "photo_upload" not in self.session_state["completed_sections"]:
                    self.session_state["completed_sections"].append("security_completed")
                self.session_state["last_action"] = "photo_upload_page_reached"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated - photo upload page reached")
                
                return True  # Consider this successful completion to photo upload stage
            
            else:
                logger.info(f"🤖 Attempting universal handler for section: {current_section}")
                
                # Try universal section handler
                universal_result = await self._handle_section_universal(current_section, data)
                if universal_result:
                    logger.info(f"✅ Universal handler successfully processed: {current_section}")
                    return universal_result
                
                # If universal handler failed, provide debugging info
                logger.warning(f"❓ Unknown section: {current_section}")
                logger.info("🔍 Page content for debugging:")
                
                # Get page text for debugging
                try:
                    page_text = await self.page.text_content("body")
                    logger.info(f"📄 Page text sample: {page_text[:500]}...")
                except:
                    pass
                
                return False
            
            logger.info("Application filling completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error during application filling: {e}")
            await take_screenshot(self.page, "application_filling_error.png")
            return False
    
    async def _continue_form_filling(self, data: DS160Data) -> bool:
        """Continue filling form sections after personal info."""
        logger.info("Continuing with form filling process...")
        
        try:
            # Check current URL to determine what page we're on
            current_url = self.page.url
            logger.info(f"Current URL for continuation: {current_url}")
            
            # Take screenshot to see current page
            await take_screenshot(self.page, "continue_form_filling.png")
            
            # If we have a chrome-error URL, this indicates navigation failure
            if "chrome-error" in current_url or not current_url.startswith("http"):
                logger.warning(f"Invalid URL detected: {current_url}")
                
                # If it's specifically chrome-error, try to recover
                if "chrome-error://" in current_url:
                    logger.error("🚨 Browser encountered chrome-error page - navigation failed")
                    await take_screenshot(self.page, "chrome_error_recovery.png")
                    
                    # Try to go back to recover
                    try:
                        logger.info("Attempting to recover by going back...")
                        await self.page.go_back()
                        await wait_for_page_load(self.page, timeout=10000)
                        
                        new_url = self.page.url
                        if "chrome-error://" not in new_url:
                            logger.info(f"✅ Recovered from error page to: {new_url}")
                            current_url = new_url
                        else:
                            logger.error("❌ Still on error page after recovery attempt")
                            return False
                            
                    except Exception as e:
                        logger.error(f"Failed to recover from chrome-error: {e}")
                        return False
                else:
                    logger.info("Attempting to detect page type by content instead of URL...")
                
                # Wait a moment for page to load
                await human_like_delay(2.0, 3.0)
                
                # Try to get page title and content to determine page type
                try:
                    page_title = await self.page.title()
                    logger.info(f"Page title: {page_title}")
                    
                    # Check for specific page content patterns
                    if "Personal Information" in page_title:
                        logger.info("🔍 Detected Personal Information page by title")
                        current_url = "personal1_detected_by_content"
                    elif await self.page.locator("text=Personal Information").count() > 0:
                        logger.info("🔍 Detected Personal Information page by content")
                        current_url = "personal1_detected_by_content"
                    elif await self.page.locator("text=Address and Phone").count() > 0:
                        logger.info("🔍 Detected Address and Phone page by content")
                        current_url = "contact_detected_by_content"
                    elif await self.page.locator("text=Passport").count() > 0:
                        logger.info("🔍 Detected Passport page by content")
                        current_url = "passport_detected_by_content"
                    elif await self.page.locator("text=U.S. Point of Contact").count() > 0:
                        logger.info("🔍 Detected US Contact page by content")
                        current_url = "uscontact_detected_by_content"
                    elif await self.page.locator("text=Travel").count() > 0:
                        logger.info("🔍 Detected Travel page by content")
                        current_url = "travel_detected_by_content"
                    elif await self.page.locator("text=Family").count() > 0 or await self.page.locator("text=Relatives").count() > 0:
                        logger.info("🔍 Detected Family Information page by content")
                        current_url = "family_detected_by_content"
                    elif await self.page.locator("text=Work").count() > 0 or await self.page.locator("text=Education").count() > 0 or await self.page.locator("text=Training").count() > 0:
                        # Check if this is the additional work/education page
                        if (await self.page.locator("text=clan").count() > 0 or 
                            await self.page.locator("text=tribe").count() > 0 or
                            await self.page.locator("text=language").count() > 0 or
                            await self.page.locator("text=military").count() > 0 or
                            await self.page.locator("text=specialized").count() > 0):
                            logger.info("🔍 Detected Work/Education Additional page by content")
                            current_url = "workeducation_additional_detected_by_content"
                        else:
                            logger.info("🔍 Detected Work/Education page by content")
                            current_url = "workeducation_detected_by_content"
                    elif await self.page.locator("text=Security").count() > 0 or await self.page.locator("text=Background").count() > 0:
                        logger.info("🔍 Detected Security and Background page by content")
                        
                        # Try to determine which security part based on page content
                        if await self.page.locator("text=communicable disease").count() > 0 or await self.page.locator("text=mental disorder").count() > 0 or await self.page.locator("text=drug").count() > 0:
                            logger.info("🔍 Detected Security Part 1 - Medical and Health by content")
                            current_url = "security1_detected_by_content"
                        elif await self.page.locator("text=controlled substance").count() > 0 or await self.page.locator("text=prostitution").count() > 0 or await self.page.locator("text=money laundering").count() > 0:
                            logger.info("🔍 Detected Security Part 2 - Criminal by content")
                            current_url = "security2_detected_by_content"
                        elif await self.page.locator("text=terrorist").count() > 0 or await self.page.locator("text=espionage").count() > 0 or await self.page.locator("text=genocide").count() > 0:
                            logger.info("🔍 Detected Security Part 3 - Security by content")
                            current_url = "security3_detected_by_content"
                        elif await self.page.locator("text=visa").count() > 0 and await self.page.locator("text=fraud").count() > 0:
                            logger.info("🔍 Detected Security Part 4 - Immigration Violations by content")
                            current_url = "security4_detected_by_content"
                        elif await self.page.locator("text=custody").count() > 0 or await self.page.locator("text=voting").count() > 0 or await self.page.locator("text=citizenship").count() > 0:
                            logger.info("🔍 Detected Security Part 5 - Miscellaneous by content")
                            current_url = "security5_detected_by_content"
                        else:
                            logger.info("🔍 Detected generic Security page by content")
                            current_url = "security_detected_by_content"
                    else:
                        # Try to find any form to continue
                        forms = await self.page.query_selector_all("form")
                        if forms:
                            logger.info(f"Found {len(forms)} forms - attempting to continue anyway")
                            # Try to click any next/continue button
                            continue_selectors = [
                                "input[value*='Continue']",
                                "input[value*='Next']", 
                                "input[name*='btnNext']",
                                "button:has-text('Continue')",
                                "button:has-text('Next')"
                            ]
                            
                            for selector in continue_selectors:
                                if await safe_click(self.page, selector):
                                    logger.info(f"Clicked continue button: {selector}")
                                    await wait_for_page_load(self.page)
                                    await human_like_delay()
                                    return await self._continue_form_filling(data)
                        
                        logger.error("Could not determine page type from content")
                        return False
                        
                except Exception as e:
                    logger.error(f"Failed to analyze page content: {e}")
                    return False
            
            # ========== SPECIFIC URL PATTERN MATCHING (HIGHEST PRIORITY) ==========
            # These checks must come BEFORE generic content-based detection to avoid misrouting
            
            # Personal Information 1
            if "complete_personal.aspx" in current_url:
                logger.info("🎯 Detected Personal 1 page by specific URL pattern - filling personal information")
                
                if not await self.form_filler.fill_personal_info(data):
                    logger.error("Failed to fill personal information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("personal")
                self.session_state["last_action"] = "personal_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after personal info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Personal Information 2
            elif "complete_personalcont.aspx" in current_url:
                # Check if we've already processed this page
                if "personal2" in self.session_state.get("completed_sections", []):
                    logger.info("🔄 Personal 2 already completed - waiting for page transition...")
                    await human_like_delay(3, 5)  # Wait longer for page transition
                    return await self._continue_form_filling(data)
                
                logger.info("🎯 Detected Personal 2 page by specific URL pattern - filling nationality and ID information")
                
                # Wait for page stability before filling
                from .utils import wait_for_page_stability
                if not await wait_for_page_stability(self.page):
                    logger.warning("⚠️ Page stability check failed, continuing anyway...")
                
                if not await self.form_filler.fill_personal_2_info(data):
                    logger.error("Failed to fill Personal 2 information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("personal2")
                self.session_state["last_action"] = "personal2_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after Personal 2")
                
                await human_like_delay(5, 8)  # Wait longer after form completion
                
                # Continue with next section after Personal 2
                return await self._continue_form_filling(data)
                
            # Address and Phone Information
            elif "complete_contact.aspx" in current_url:
                logger.info("🎯 Detected Contact page by specific URL pattern - filling Address and Phone information")
                
                if not await self.form_filler.fill_contact_info(data):
                    logger.error("Failed to fill contact information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("contact")
                self.session_state["last_action"] = "contact_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after contact info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Passport Information - THIS FIXES THE ROUTING BUG!
            elif "Passport_Visa_Info.aspx" in current_url:
                logger.info("🎯 Detected Passport page by specific URL pattern - filling passport information")
                
                if not await self.form_filler.fill_passport_info(data):
                    logger.error("Failed to fill passport information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("passport")
                self.session_state["last_action"] = "passport_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after passport info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # U.S. Point of Contact Information
            elif "complete_uscontact.aspx" in current_url:
                logger.info("🎯 Detected US Contact page by specific URL pattern - filling U.S. Point of Contact information")
                
                if not await self.form_filler.fill_us_contact_info(data):
                    logger.error("Failed to fill U.S. contact information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("us_contact")
                self.session_state["last_action"] = "us_contact_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after US contact info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Travel Information
            elif "complete_travel.aspx" in current_url:
                logger.info("🎯 Detected Travel page by specific URL pattern - filling travel information")
                
                if not await self.form_filler.fill_travel_info(data):
                    logger.error("Failed to fill travel information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("travel")
                self.session_state["last_action"] = "travel_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after travel info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Travel Companions Information
            elif "complete_travelcompanions.aspx" in current_url:
                logger.info("🎯 Detected Travel Companions page by specific URL pattern - filling travel companions information")
                
                if not await self.form_filler.fill_travel_companions_info(data):
                    logger.error("Failed to fill travel companions information")
                    return False
                
                # Update session state  
                self.session_state["completed_sections"].append("travel_companions")
                self.session_state["last_action"] = "travel_companions_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after travel companions info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Previous U.S. Travel Information
            elif "complete_previousustravel.aspx" in current_url:
                logger.info("🎯 Detected Previous US Travel page by specific URL pattern - filling previous US travel information")
                
                if not await self.form_filler.fill_previous_us_travel_info(data):
                    logger.error("Failed to fill previous US travel information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("previous_us_travel")
                self.session_state["last_action"] = "previous_us_travel_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after previous US travel info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Family Information: Relatives
            elif "complete_family1.aspx" in current_url:
                logger.info("🎯 Detected Family page by specific URL pattern - filling family information")
                
                if not await self.form_filler.fill_family_info(data):
                    logger.error("Failed to fill family information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("family")
                self.session_state["last_action"] = "family_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after family info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Family Information: Spouse/Additional Family Pages
            elif ("complete_family2.aspx" in current_url or 
                  "complete_family4.aspx" in current_url or 
                  "complete_family5.aspx" in current_url):
                logger.info("🎯 Detected additional family page (spouse/divorced/widowed) - handling with FamilyHandler")
                
                if not await self.form_filler.family_handler.handle_additional_family_page(data):
                    logger.error("Failed to fill additional family information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("family_additional")
                self.session_state["last_action"] = "family_additional_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after additional family info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Work/Education/Training Information (Present)
            elif "complete_workeducation1.aspx" in current_url:
                logger.info("🎯 Detected Work/Education 1 page by specific URL pattern - filling work/education information")
                
                if not await self.form_filler.fill_work_education_info(data):
                    logger.error("Failed to fill work/education information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("work_education")
                self.session_state["last_action"] = "work_education_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after work/education info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Work/Education/Training Information (Previous)
            elif "complete_workeducation2.aspx" in current_url:
                logger.info("🎯 Detected Work/Education 2 page by specific URL pattern - filling previous work/education information")
                
                if not await self.form_filler.fill_previous_work_education_info(data):
                    logger.error("Failed to fill previous work/education information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("work_education_2")
                self.session_state["last_action"] = "work_education_2_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after work/education 2 info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Work/Education/Training Information (Additional)
            elif "complete_workeducation3.aspx" in current_url:
                logger.info("🎯 Detected Work/Education 3 page by specific URL pattern - filling additional work/education information")
                
                if not await self.form_filler.fill_work_education_additional_info(data):
                    logger.error("Failed to fill additional work/education information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("work_education_3")
                self.session_state["last_action"] = "work_education_3_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after work/education 3 info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Security and Background: Part 1
            elif "complete_securityandbackground1.aspx" in current_url:
                logger.info("🎯 Detected Security 1 page by specific URL pattern - filling security part 1 information")
                
                if not await self.form_filler.fill_security_background_part1_medical(data):
                    logger.error("Failed to fill security part 1 information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("security_1")
                self.session_state["last_action"] = "security_1_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after security 1 info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Security and Background: Part 2
            elif "complete_securityandbackground2.aspx" in current_url:
                logger.info("🎯 Detected Security 2 page by specific URL pattern - filling security part 2 information")
                
                if not await self.form_filler.fill_security_background_part2_criminal(data):
                    logger.error("Failed to fill security part 2 information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("security_2")
                self.session_state["last_action"] = "security_2_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after security 2 info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Security and Background: Part 3
            elif "complete_securityandbackground3.aspx" in current_url:
                logger.info("🎯 Detected Security 3 page by specific URL pattern - filling security part 3 information")
                
                if not await self.form_filler.fill_security_background_part3_security(data):
                    logger.error("Failed to fill security part 3 information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("security_3")
                self.session_state["last_action"] = "security_3_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after security 3 info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Security and Background: Part 4
            elif "complete_securityandbackground4.aspx" in current_url:
                logger.info("🎯 Detected Security 4 page by specific URL pattern - filling security part 4 information")
                
                if not await self.form_filler.fill_security_background_part4_immigration(data):
                    logger.error("Failed to fill security part 4 information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("security_4")
                self.session_state["last_action"] = "security_4_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after security 4 info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Security and Background: Part 5
            elif "complete_securityandbackground5.aspx" in current_url:
                logger.info("🎯 Detected Security 5 page by specific URL pattern - filling security part 5 information")
                
                if not await self.form_filler.fill_security_background_part5_miscellaneous(data):
                    logger.error("Failed to fill security part 5 information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("security_5")
                self.session_state["last_action"] = "security_5_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after security 5 info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # ========== FALLBACK TO GENERIC PATTERN MATCHING ==========
            # These checks run only if specific URL patterns don't match
            
            # Check if we're on Personal 1 page (by URL or content) - FALLBACK
            elif ("personal1" in current_url.lower() and "complete_personal.aspx" in current_url.lower()) or "personal1_detected_by_content" in current_url.lower():
                logger.info("🔍 Detected Personal 1 page by generic pattern - filling personal information")
                
                if not await self.form_filler.fill_personal_info(data):
                    logger.error("Failed to fill personal information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("personal")
                self.session_state["last_action"] = "personal_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after personal info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on Personal 2 page (nationality and ID info)
            elif "personalcont" in current_url.lower() or "personal2" in current_url.lower():
                logger.info("Detected Personal 2 page - filling nationality and ID information")
                
                if not await self.form_filler.fill_personal_2_info(data):
                    logger.error("Failed to fill Personal 2 information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("personal2")
                self.session_state["last_action"] = "personal2_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after Personal 2")
                
                await human_like_delay()
                
                # Continue with next section after Personal 2
                return await self._continue_form_filling(data)
                
            # Check if we're on US Contact page (Point of Contact)
            elif "uscontact" in current_url.lower() or "uscontact_detected_by_content" in current_url.lower():
                logger.info("Detected US Contact page - filling U.S. Point of Contact information")
                
                if not await self.form_filler.fill_us_contact_info(data):
                    logger.error("Failed to fill U.S. contact information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("us_contact")
                self.session_state["last_action"] = "us_contact_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after US contact info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on contact info page (Address and Phone)
            elif "contact" in current_url.lower() or "contact_detected_by_content" in current_url.lower():
                logger.info("Detected Contact page - filling Address and Phone information")
                
                if not await self.form_filler.fill_contact_info(data):
                    logger.error("Failed to fill contact information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("contact")
                self.session_state["last_action"] = "contact_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after contact info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on passport info page
            elif "passport" in current_url.lower() or "passport_detected_by_content" in current_url.lower():
                logger.info("Detected Passport page - filling passport information")
                
                if not await self.form_filler.fill_passport_info(data):
                    logger.error("Failed to fill passport information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("passport")
                self.session_state["last_action"] = "passport_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after passport info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on travel companions page
            elif "travelcompanions" in current_url.lower():
                logger.info("Detected Travel Companions page - filling travel companions information")
                
                if not await self.form_filler.fill_travel_companions_info(data):
                    logger.error("Failed to fill travel companions information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("travel_companions")
                self.session_state["last_action"] = "travel_companions_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on previous US travel page
            elif "previousustravel" in current_url.lower():
                logger.info("Detected Previous US Travel page - filling previous US travel information")
                
                if not await self.form_filler.fill_previous_us_travel_info(data):
                    logger.error("Failed to fill previous US travel information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("previous_us_travel")
                self.session_state["last_action"] = "previous_us_travel_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on travel info page
            elif "travel" in current_url.lower() or "travel_detected_by_content" in current_url.lower():
                logger.info("Detected Travel page - filling travel information")
                
                if not await self.form_filler.fill_travel_info(data):
                    logger.error("Failed to fill travel information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("travel")
                self.session_state["last_action"] = "travel_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after travel info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on family info page (Family Information/Relatives)
            elif ("family" in current_url.lower() or "relatives" in current_url.lower() or 
                  "family_detected_by_content" in current_url.lower()):
                logger.info("Detected Family Information page - filling family information")
                
                if not await self.form_filler.fill_family_info(data):
                    logger.error("Failed to fill family information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("family")
                self.session_state["last_action"] = "family_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after family info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on work/education info page (current work - page 1)
            elif ("workeducation1" in current_url.lower() or 
                  ("workeducation" in current_url.lower() and "2" not in current_url.lower() and "3" not in current_url.lower() and "additional" not in current_url.lower()) or 
                  ("work" in current_url.lower() and "education" in current_url.lower() and "workeducation_detected_by_content" in current_url.lower())):
                logger.info("Detected Work/Education page 1 - filling current work and education information")
                
                if not await self.form_filler.fill_work_education_info(data):
                    logger.error("Failed to fill current work/education information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("work_education")
                self.session_state["last_action"] = "work_education_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after work/education info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on previous work/education info page (page 2)
            elif "workeducation2" in current_url.lower():
                logger.info("Detected Work/Education page 2 - filling previous work and education information")
                
                if not await self.form_filler.fill_previous_work_education_info(data):
                    logger.error("Failed to fill previous work/education information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("work_education")
                self.session_state["last_action"] = "work_education_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after work/education info")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on Work/Education Additional Information page
            elif ("additional" in current_url.lower() and "work" in current_url.lower()) or \
                 ("workeducation" in current_url.lower() and "additional" in current_url.lower()) or \
                 ("clan" in current_url.lower() or "tribe" in current_url.lower()) or \
                 ("language" in current_url.lower() and "work" in current_url.lower()) or \
                 ("military" in current_url.lower() and "education" in current_url.lower()) or \
                 ("workeducation_additional_detected_by_content" in current_url.lower()):
                logger.info("Detected Work/Education Additional Information page")
                
                if not await self.form_filler.fill_work_education_additional_info(data):
                    logger.error("Failed to fill Work/Education Additional Information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("work_education_additional")
                self.session_state["last_action"] = "work_education_additional_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after Work/Education Additional")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on Work/Education page 3 - Additional Training/Education Information
            elif "workeducation3" in current_url.lower():
                logger.info("Detected Work/Education page 3 - Additional Training/Education Information")
                
                if not await self.form_filler.fill_work_education_additional_info(data):
                    logger.error("Failed to fill Work/Education page 3 - Additional Information")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("work_education_page3")
                self.session_state["last_action"] = "work_education_page3_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after Work/Education page 3")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check if we're on ANY Security and Background page - Universal Handler
            elif ("security" in current_url.lower()) or \
                 ("medical" in current_url.lower()) or \
                 ("health" in current_url.lower() and "security" in current_url.lower()) or \
                 ("criminal" in current_url.lower()) or \
                 ("terrorist" in current_url.lower()) or \
                 ("espionage" in current_url.lower()) or \
                 ("immigration" in current_url.lower() and "violation" in current_url.lower()) or \
                 ("fraud" in current_url.lower()) or \
                 ("miscellaneous" in current_url.lower()) or \
                 ("custody" in current_url.lower()) or \
                 ("voting" in current_url.lower() and "illegal" in current_url.lower()) or \
                 ("citizenship" in current_url.lower() and "renounce" in current_url.lower()) or \
                 ("security1_detected_by_content" in current_url.lower()) or \
                 ("security2_detected_by_content" in current_url.lower()) or \
                 ("security3_detected_by_content" in current_url.lower()) or \
                 ("security4_detected_by_content" in current_url.lower()) or \
                 ("security5_detected_by_content" in current_url.lower()) or \
                 ("security_detected_by_content" in current_url.lower()):
                logger.info(f"🔒 Detected Security and Background page: {current_url}")
                
                if not await self.form_filler.fill_security_universal(data):
                    logger.error("Failed to fill Security and Background page")
                    return False
                
                # Update session state
                self.session_state["completed_sections"].append("security_universal")
                self.session_state["last_action"] = "security_universal_filled"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated after Security page")
                
                await human_like_delay()
                
                # Continue with next section
                return await self._continue_form_filling(data)
                
            # Check for Photo Upload page first before trying other forms
            elif "photo_uploadthephoto.aspx" in current_url.lower() or "uploadphoto" in current_url.lower():
                logger.info("🖼️ Reached Photo Upload page during continuation - displaying application recovery information")
                await take_screenshot(self.page, "photo_upload_page_continue.png")
                
                # Display application recovery information
                await self._display_application_recovery_info(data)
                
                logger.info("ℹ️ Photo upload requires manual intervention - stopping automated process")
                logger.info("📋 Please complete photo upload manually and continue with the application")
                
                # Update session state
                self.session_state["current_page"] = "photo_upload_reached"
                if "photo_upload" not in self.session_state["completed_sections"]:
                    self.session_state["completed_sections"].append("security_completed")
                self.session_state["last_action"] = "photo_upload_page_reached"
                session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
                await save_session_state(self.session_state, session_file)
                logger.info("📄 Session state updated - photo upload page reached")
                
                return True  # Consider this successful completion to photo upload stage
            
            # If we don't recognize the page, see if we can find any forms to fill
            else:
                logger.info(f"Unrecognized page: {current_url}")
                
                # Check if there are any forms on the page
                forms = await self.page.query_selector_all("form")
                if forms:
                    logger.info(f"Found {len(forms)} forms on page - might be another section")
                    
                    # Try to detect what kind of page this is based on content
                    page_text = await self.page.text_content("body")
                    
                    if "nationality" in page_text.lower() or "country" in page_text.lower():
                        logger.info("Page seems to be about nationality - trying Personal 2 filler")
                        
                        if not await self.form_filler.fill_personal_2_info(data):
                            logger.error("Failed to fill nationality information")
                            return False
                            
                        return await self._continue_form_filling(data)
                        
                    elif "contact" in page_text.lower() or "address" in page_text.lower():
                        logger.info("Page seems to be about contact - trying contact filler")
                        
                        if not await self.form_filler.fill_contact_info(data):
                            logger.error("Failed to fill contact information")
                            return False
                            
                        return await self._continue_form_filling(data)
                        
                    else:
                        logger.warning("Could not determine page type - stopping form filling")
                        logger.info(f"Page text sample: {page_text[:500]}...")
                        return True  # Consider this a success, manual review needed
                else:
                    logger.info("No forms found on page - form filling process may be complete")
                    return True
            
        except Exception as e:
            logger.error(f"Error continuing form filling: {e}")
            await take_screenshot(self.page, "continue_form_filling_error.png")
            return False
    
    async def resume_session(self, session_file: str = "session_state.json") -> bool:
        """Resume from a previous session."""
        logger.info(f"Attempting to resume session from {session_file}")
        
        session_state = await load_session_state(session_file)
        if not session_state:
            logger.warning("No previous session found")
            return False
        
        self.session_state = session_state
        logger.info(f"Resumed session started at {session_state.get('started_at')}")
        logger.info(f"Completed sections: {session_state.get('completed_sections', [])}")
        
        # Check if browser state was loaded
        if self.browser_options.get('load_browser_state') or (
            self.browser_options.get('browser_state_file') and 
            Path(self.browser_options.get('browser_state_file')).exists()
        ):
            logger.info("🔄 Browser state loaded - cookies and localStorage restored")
        
        # Load form data if available and valid
        if "form_data" in session_state:
            try:
                # Validate that form_data has the required structure
                form_data = session_state["form_data"]
                if isinstance(form_data, dict) and len(form_data) > 0:
                    # Try to create DS160Data object
                    data = DS160Data(**form_data)
                    # Continue from where we left off
                    # This would require more sophisticated state management
                    logger.info("Session resumed successfully with form data")
                    return True
                else:
                    logger.warning("Form data in session state is empty or invalid")
            except Exception as e:
                logger.warning(f"Session state contains invalid form data: {e}")
                logger.info("Will continue with fresh data loading")
        
        # Even if form data is invalid, we can still use the session state
        # for browser state restoration and application data
        if len(session_state.get('completed_sections', [])) > 0:
            logger.info(f"Session state loaded with completed sections: {session_state.get('completed_sections')}")
            logger.info("Session state will be used for progress tracking, but fresh data will be loaded")
            return True
        
        return False
    
    def _log_request(self, request) -> None:
        """Log outgoing requests."""
        if logger.level("DEBUG").no >= logger._core.min_level:
            logger.debug(f"Request: {request.method} {request.url}")
    
    def _log_response(self, response) -> None:
        """Log incoming responses."""
        if logger.level("DEBUG").no >= logger._core.min_level:
            logger.debug(f"Response: {response.status} {response.url}")
    
    async def run(self, data_file: Optional[str] = None, resume: bool = False) -> bool:
        """Main entry point to run the bot."""
        logger.info("🤖 DS-160 Bot Starting...")
        logger.warning("⚠️  LEGAL DISCLAIMER:")
        logger.warning("⚠️  Automating DS-160 forms may violate US State Department terms of service")
        logger.warning("⚠️  Use at your own risk and responsibility")
        logger.warning("⚠️  Manual review of all filled information is strongly recommended")
        
        try:
            # Try to resume session if requested
            if resume:
                logger.info("Attempting to resume from previous session...")
                
                # Load saved application data
                saved_app_data = await load_application_data()
                if saved_app_data:
                    self.application_data = saved_app_data
                    logger.info("🔐 Loaded saved application data:")
                    logger.info(f"  Application ID: {self.application_data.application_id}")
                    logger.info(f"  Surname: {self.application_data.surname}")
                    logger.info(f"  Security Answer: {'*' * len(self.application_data.security_question_answer) if self.application_data.security_question_answer else 'None'}")
                    logger.info(f"  Birth Year: {self.application_data.year_of_birth}")
                    logger.info(f"  Last Section: {self.application_data.current_section}")
                    
                    # If we have enough data to resume, try it with session state
                    if await self.resume_session():
                        logger.info("✅ Resuming with saved application data")
                        return True
                    else:
                        logger.warning("Could not resume with session state, but will use saved app data")
                else:
                    logger.warning("No saved application data found for resume")
                    
                    # Try regular session resume
                    if await self.resume_session():
                        logger.info("Resuming from previous session")
                        return True
            
            # Load data
            data = await self.load_data(data_file)
            
            # Navigate to application
            if not await self.navigate_to_application():
                return False
            
            # Fill application
            success = await self.fill_application(data)
            
            if success:
                logger.info("✅ DS-160 application process completed successfully")
                logger.info("📋 Please review all filled information manually before submission")
            else:
                logger.error("❌ DS-160 application process failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Bot execution failed: {e}")
            if self.page:
                await take_screenshot(self.page, "bot_error.png")
            return False
    
    def _get_section_config(self) -> Dict[str, Dict[str, Any]]:
        """
        Get configuration mapping for all DS-160 sections.
        Maps section names to their form filler methods and display info.
        """
        return {
            # Authentication and setup
            "security_question": {
                "method": None,  # Handled specially in existing logic
                "display_name": "Security Question",
                "emoji": "🔐",
                "skip_auto_handle": True
            },
            "application_id": {
                "method": None,  # Handled specially in existing logic
                "display_name": "Application ID",
                "emoji": "🆔",
                "skip_auto_handle": True
            },
            
            # Personal Information
            "personal_info": {
                "method": "fill_personal_info",
                "display_name": "Personal Information",
                "emoji": "👤"
            },
            "personal_info_2": {
                "method": "fill_personal_2_info", 
                "display_name": "Personal Information (Part 2)",
                "emoji": "👤"
            },
            "nationality_info": {
                "method": "fill_nationality_info",
                "display_name": "Nationality Information", 
                "emoji": "🌍"
            },
            
            # Contact Information
            "contact_info": {
                "method": "fill_contact_info",
                "display_name": "Contact Information",
                "emoji": "📞"
            },
            "passport_info": {
                "method": "fill_passport_info",
                "display_name": "Passport Information",
                "emoji": "📘"
            },
            
            # Travel Information
            "travel_info": {
                "method": "fill_travel_info",
                "display_name": "Travel Information",
                "emoji": "✈️"
            },
            "travel_companions": {
                "method": "fill_travel_companions_info",
                "display_name": "Travel Companions",
                "emoji": "👥"
            },
            "previous_us_travel": {
                "method": "fill_previous_us_travel_info",
                "display_name": "Previous US Travel",
                "emoji": "🛂"
            },
            
            # US Contact Information
            "us_contact_info": {
                "method": "fill_us_contact_info",
                "display_name": "US Contact Information",
                "emoji": "🇺🇸"
            },
            
            # Family Information
            "family_info": {
                "method": "fill_family_info",
                "display_name": "Family Information",
                "emoji": "👨‍👩‍👧‍👦"
            },
            
            # Work and Education
            "work_education": {
                "method": "fill_work_education_info",
                "display_name": "Work/Education Information",
                "emoji": "💼"
            },
            "previous_work_education": {
                "method": "fill_previous_work_education_info",
                "display_name": "Previous Work/Education",
                "emoji": "📚"
            },
            "work_education_additional": {
                "method": "fill_work_education_additional_info",
                "display_name": "Additional Work/Education",
                "emoji": "📋"
            },
            
            # Security and Background
            "security_background": {
                "method": "fill_security_universal",
                "display_name": "Security and Background",
                "emoji": "🛡️"
            },
            "security_background_1": {
                "method": "fill_security_background_part1_medical",
                "display_name": "Security Part 1 (Medical)",
                "emoji": "🏥"
            },
            "security_background_2": {
                "method": "fill_security_background_part2_criminal",
                "display_name": "Security Part 2 (Criminal)",
                "emoji": "⚖️"
            },
            "security_background_3": {
                "method": "fill_security_background_part3_security",
                "display_name": "Security Part 3 (Security)",
                "emoji": "🔒"
            },
            "security_background_4": {
                "method": "fill_security_background_part4_immigration",
                "display_name": "Security Part 4 (Immigration)",
                "emoji": "🛃"
            },
            "security_background_5": {
                "method": "fill_security_background_part5_miscellaneous",
                "display_name": "Security Part 5 (Miscellaneous)",
                "emoji": "📝"
            },
            
            # Special pages
            "photo_upload": {
                "method": None,  # Handled specially - requires manual intervention
                "display_name": "Photo Upload",
                "emoji": "🖼️",
                "skip_auto_handle": True
            },
            "recovery": {
                "method": None,
                "display_name": "Recovery Page", 
                "emoji": "🔄",
                "skip_auto_handle": True
            },
            "form_page": {
                "method": None,
                "display_name": "Generic Form Page",
                "emoji": "📄",
                "requires_analysis": True
            }
        }

    async def _handle_section_universal(self, current_section: str, data: DS160Data) -> bool:
        """
        Universal section handler that can process any DS-160 section.
        
        Args:
            current_section: The detected section name
            data: DS160 form data
            
        Returns:
            bool: True if successful, False otherwise
        """
        section_config = self._get_section_config()
        config = section_config.get(current_section)
        
        if not config:
            logger.warning(f"❓ Unknown section: {current_section}")
            return await self._handle_unknown_section(current_section)
        
        # Skip sections that require special handling
        if config.get("skip_auto_handle", False):
            logger.debug(f"⏭️ Section {current_section} requires special handling - skipping universal handler")
            return False
        
        # Get section info
        display_name = config["display_name"]
        emoji = config["emoji"]
        method_name = config["method"]
        
        if not method_name:
            logger.warning(f"⚠️ No method defined for section: {current_section}")
            return False
        
        logger.info(f"{emoji} Filling {display_name}")
        await take_screenshot(self.page, f"before_{current_section}.png")
        
        try:
            # Get the form filler method dynamically
            form_method = getattr(self.form_filler, method_name, None)
            if not form_method:
                logger.error(f"❌ Method {method_name} not found in form_filler")
                return False
            
            # Call the form filler method
            if not await form_method(data):
                logger.error(f"❌ Failed to fill {display_name}")
                return False
            
            logger.info(f"✅ {display_name} section completed successfully")
            
            # Update session state
            await self._update_session_after_section(current_section)
            
            # Continue to next section
            return await self._continue_form_filling(data)
            
        except Exception as e:
            logger.error(f"❌ Error filling {display_name}: {e}")
            await take_screenshot(self.page, f"error_{current_section}.png")
            return False
    
    async def _update_session_after_section(self, section_name: str) -> None:
        """Update session state after completing a section."""
        self.session_state["current_page"] = f"{section_name}_completed"
        if section_name not in self.session_state["completed_sections"]:
            self.session_state["completed_sections"].append(section_name)
        self.session_state["last_action"] = f"{section_name}_filled"
        
        session_file = self.browser_options.get('session_state_file', 'session_data/session_state.json')
        await save_session_state(self.session_state, session_file)
        logger.info(f"📄 Session state updated after {section_name}")
    
    async def _handle_unknown_section(self, section_name: str) -> bool:
        """Handle unknown sections with debugging information."""
        logger.warning(f"❓ Unknown section: {section_name}")
        logger.info("🔍 Page content for debugging:")
        
        # Get page text for debugging
        try:
            page_text = await self.page.text_content("body")
            logger.info(f"📄 Page text sample: {page_text[:500]}...")
            
            # Take screenshot for manual analysis
            await take_screenshot(self.page, f"unknown_section_{section_name}.png")
            
            # Try to detect if this looks like a form page
            form_elements = await self.page.query_selector_all("input, select, textarea")
            if len(form_elements) > 0:
                logger.info(f"🔍 Found {len(form_elements)} form elements - this appears to be a fillable form")
                logger.info("💡 Consider adding this section to the section configuration")
                
                # Log some form elements for analysis
                for i, element in enumerate(form_elements[:5]):
                    try:
                        tag = await element.evaluate("el => el.tagName")
                        name = await element.get_attribute("name") or "no-name"
                        element_type = await element.get_attribute("type") or "no-type"
                        logger.info(f"  Element {i+1}: {tag} name='{name}' type='{element_type}'")
                    except:
                        continue
            
        except Exception as e:
            logger.error(f"Error analyzing unknown section: {e}")
        
        return False

    def _get_current_section(self) -> str:
        """Determine current section based on URL analysis.
        
        Returns the actual section name based on current page URL,
        instead of relying on hardcoded initialization values.
        """
        if not self.page:
            return "unknown"
            
        current_url = self.page.url.lower()
        logger.debug(f"🔍 Analyzing URL for section detection: {current_url}")
        
        # Security Question / Application Confirmation pages (highest priority)
        if "confirmapplicationid.aspx" in current_url or "securequestion" in current_url:
            return "security_question"
        
        # Security pages (most specific first)
        if "securityandbackground5" in current_url:
            return "security_background_5"
        elif "securityandbackground4" in current_url:
            return "security_background_4"
        elif "securityandbackground3" in current_url:
            return "security_background_3"
        elif "securityandbackground2" in current_url:
            return "security_background_2"
        elif "securityandbackground1" in current_url:
            return "security_background_1"
        
        # Personal info pages
        elif "complete_personalcont" in current_url:
            return "personal_info_2"
        elif "complete_personal" in current_url:
            return "personal_info"
        
        # Contact and passport pages
        elif "complete_contact" in current_url:
            return "contact_info"
        elif "passport_visa_info" in current_url:
            return "passport_info"
        elif "complete_uscontact" in current_url:
            return "us_contact_info"
        
        # Travel pages
        elif "complete_travelcompanions" in current_url:
            return "travel_companions"
        elif "complete_previousustravel" in current_url:
            return "previous_us_travel"
        elif "complete_travel" in current_url:
            return "travel_info"
        
        # Family and work/education pages
        elif "complete_family1" in current_url:
            return "family_info"
        elif "complete_workeducation3" in current_url:
            return "work_education_additional"
        elif "complete_workeducation2" in current_url:
            return "previous_work_education"
        elif "complete_workeducation1" in current_url:
            return "work_education"
        
        # Photo upload page
        elif "photo_uploadthephoto.aspx" in current_url or "uploadphoto" in current_url:
            return "photo_upload"
        
        # Recovery and general pages
        elif "recovery.aspx" in current_url:
            return "recovery"
        elif "complete_" in current_url:
            # Generic complete page - try to extract more info
            if "security" in current_url:
                return "security_background"
            elif "personal" in current_url:
                return "personal_info"
            elif "work" in current_url or "education" in current_url:
                return "work_education"
            else:
                return "form_page"
        
        # Start/landing pages
        elif any(pattern in current_url for pattern in ["ds160.home", "genniv", "start", "select"]):
            return "start"
        
        else:
            logger.warning(f"🤔 Unknown URL pattern: {current_url}")
            return "unknown"
    
    async def _update_session_current_page(self, current_section: str) -> None:
        """Update session state with the real current page/section.
        
        Args:
            current_section: The detected current section name
        """
        try:
            # Update session state
            self.session_state["current_page"] = current_section
            self.session_state["updated_at"] = datetime.now().isoformat()
            
            # Save to file
            session_file = "session_state.json"
            await save_session_state(self.session_state, session_file)
            
            logger.debug(f"📄 Session state updated: current_page = {current_section}")
            
        except Exception as e:
            logger.warning(f"Failed to update session current page: {e}")
    
    async def _display_application_recovery_info(self, data: DS160Data) -> None:
        """Display application recovery information when reaching photo upload page.
        
        Args:
            data: DS160Data containing user information for recovery
            
        This shows the user critical information needed to recover/continue their application.
        """
        try:
            # Always try to extract Application ID from current page first (most reliable)
            app_id = "NOT_FOUND"
            try:
                # Look for application ID in page content with multiple patterns
                page_text = await self.page.text_content("body")
                import re
                
                # Try multiple patterns to find Application ID
                patterns = [
                    r'Application ID[:\s]*([A-Z0-9]{10,})',               # "Application ID: AA00EVX8J1"
                    r'Application\s+ID[:\s]*([A-Z0-9]{10,})',            # "Application ID AA00EVX8J1"
                    r'ID[:\s]*([A-Z]{2}[0-9A-Z]{8,})',                  # "ID: AA00EVX8J1"
                    r'([A-Z]{2}[0-9][0-9A-Z]{7,})',                     # Pattern: 2 letters + digit + 7+ chars
                    r'(\b[A-Z]{2}[0-9A-Z]{8,}\b)',                      # Word boundary pattern
                ]
                
                for pattern in patterns:
                    app_id_match = re.search(pattern, page_text, re.IGNORECASE)
                    if app_id_match:
                        extracted_id = app_id_match.group(1).upper()
                        # Validate format (starts with 2 letters, followed by numbers/letters)
                        if re.match(r'^[A-Z]{2}[0-9A-Z]{8,}$', extracted_id):
                            app_id = extracted_id
                            logger.info(f"📄 Extracted Application ID from page: {app_id} (pattern: {pattern})")
                            break
                
                # Also try to find it in page URL
                if app_id == "NOT_FOUND":
                    current_url = self.page.url
                    url_match = re.search(r'[?&].*?([A-Z]{2}[0-9A-Z]{8,})', current_url)
                    if url_match:
                        app_id = url_match.group(1).upper()
                        logger.info(f"📄 Extracted Application ID from URL: {app_id}")
                
            except Exception as e:
                logger.warning(f"Failed to extract Application ID from page: {e}")
            
            # Fallback to session state only if page extraction failed
            if app_id == "NOT_FOUND":
                app_id = self.session_state.get("application_id", "NOT_FOUND")
                if app_id and app_id != "NOT_FOUND":
                    logger.info(f"📄 Using Application ID from session state: {app_id}")
            
            # Update session state with the real Application ID if found
            if app_id != "NOT_FOUND" and app_id != self.session_state.get("application_id"):
                logger.info(f"📄 Updating session state with correct Application ID: {app_id}")
                self.session_state["application_id"] = app_id
            
            # Get user data for recovery information
            surname = "NOT_AVAILABLE"
            birth_year = "NOT_AVAILABLE"
            security_answer = "NOT_AVAILABLE"
            
            # Try to extract data from form data first (most reliable)
            if data:
                try:
                    # Extract surname (first part of surnames)
                    if data.personal_info and data.personal_info.surnames:
                        surname = data.personal_info.surnames.split()[0]
                        logger.info(f"📄 Extracted surname from data: {surname}")
                    
                    # Extract birth year
                    if data.personal_info and data.personal_info.date_of_birth:
                        birth_year = str(data.personal_info.date_of_birth.year)
                        logger.info(f"📄 Extracted birth year from data: {birth_year}")
                    
                    # Try to get security answer from application data if stored
                    if hasattr(data, 'security_question_answer') and data.security_question_answer:
                        security_answer = data.security_question_answer
                        logger.info(f"📄 Using security answer from data")
                    elif hasattr(self, 'application_data') and hasattr(self.application_data, 'security_question_answer'):
                        security_answer = self.application_data.security_question_answer
                        logger.info(f"📄 Using security answer from application_data")
                    
                except Exception as ex:
                    logger.warning(f"Failed to extract user data for recovery: {ex}")
            
            # Also try to extract information from the current page if available
            try:
                page_text = await self.page.text_content("body")
                
                # Try to find surname or name information on page
                if surname == "NOT_AVAILABLE":
                    name_patterns = [
                        r'Name[:\s]*([A-Z][A-Z\s]+)',
                        r'Surname[:\s]*([A-Z][A-Z\s]+)',
                        r'Last Name[:\s]*([A-Z][A-Z\s]+)',
                    ]
                    for pattern in name_patterns:
                        name_match = re.search(pattern, page_text, re.IGNORECASE)
                        if name_match:
                            surname = name_match.group(1).strip().split()[0]
                            logger.info(f"📄 Extracted surname from page: {surname}")
                            break
                
            except Exception as ex:
                logger.warning(f"Failed to extract additional data from page: {ex}")
            
            # Create prominent console output
            print("\n" + "="*80)
            print("🎯 DS-160 APPLICATION RECOVERY INFORMATION")
            print("="*80)
            print(f"📋 APPLICATION ID: {app_id}")
            print(f"👤 SURNAME: {surname}")
            print(f"📅 BIRTH YEAR: {birth_year}")
            print(f"🔐 SECURITY ANSWER: {security_answer}")
            print(f"🌐 CURRENT URL: {self.page.url}")
            print(f"📅 TIMESTAMP: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*80)
            print("📝 INSTRUCTIONS:")
            print("   1. Save this information in a secure location")
            print("   2. Use this data to recover your application later")
            print("   3. Complete photo upload manually")
            print("   4. Continue with remaining DS-160 sections")
            print("="*80)
            print("🔄 TO RESUME APPLICATION:")
            print("   - Go to: https://ceac.state.gov/genniv/")
            print(f"   - Enter Application ID: {app_id}")
            print(f"   - Enter Surname: {surname}")
            print(f"   - Enter Birth Year: {birth_year}")
            print(f"   - Answer Security Question: {security_answer}")
            print("="*80 + "\n")
            
            # Also log this information
            logger.info("🎯 APPLICATION RECOVERY INFORMATION DISPLAYED")
            logger.info(f"📋 Application ID: {app_id}")
            logger.info(f"👤 Surname: {surname}")
            logger.info(f"📅 Birth Year: {birth_year}")
            logger.info(f"🔐 Security Answer: {security_answer}")
            logger.info(f"🌐 Current URL: {self.page.url}")
            
            # Update session state with recovery info
            self.session_state["recovery_info"] = {
                "application_id": app_id,
                "surname": surname,
                "birth_year": birth_year,
                "security_answer": security_answer,
                "photo_upload_url": self.page.url,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to display application recovery info: {e}")
            # Fallback - at least show what we have
            app_id = self.session_state.get("application_id", "NOT_FOUND")
            print(f"\n🚨 CRITICAL: Save this Application ID: {app_id}")
            print(f"🌐 Current URL: {self.page.url}")
            print("📝 Complete photo upload manually and continue application\n") 