"""Configuration management for DS-160 bot."""
import os
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
from pydantic import BaseModel, Field
from dotenv import load_dotenv

load_dotenv()

class BrowserConfig(BaseModel):
    """Browser configuration settings."""
    headless: bool = Field(default=True, description="Run browser in headless mode")
    browser_type: str = Field(default="chromium", description="Browser type (chromium, firefox, webkit)")
    viewport_width: int = Field(default=1920, description="Browser viewport width")
    viewport_height: int = Field(default=1080, description="Browser viewport height")
    user_agent: Optional[str] = Field(default=None, description="Custom user agent")
    timeout: int = Field(default=30000, description="Default timeout in milliseconds")

class RetryConfig(BaseModel):
    """Retry configuration settings."""
    max_attempts: int = Field(default=3, description="Maximum retry attempts")
    base_delay: float = Field(default=1.0, description="Base delay for exponential backoff")
    max_delay: float = Field(default=60.0, description="Maximum delay between retries")
    timeout: int = Field(default=10000, description="Timeout for individual operations")

class LoggingConfig(BaseModel):
    """Logging configuration settings."""
    level: str = Field(default="INFO", description="Logging level")
    log_to_file: bool = Field(default=True, description="Enable file logging")
    log_file: str = Field(default="logs/ds160_bot.log", description="Log file path")
    max_file_size: str = Field(default="10 MB", description="Maximum log file size")
    retention: str = Field(default="7 days", description="Log retention period")

class SecurityConfig(BaseModel):
    """Security and safety configuration."""
    auto_submit: bool = Field(default=False, description="Allow automatic form submission")
    save_screenshots: bool = Field(default=True, description="Save screenshots on errors")
    mask_sensitive_data: bool = Field(default=True, description="Mask sensitive data in logs")
    session_timeout: int = Field(default=1800, description="Session timeout in seconds")

class Config(BaseModel):
    """Main configuration class."""
    browser: BrowserConfig = Field(default_factory=BrowserConfig)
    retry: RetryConfig = Field(default_factory=RetryConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    # Data paths
    data_file: str = Field(default="data/example_data.json", description="Input data JSON file")
    selectors_file: str = Field(default="config/selectors.yaml", description="CSS selectors config file")
    
    # DS-160 specific
    ds160_url: str = Field(default="https://ceac.state.gov/genniv/", description="DS-160 application URL")
    captcha_service_api_key: Optional[str] = Field(default=None, description="CAPTCHA solving service API key")

    @classmethod
    def load_from_file(cls, config_path: str = "config/config.yaml") -> "Config":
        """Load configuration from YAML file."""
        config_file = Path(config_path)
        if not config_file.exists():
            # Create default config if it doesn't exist
            default_config = cls()
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, 'w') as f:
                yaml.dump(default_config.model_dump(), f, default_flow_style=False)
            return default_config
        
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)

    @classmethod
    def load_from_env(cls) -> "Config":
        """Load configuration from environment variables."""
        config_data = {}
        
        # Browser config
        if os.getenv("HEADLESS"):
            config_data["browser"] = {"headless": os.getenv("HEADLESS", "true").lower() == "true"}
        
        # Security config
        if os.getenv("AUTO_SUBMIT"):
            config_data["security"] = {"auto_submit": os.getenv("AUTO_SUBMIT", "false").lower() == "true"}
        
        # Data file
        if os.getenv("DATA_FILE"):
            config_data["data_file"] = os.getenv("DATA_FILE")
        
        # CAPTCHA API key
        if os.getenv("CAPTCHA_API_KEY"):
            config_data["captcha_service_api_key"] = os.getenv("CAPTCHA_API_KEY")
        
        return cls(**config_data)

def get_config() -> Config:
    """Get configuration with environment variable overrides."""
    config = Config.load_from_file()
    env_config = Config.load_from_env()
    
    # Merge configurations (env takes precedence)
    merged_data = config.model_dump()
    env_data = env_config.model_dump()
    
    def deep_update(base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        for key, value in update.items():
            if isinstance(value, dict) and key in base and isinstance(base[key], dict):
                base[key] = deep_update(base[key], value)
            else:
                base[key] = value
        return base
    
    merged_data = deep_update(merged_data, env_data)
    return Config(**merged_data) 