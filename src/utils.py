"""Utility functions for DS-160 bot."""
import asyncio
import random
import re
from datetime import datetime, date
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from playwright.async_api import Page, ElementHandle, Locator
from loguru import logger
import json
from pydantic import BaseModel


class ApplicationData(BaseModel):
    """Key application data for resume functionality."""
    application_id: Optional[str] = None
    surname: Optional[str] = None
    security_question_answer: Optional[str] = None
    year_of_birth: Optional[int] = None
    created_at: datetime = datetime.now()
    last_updated: datetime = datetime.now()
    current_section: Optional[str] = None
    current_url: Optional[str] = None


async def wait_for_element(
    page: Page,
    selector: str,
    timeout: int = 30000,
    state: str = "visible"
) -> Optional[Locator]:
    """Wait for element to be in specified state."""
    try:
        element = page.locator(selector)
        await element.wait_for(state=state, timeout=timeout)
        return element
    except Exception as e:
        logger.warning(f"Element not found: {selector} - {e}")
        return None


async def wait_for_page_stability(page: Page, timeout: int = 15000) -> bool:
    """Wait for page to be stable (no more loading indicators)."""
    try:
        logger.debug("⏳ Waiting for page stability...")
        
        # Wait for basic page load
        await page.wait_for_load_state("domcontentloaded", timeout=timeout)
        
        # Wait for any loading indicators to disappear
        loading_selectors = [
            ".loading", "[id*='loading']", "[class*='loading']",
            ".spinner", "[id*='spinner']", "[class*='spinner']",
            "#aspnetForm[disabled]", "form[disabled]"  # Common DS-160 loading state
        ]
        
        for selector in loading_selectors:
            try:
                # Wait for loading indicator to be hidden (if it exists)
                element = page.locator(selector)
                if await element.count() > 0:
                    logger.debug(f"⏳ Waiting for loading indicator to disappear: {selector}")
                    await element.wait_for(state="hidden", timeout=5000)
            except:
                pass  # Ignore if selector doesn't exist
        
        # Additional delay for form elements to become interactive
        await asyncio.sleep(2)
        
        logger.debug("✅ Page appears stable")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ Page stability check timed out: {e}")
        return False

async def safe_click(
    page: Page,
    selector: str,
    timeout: int = 20000,  # Increased from 10s to 20s
    force: bool = False
) -> bool:
    """Safely click an element with retries."""
    try:
        element = await wait_for_element(page, selector, timeout)
        if element:
            await element.click(force=force)
            await asyncio.sleep(0.1)  # Small delay after click
            return True
        return False
    except Exception as e:
        logger.error(f"Failed to click {selector}: {e}")
        return False

async def safe_fill(
    page: Page,
    selector: str,
    value: str,
    timeout: int = 10000,
    clear: bool = True
) -> bool:
    """Safely fill an input field."""
    try:
        element = await wait_for_element(page, selector, timeout)
        if element:
            if clear:
                await element.clear()
            await element.fill(value)
            await asyncio.sleep(0.1)  # Small delay after fill
            return True
        return False
    except Exception as e:
        logger.error(f"Failed to fill {selector} with '{mask_sensitive(value)}': {e}")
        return False

async def safe_fill_slow(
    page: Page,
    selector: str,
    value: str,
    timeout: int = 10000,
    clear: bool = True,
    typing_delay: float = 0.1
) -> bool:
    """Slowly fill an input field with human-like typing for DS-160 JavaScript validation."""
    try:
        element = await wait_for_element(page, selector, timeout)
        if element:
            if clear:
                await element.clear()
                await asyncio.sleep(0.2)  # Wait after clear
            
            # Focus the element first
            await element.focus()
            await asyncio.sleep(0.1)
            
            # Type slowly character by character
            for char in value:
                await element.type(char, delay=typing_delay * 1000)  # Convert to ms
                await asyncio.sleep(random.uniform(0.05, 0.15))  # Random micro-delay
            
            # Wait a bit and verify the value was set
            await asyncio.sleep(0.1)
            
            # Trigger blur event to ensure JS validation runs
            await element.blur()
            await asyncio.sleep(0.1)
            
            # Verify value was actually set
            current_value = await element.input_value()
            if current_value == value:
                logger.debug(f"Successfully filled {selector} with slow typing")
                return True
            else:
                logger.warning(f"Value mismatch after slow fill: expected '{value}', got '{current_value}'")
                return False
                
        return False
    except Exception as e:
        logger.error(f"Failed to slow fill {selector} with '{mask_sensitive(value)}': {e}")
        return False

async def safe_select(
    page: Page,
    selector: str,
    value: str,
    timeout: int = 20000  # Increased from 10s to 20s
) -> bool:
    """Safely select an option from dropdown."""
    try:
        element = await wait_for_element(page, selector, timeout)
        if element:
            await element.select_option(value=value)
            await asyncio.sleep(0.1)
            return True
        return False
    except Exception as e:
        logger.error(f"Failed to select '{value}' in {selector}: {e}")
        return False

async def safe_select_by_text(
    page: Page,
    selector: str,
    text: str,
    timeout: int = 20000  # Increased from 10s to 20s
) -> bool:
    """Safely select an option by visible text."""
    try:
        element = await wait_for_element(page, selector, timeout)
        if element:
            await element.select_option(label=text)
            await asyncio.sleep(0.1)
            return True
        return False
    except Exception as e:
        logger.error(f"Failed to select text '{text}' in {selector}: {e}")
        return False

async def get_element_text(
    page: Page,
    selector: str,
    timeout: int = 5000
) -> Optional[str]:
    """Get text content of an element."""
    try:
        element = await wait_for_element(page, selector, timeout)
        if element:
            return await element.text_content()
        return None
    except Exception as e:
        logger.warning(f"Failed to get text from {selector}: {e}")
        return None

async def element_exists(
    page: Page,
    selector: str,
    timeout: int = 5000
) -> bool:
    """Check if element exists on page."""
    try:
        element = await wait_for_element(page, selector, timeout)
        return element is not None
    except Exception:
        return False

async def wait_for_page_load(
    page: Page,
    timeout: int = 30000,
    wait_for_selector: Optional[str] = None
) -> bool:
    """Wait for page to fully load."""
    try:
        # Wait for network to be idle
        await page.wait_for_load_state("networkidle", timeout=timeout)
        
        # If specific selector provided, wait for it
        if wait_for_selector:
            await wait_for_element(page, wait_for_selector, timeout)
        
        await asyncio.sleep(0.2)  # Additional safety delay
        return True
    except Exception as e:
        logger.warning(f"Page load timeout: {e}")
        return False

async def take_screenshot(
    page: Page,
    filename: str,
    full_page: bool = True
) -> Optional[str]:
    """Take screenshot and save to file."""
    try:
        screenshot_dir = Path("screenshots")
        screenshot_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = screenshot_dir / f"{timestamp}_{filename}"
        
        await page.screenshot(path=str(filepath), full_page=full_page)
        logger.info(f"Screenshot saved: {filepath}")
        return str(filepath)
    except Exception as e:
        logger.error(f"Failed to take screenshot: {e}")
        return None

async def handle_captcha(
    page: Page,
    captcha_selector: str = "img[id*='captcha']",
    input_selector: str = "input[name='captcha']"
) -> bool:
    """Handle CAPTCHA with smart detection of manual solving."""
    try:
        # Check if CAPTCHA exists
        if not await element_exists(page, captcha_selector):
            return True
        
        logger.warning("CAPTCHA detected - manual intervention required")
        
        # Take screenshot of CAPTCHA
        await take_screenshot(page, "captcha_detected.png")
        
        # Store initial URL to detect page changes
        initial_url = page.url
        logger.info(f"CAPTCHA page URL: {initial_url}")
        
        # Get detailed page information for debugging
        page_info = await get_captcha_page_info(page)
        logger.debug(f"CAPTCHA page analysis:")
        logger.debug(f"  Title: {page_info['title']}")
        logger.debug(f"  Found {len(page_info['captcha_elements'])} CAPTCHA image(s)")
        logger.debug(f"  Found {len(page_info['input_elements'])} CAPTCHA input(s)")
        logger.debug(f"  Found {len(page_info['button_elements'])} relevant button(s)")
        if page_info['error_messages']:
            logger.debug(f"  Error messages: {page_info['error_messages']}")
        
        # Smart user instructions
        logger.info("🔍 CAPTCHA Resolution Options:")
        logger.info("  1. Fill the CAPTCHA field and continue")
        logger.info("  2. Solve manually and navigate to the next page")
        logger.info("  3. Leave CAPTCHA field empty if you've already solved it manually")
        logger.info("⏳ Waiting for CAPTCHA resolution (max 5 minutes)...")
        
        # Wait for CAPTCHA to be solved with multiple detection methods
        start_time = datetime.now()
        timeout = 300  # 5 minutes
        check_interval = 2  # Check every 2 seconds
        
        while (datetime.now() - start_time).seconds < timeout:
            try:
                # Use smart detection to check all resolution methods
                detection_result = await smart_captcha_detection(page, initial_url)
                
                if detection_result["resolved"]:
                    method = detection_result["method"]
                    details = detection_result["details"]
                    
                    if method == "url_change":
                        logger.info(f"✅ CAPTCHA resolved: {details}")
                        logger.info("🎉 User appears to have solved CAPTCHA manually and proceeded")
                    elif method == "captcha_disappeared":
                        logger.info(f"✅ CAPTCHA resolved: {details}")
                    elif method == "input_filled":
                        logger.info(f"✅ CAPTCHA resolved: {details}")
                    elif method == "success_indicator":
                        logger.info(f"✅ CAPTCHA resolved: {details}")
                    else:
                        logger.info(f"✅ CAPTCHA resolved via {method}: {details}")
                    
                    return True
                
                # Log current status every 30 seconds
                elapsed = (datetime.now() - start_time).seconds
                if elapsed > 0 and elapsed % 30 == 0:
                    remaining = timeout - elapsed
                    logger.info(f"⏳ Still waiting for CAPTCHA resolution... {remaining}s remaining")
                    logger.debug(f"Current URL: {detection_result['current_url']}")
                    if detection_result.get("details"):
                        logger.debug(f"Detection details: {detection_result['details']}")
                
                await asyncio.sleep(check_interval)
                
            except Exception as e:
                logger.debug(f"CAPTCHA check iteration error: {e}")
                await asyncio.sleep(check_interval)
        
        # Timeout reached
        current_url = page.url
        if current_url != initial_url:
            logger.info(f"⚠️ CAPTCHA timeout but page changed: {initial_url} → {current_url}")
            logger.info("Assuming CAPTCHA was resolved manually")
            return True
        
        logger.error("❌ CAPTCHA timeout - no resolution detected")
        logger.info("🔄 Continuing anyway - user may have solved it in another way")
        return False
        
    except Exception as e:
        logger.error(f"CAPTCHA handling error: {e}")
        return False


async def smart_captcha_detection(page: Page, initial_url: str) -> Dict[str, Any]:
    """Advanced CAPTCHA detection with multiple resolution methods.
    
    Returns:
        Dict with detection results and method used
    """
    result = {
        "resolved": False,
        "method": None,
        "current_url": page.url,
        "url_changed": False,
        "details": None
    }
    
    try:
        current_url = page.url
        result["current_url"] = current_url
        result["url_changed"] = current_url != initial_url
        
        # Method 1: URL change detection (highest priority)
        if current_url != initial_url:
            result["resolved"] = True
            result["method"] = "url_change"
            result["details"] = f"Page navigated from {initial_url} to {current_url}"
            return result
        
        # Method 2: CAPTCHA image disappeared
        captcha_selectors = [
            "img[id*='captcha']",
            "img[src*='captcha']", 
            "img[alt*='captcha']",
            "img[class*='captcha']",
            ".captcha img",
            "#captcha img"
        ]
        
        captcha_found = False
        for selector in captcha_selectors:
            if await element_exists(page, selector, timeout=1000):
                captcha_found = True
                break
        
        if not captcha_found:
            result["resolved"] = True
            result["method"] = "captcha_disappeared"
            result["details"] = "CAPTCHA image no longer present on page"
            return result
        
        # Method 3: CAPTCHA input field filled
        input_selectors = [
            "input[name='captcha']",
            "input[id*='captcha']",
            "input[class*='captcha']",
            ".captcha input",
            "#captcha input"
        ]
        
        for selector in input_selectors:
            try:
                if await element_exists(page, selector, timeout=1000):
                    element = page.locator(selector)
                    value = await element.input_value()
                    if value and value.strip():
                        result["resolved"] = True
                        result["method"] = "input_filled"
                        result["details"] = f"CAPTCHA input filled via {selector}"
                        return result
            except Exception:
                continue
        
        # Method 4: Check for form submission success indicators
        success_indicators = [
            # Success messages
            ".success",
            ".alert-success",
            "[class*='success']",
            # Progress indicators
            ".progress",
            ".step-complete",
            # New form sections appearing
            "form[action*='next']",
            "input[name*='continue']",
            "button[name*='continue']"
        ]
        
        for selector in success_indicators:
            if await element_exists(page, selector, timeout=500):
                try:
                    element = page.locator(selector)
                    if await element.is_visible():
                        result["resolved"] = True
                        result["method"] = "success_indicator"
                        result["details"] = f"Success indicator found: {selector}"
                        return result
                except Exception:
                    continue
        
        # Method 5: Check page title changes
        try:
            page_title = await page.title()
            if page_title and ("error" not in page_title.lower()) and ("captcha" not in page_title.lower()):
                # If title doesn't contain error/captcha, might indicate progress
                result["details"] = f"Page title: {page_title}"
        except Exception:
            pass
        
        return result
        
    except Exception as e:
        result["details"] = f"Detection error: {e}"
        return result


async def get_captcha_page_info(page: Page) -> Dict[str, Any]:
    """Get comprehensive information about current page state for CAPTCHA debugging."""
    info = {
        "url": page.url,
        "title": None,
        "captcha_elements": [],
        "input_elements": [],
        "button_elements": [],
        "form_elements": [],
        "error_messages": []
    }
    
    try:
        info["title"] = await page.title()
    except Exception:
        pass
    
    # Find CAPTCHA-related elements
    captcha_selectors = [
        "img[id*='captcha']", "img[src*='captcha']", "img[alt*='captcha']",
        "img[class*='captcha']", ".captcha img", "#captcha img"
    ]
    
    for selector in captcha_selectors:
        try:
            if await element_exists(page, selector, timeout=1000):
                element = page.locator(selector)
                src = await element.get_attribute("src") or ""
                alt = await element.get_attribute("alt") or ""
                info["captcha_elements"].append({
                    "selector": selector,
                    "src": src[:50] + "..." if len(src) > 50 else src,
                    "alt": alt,
                    "visible": await element.is_visible()
                })
        except Exception:
            continue
    
    # Find input elements
    input_selectors = [
        "input[name*='captcha']", "input[id*='captcha']", 
        "input[class*='captcha']", ".captcha input", "#captcha input"
    ]
    
    for selector in input_selectors:
        try:
            if await element_exists(page, selector, timeout=1000):
                element = page.locator(selector)
                value = await element.input_value() or ""
                info["input_elements"].append({
                    "selector": selector,
                    "value_length": len(value),
                    "has_value": bool(value.strip()),
                    "visible": await element.is_visible(),
                    "enabled": await element.is_enabled()
                })
        except Exception:
            continue
    
    # Find relevant buttons
    button_selectors = [
        "input[type='submit']", "button[type='submit']",
        "input[value*='Continue']", "button[value*='Continue']",
        "input[name*='continue']", "button[name*='continue']"
    ]
    
    for selector in button_selectors:
        try:
            if await element_exists(page, selector, timeout=1000):
                element = page.locator(selector)
                value = await element.get_attribute("value") or ""
                text = await element.text_content() or ""
                info["button_elements"].append({
                    "selector": selector,
                    "value": value,
                    "text": text,
                    "visible": await element.is_visible(),
                    "enabled": await element.is_enabled()
                })
        except Exception:
            continue
    
    # Find forms
    try:
        forms = await page.query_selector_all("form")
        for i, form in enumerate(forms):
            action = await form.get_attribute("action") or ""
            method = await form.get_attribute("method") or ""
            info["form_elements"].append({
                "index": i,
                "action": action,
                "method": method
            })
    except Exception:
        pass
    
    # Check for error messages
    try:
        info["error_messages"] = await check_form_errors(page)
    except Exception:
        pass
    
    return info


def format_date_for_form(date_obj: date, format_type: str = "ds160") -> Dict[str, str]:
    """Format date object for form fields."""
    if date_obj is None:
        return {"day": "", "month": "", "year": ""}  # Return empty values for None dates
    
    if format_type == "ds160":
        # DS-160 uses format: Day as "01", Month as "JAN", Year as "1990"
        month_names = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 
                      'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']
        return {
            "day": f"{date_obj.day:02d}",  # "01", "02", etc.
            "month": month_names[date_obj.month - 1],  # "JAN", "FEB", etc.
            "year": str(date_obj.year)  # "1990"
        }
    elif format_type == "mdy":
        return {
            "month": str(date_obj.month),
            "day": str(date_obj.day),
            "year": str(date_obj.year)
        }
    elif format_type == "dmy":
        return {
            "day": str(date_obj.day),
            "month": str(date_obj.month),
            "year": str(date_obj.year)
        }
    else:
        raise ValueError(f"Unsupported date format: {format_type}")

def mask_sensitive(value: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """Mask sensitive information for logging."""
    if not value or len(value) <= visible_chars:
        return mask_char * len(value) if value else ""
    
    return value[:visible_chars] + mask_char * (len(value) - visible_chars)

def validate_email(email: str) -> bool:
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def format_phone_for_ds160(phone: str) -> str:
    """Format phone number for DS-160 form (digits only, 5-15 characters).
    
    DS-160 requirements:
    - Only digits (no spaces, hyphens, parentheses)
    - 5-15 digits total
    - Remove country code prefixes like +7, +1, etc.
    """
    if not phone:
        return ""
    
    # Remove all non-digit characters
    cleaned = re.sub(r'[^\d]', '', phone)
    
    # Remove common country code prefixes for international numbers
    # +7 (Kazakhstan, Russia), +1 (US/Canada), +44 (UK), etc.
    if cleaned.startswith('7') and len(cleaned) > 10:  # Kazakhstan/Russia +7
        cleaned = cleaned[1:]
    elif cleaned.startswith('1') and len(cleaned) == 11:  # US/Canada +1
        cleaned = cleaned[1:]
    elif cleaned.startswith('44') and len(cleaned) > 10:  # UK +44
        cleaned = cleaned[2:]
    
    # Ensure it's within DS-160 limits (5-15 digits)
    if len(cleaned) < 5:
        # If too short, return empty (will trigger "Does not apply" logic)
        return ""
    elif len(cleaned) > 15:
        # If too long, truncate to 15 digits
        cleaned = cleaned[:15]
    
    return cleaned

def validate_phone(phone: str) -> bool:
    """Validate phone number format."""
    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\d+]', '', phone)
    
    # Check if it's a valid international format
    if cleaned.startswith('+'):
        return len(cleaned) >= 8 and len(cleaned) <= 15
    else:
        return len(cleaned) >= 7 and len(cleaned) <= 15

async def retry_async(
    func,
    *args,
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    **kwargs
):
    """Retry async function with exponential backoff."""
    last_exception = None
    
    for attempt in range(max_attempts):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            
            if attempt == max_attempts - 1:
                break
            
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
            jitter = random.uniform(0.1, 0.3) * delay
            total_delay = delay + jitter
            
            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {total_delay:.2f}s...")
            await asyncio.sleep(total_delay)
    
    raise last_exception

def create_session_state() -> Dict[str, Any]:
    """Create initial session state."""
    return {
        "started_at": datetime.now().isoformat(),
        "current_page": "start",
        "completed_sections": [],
        "errors": [],
        "form_data": {},
        "last_action": None,
        "session_id": None,
        "browser_profile_dir": None,
        "data_file_hash": None
    }

async def save_session_state(state: Dict[str, Any], filepath: str = "session_state.json") -> None:
    """Save session state to file."""
    try:
        state["updated_at"] = datetime.now().isoformat()
        
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2, default=str)
        
        logger.debug(f"Session state saved to {filepath}")
    except Exception as e:
        logger.error(f"Failed to save session state: {e}")

async def load_session_state(filepath: str = "session_state.json") -> Optional[Dict[str, Any]]:
    """Load session state from file."""
    try:
        if Path(filepath).exists():
            with open(filepath, 'r') as f:
                state = json.load(f)
            logger.debug(f"Session state loaded from {filepath}")
            return state
        return None
    except Exception as e:
        logger.error(f"Failed to load session state: {e}")
        return None

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for cross-platform compatibility."""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Limit length
    if len(filename) > 255:
        name, ext = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        filename = name[:255-len(ext)-1] + '.' + ext if ext else name[:255]
    
    return filename

async def check_form_errors(page: Page) -> List[str]:
    """Check for form validation errors on current page."""
    error_selectors = [
        ".error",
        ".validation-error",
        "[class*='error']",
        ".field-validation-error",
        ".alert-danger"
    ]
    
    errors = []
    
    for selector in error_selectors:
        try:
            error_elements = page.locator(selector)
            count = await error_elements.count()
            
            for i in range(count):
                error_text = await error_elements.nth(i).text_content()
                if error_text and error_text.strip():
                    errors.append(error_text.strip())
        except Exception:
            continue
    
    return errors


async def parse_validation_errors(page: Page) -> Dict[str, List[str]]:
    """Parse validation errors and map them to field categories.
    
    Returns:
        Dict mapping field categories to error messages
    """
    errors = await check_form_errors(page)
    
    # Field mapping patterns - maps error keywords to field categories
    field_error_patterns = {
        "surnames": ["surname", "last name", "family name", "father", "mother"],
        "given_names": ["given name", "first name", "fname"],
        "date_of_birth": ["date of birth", "birth date", "dob", "birth"],
        "status": ["status", "immigration status", "us status"],
        "address": ["address", "street", "city", "state", "country"],
        "phone": ["phone", "telephone", "mobile"],
        "email": ["email", "e-mail"],
        "passport": ["passport", "document"],
        "travel": ["travel", "arrival", "departure", "flight"],
        "work": ["work", "employment", "employer", "occupation"],
        "education": ["education", "school", "university", "study"]
    }
    
    parsed_errors = {}
    
    for error in errors:
        error_lower = error.lower()
        categorized = False
        
        for field_category, keywords in field_error_patterns.items():
            for keyword in keywords:
                if keyword in error_lower:
                    if field_category not in parsed_errors:
                        parsed_errors[field_category] = []
                    parsed_errors[field_category].append(error)
                    categorized = True
                    break
            if categorized:
                break
        
        # If no category found, add to "unknown" category
        if not categorized:
            if "unknown" not in parsed_errors:
                parsed_errors["unknown"] = []
            parsed_errors["unknown"].append(error)
    
    return parsed_errors


async def verify_field_value(
    page: Page,
    selector: str,
    expected_value: str,
    timeout: int = 5000
) -> Dict[str, Any]:
    """Verify if a field contains the expected value after filling.
    
    Returns:
        Dict with verification results including actual value, match status, etc.
    """
    result = {
        "selector": selector,
        "expected": expected_value,
        "actual": None,
        "matches": False,
        "field_exists": False,
        "field_visible": False,
        "field_enabled": False,
        "error": None
    }
    
    try:
        element = await wait_for_element(page, selector, timeout)
        if not element:
            result["error"] = "Element not found"
            return result
            
        result["field_exists"] = True
        result["field_visible"] = await element.is_visible()
        result["field_enabled"] = await element.is_enabled()
        
        # Get actual value based on element type
        tag_name = await element.evaluate("el => el.tagName.toLowerCase()")
        
        if tag_name == "input":
            input_type = await element.get_attribute("type") or "text"
            if input_type in ["text", "email", "tel", "password"]:
                result["actual"] = await element.input_value()
            elif input_type in ["radio", "checkbox"]:
                result["actual"] = "checked" if await element.is_checked() else "unchecked"
        elif tag_name == "select":
            result["actual"] = await element.evaluate("el => el.value")
        elif tag_name == "textarea":
            result["actual"] = await element.input_value()
        else:
            result["actual"] = await element.text_content()
        
        # Check if values match (handle different data types)
        if result["actual"] is not None:
            actual_str = str(result["actual"]).strip()
            expected_str = str(expected_value).strip()
            result["matches"] = actual_str == expected_str
        
        logger.debug(f"Field verification: {selector} - Expected: '{expected_value}', Actual: '{result['actual']}', Matches: {result['matches']}")
        
    except Exception as e:
        result["error"] = str(e)
        logger.error(f"Error verifying field {selector}: {e}")
    
    return result


async def retry_problematic_fields(
    page: Page,
    field_attempts: List[Dict[str, Any]],
    max_strategies: int = 3
) -> List[Dict[str, Any]]:
    """Retry filling problematic fields using progressive strategies.
    
    Args:
        field_attempts: List of dicts with 'selector', 'value', 'success' keys
        max_strategies: Maximum number of strategies to try
        
    Returns:
        Updated field_attempts list with retry results
    """
    strategies = [
        {"name": "normal", "func": safe_fill, "kwargs": {}},
        {"name": "slow", "func": safe_fill_slow, "kwargs": {"typing_delay": 0.2}},
        {"name": "focus_blur", "func": safe_fill_with_focus_blur, "kwargs": {}},
    ]
    
    retry_results = []
    
    for attempt in field_attempts:
        if attempt.get("success", False):
            retry_results.append(attempt)
            continue
            
        selector = attempt["selector"]
        value = attempt["value"]
        
        logger.info(f"🔄 Retrying problematic field: {selector}")
        
        for i, strategy in enumerate(strategies[:max_strategies]):
            strategy_name = strategy["name"]
            fill_func = strategy["func"]
            kwargs = strategy["kwargs"]
            
            logger.debug(f"  Trying strategy {i+1}/{max_strategies}: {strategy_name}")
            
            try:
                success = await fill_func(page, selector, value, **kwargs)
                if success:
                    # Verify the value stuck
                    verification = await verify_field_value(page, selector, value)
                    if verification["matches"]:
                        logger.info(f"  ✅ Strategy '{strategy_name}' succeeded for {selector}")
                        attempt["success"] = True
                        attempt["retry_strategy"] = strategy_name
                        attempt["verification"] = verification
                        break
                    else:
                        logger.warning(f"  ⚠️ Strategy '{strategy_name}' filled but value didn't stick: {verification}")
                else:
                    logger.warning(f"  ❌ Strategy '{strategy_name}' failed for {selector}")
                    
            except Exception as e:
                logger.error(f"  💥 Strategy '{strategy_name}' threw error: {e}")
                
            # Small delay between strategies
            await asyncio.sleep(0.1)
        
        retry_results.append(attempt)
    
    # Summary
    successful_retries = sum(1 for r in retry_results if r.get("success", False))
    total_fields = len(retry_results)
    logger.info(f"🔄 Retry summary: {successful_retries}/{total_fields} fields successful")
    
    return retry_results


async def safe_fill_aspnet_field(
    page: Page,
    selectors: List[str],
    value: str,
    field_name: str = "field",
    timeout: int = 15000,
    verify_value: bool = True
) -> bool:
    """Fill ASP.NET form field with proper event handling and validation.
    
    This function handles:
    - Multiple selector fallbacks
    - ASP.NET onchange event triggering
    - ViewState preservation
    - Value verification
    - Anti-detection timing
    """
    logger.info(f"🔧 [ASPNET_FILL] Starting to fill {field_name} with value: '{mask_sensitive(value)}'")
    
    for i, selector in enumerate(selectors):
        logger.info(f"🔍 [ASPNET_FILL] Trying selector {i+1}/{len(selectors)}: {selector}")
        
        try:
            # Check if element exists
            element_count = await page.locator(selector).count()
            if element_count == 0:
                logger.warning(f"⚠️ [ASPNET_FILL] Element not found: {selector}")
                continue
                
            # Wait for element to be ready
            element = await wait_for_element(page, selector, timeout)
            if not element:
                logger.warning(f"⚠️ [ASPNET_FILL] Element not ready: {selector}")
                continue
            
            # Check if element is visible and enabled
            is_visible = await element.is_visible()
            is_enabled = await element.is_enabled()
            
            if not is_visible:
                logger.warning(f"⚠️ [ASPNET_FILL] Element not visible: {selector}")
                continue
                
            if not is_enabled:
                logger.warning(f"⚠️ [ASPNET_FILL] Element not enabled: {selector}")
                continue
            
            logger.info(f"✅ [ASPNET_FILL] Element found and ready: {selector}")
            
            # ASP.NET-specific filling sequence
            success = await _fill_aspnet_element(page, element, value, field_name)
            
            if success and verify_value:
                # Verify the value was actually set
                try:
                    filled_value = await element.input_value()
                    if filled_value and filled_value.strip() == value.strip():
                        logger.info(f"✅ [ASPNET_FILL] {field_name} filled and verified successfully")
                        return True
                    else:
                        logger.warning(f"⚠️ [ASPNET_FILL] Value verification failed. Expected: '{value}', Got: '{filled_value}'")
                except Exception as e:
                    logger.warning(f"⚠️ [ASPNET_FILL] Could not verify value: {e}")
                    # If we can't verify but filling succeeded, assume it worked
                    return True
            elif success:
                logger.info(f"✅ [ASPNET_FILL] {field_name} filled successfully (verification skipped)")
                return True
                
        except Exception as e:
            logger.error(f"❌ [ASPNET_FILL] Error with selector {i+1}: {e}")
            continue
    
    logger.error(f"❌ [ASPNET_FILL] All selectors failed for {field_name}")
    return False


async def _fill_aspnet_element(
    page: Page,
    element,
    value: str,
    field_name: str
) -> bool:
    """Fill a single ASP.NET element with proper event handling."""
    try:
        # Step 1: Scroll element into view
        await element.scroll_into_view_if_needed()
        await human_like_delay(0.2, 0.4)
        
        # Step 2: Focus with human-like delay
        await element.focus()
        await human_like_delay(0.3, 0.6)
        
        # Step 3: Clear existing value
        try:
            current_value = await element.input_value()
            if current_value:
                logger.info(f"🧹 [ASPNET_FILL] Clearing existing value: '{current_value}'")
                await element.select_all()
                await human_like_delay(0.1, 0.2)
                await page.keyboard.press('Delete')
                await human_like_delay(0.2, 0.3)
        except Exception as e:
            logger.debug(f"Could not clear existing value: {e}")
        
        # Step 4: Type value slowly (human-like)
        logger.info(f"⌨️ [ASPNET_FILL] Typing value: '{mask_sensitive(value)}'")
        await element.type(value, delay=randomize_typing_delay())
        await human_like_delay(0.3, 0.5)
        
        # Step 5: Trigger ASP.NET onchange event
        await element.evaluate("element => element.onchange && element.onchange()")
        await human_like_delay(0.2, 0.4)
        
        # Step 6: Simulate Tab key to move focus (triggers validation)
        await page.keyboard.press('Tab')
        await human_like_delay(0.5, 0.8)
        
        # Step 7: Blur to ensure all events are triggered
        await element.blur()
        await human_like_delay(0.3, 0.5)
        
        logger.info(f"✅ [ASPNET_FILL] Successfully filled {field_name} with ASP.NET event handling")
        return True
        
    except Exception as e:
        logger.error(f"❌ [ASPNET_FILL] Failed to fill element: {e}")
        return False


def randomize_typing_delay() -> int:
    """Generate human-like typing delay between 80-150ms."""
    import random
    return random.randint(80, 150)


async def safe_select_aspnet_field(
    page: Page,
    selectors: List[str],
    value: str,
    field_name: str,
    timeout: int = 10000,
    by_text: bool = False,
    verify_value: bool = True
) -> bool:
    """
    ASP.NET-aware select/dropdown field filling with comprehensive error handling.
    
    This function handles ASP.NET specific requirements for dropdown fields:
    - JavaScript event triggering (onchange)
    - ViewState preservation
    - Value verification
    - Anti-detection timing
    
    Args:
        page: Playwright page object
        selectors: List of CSS/XPath selectors to try
        value: Value to select (by value or by text)
        field_name: Human-readable field name for logging
        timeout: Element wait timeout in milliseconds
        by_text: If True, select by visible text; if False, select by value
        verify_value: Whether to verify the selected value
    
    Returns:
        bool: True if field was successfully filled, False otherwise
    """
    logger.info(f"🔧 [ASPNET_SELECT] Starting to select {field_name} with value: '{mask_sensitive(value)}'")
    
    for i, selector in enumerate(selectors):
        logger.info(f"🔍 [ASPNET_SELECT] Trying selector {i+1}/{len(selectors)}: {selector}")
        
        try:
            # Check if element exists
            element_count = await page.locator(selector).count()
            if element_count == 0:
                logger.warning(f"⚠️ [ASPNET_SELECT] Element not found: {selector}")
                continue
                
            # Wait for element to be ready
            element = await wait_for_element(page, selector, timeout)
            if not element:
                logger.warning(f"⚠️ [ASPNET_SELECT] Element not ready: {selector}")
                continue
            
            # Check if element is visible and enabled
            is_visible = await element.is_visible()
            is_enabled = await element.is_enabled()
            
            if not is_visible:
                logger.warning(f"⚠️ [ASPNET_SELECT] Element not visible: {selector}")
                continue
                
            if not is_enabled:
                logger.warning(f"⚠️ [ASPNET_SELECT] Element not enabled: {selector}")
                continue
            
            logger.info(f"✅ [ASPNET_SELECT] Element found and ready: {selector}")
            
            # ASP.NET-specific selection sequence
            success = await _select_aspnet_element(page, element, value, field_name, by_text)
            
            if success and verify_value:
                # Verify the value was actually selected
                try:
                    if by_text:
                        # For text selection, verify by getting selected option text
                        selected_text = await element.evaluate("el => el.options[el.selectedIndex]?.text")
                        if selected_text and selected_text.strip().upper() == value.strip().upper():
                            logger.info(f"✅ [ASPNET_SELECT] {field_name} selected and verified by text successfully")
                            return True
                        else:
                            logger.warning(f"⚠️ [ASPNET_SELECT] Text verification failed. Expected: '{value}', Got: '{selected_text}'")
                    else:
                        # For value selection, verify by getting selected value
                        selected_value = await element.input_value()
                        if selected_value and selected_value.strip() == value.strip():
                            logger.info(f"✅ [ASPNET_SELECT] {field_name} selected and verified by value successfully")
                            return True
                        else:
                            logger.warning(f"⚠️ [ASPNET_SELECT] Value verification failed. Expected: '{value}', Got: '{selected_value}'")
                except Exception as e:
                    logger.warning(f"⚠️ [ASPNET_SELECT] Could not verify selection: {e}")
            
            # If we got here and verification was successful or not required
            if success and not verify_value:
                logger.info(f"✅ [ASPNET_SELECT] {field_name} selected successfully (no verification)")
                return True
                
        except Exception as e:
            logger.warning(f"⚠️ [ASPNET_SELECT] Error with selector {selector}: {e}")
            continue
    
    logger.error(f"❌ [ASPNET_SELECT] Failed to select {field_name} with any selector")
    return False


async def _select_aspnet_element(page: Page, element, value: str, field_name: str, by_text: bool = False) -> bool:
    """
    Internal function to select dropdown value with ASP.NET event handling.
    
    Args:
        page: Playwright page object
        element: Playwright element object
        value: Value to select
        field_name: Human-readable field name for logging
        by_text: If True, select by visible text; if False, select by value
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Step 1: Focus on element
        logger.info(f"🎯 [ASPNET_SELECT] Focusing on {field_name}")
        await element.focus()
        await human_like_delay(0.2, 0.4)
        
        # Step 2: Click to open dropdown
        await element.click()
        await human_like_delay(0.3, 0.5)
        
        # Step 3: Select the value
        if by_text:
            logger.info(f"📝 [ASPNET_SELECT] Selecting by text: '{value}'")
            await element.select_option(label=value)
        else:
            logger.info(f"🔢 [ASPNET_SELECT] Selecting by value: '{value}'")
            await element.select_option(value=value)
        
        await human_like_delay(0.3, 0.5)
        
        # Step 4: Trigger ASP.NET onchange event
        await element.evaluate("element => element.onchange && element.onchange()")
        await human_like_delay(0.2, 0.4)
        
        # Step 5: Simulate Tab key to move focus (triggers validation)
        await page.keyboard.press('Tab')
        await human_like_delay(0.5, 0.8)
        
        # Step 6: Blur to ensure all events are triggered
        await element.blur()
        await human_like_delay(0.3, 0.5)
        
        logger.info(f"✅ [ASPNET_SELECT] Successfully selected {field_name} with ASP.NET event handling")
        return True
        
    except Exception as e:
        logger.error(f"❌ [ASPNET_SELECT] Failed to select element: {e}")
        return False


async def safe_fill_with_focus_blur(
    page: Page,
    selector: str,
    value: str,
    timeout: int = 10000,
    clear: bool = True
) -> bool:
    """Legacy function - use safe_fill_aspnet_field for ASP.NET forms."""
    try:
        element = await wait_for_element(page, selector, timeout)
        if not element:
            return False
            
        # Focus first
        await element.focus()
        await asyncio.sleep(0.3)
        
        # Clear if needed
        if clear:
            await element.clear()
            await asyncio.sleep(0.2)
        
        # Fill the value
        await element.fill(value)
        await asyncio.sleep(0.3)
        
        # Explicit blur to trigger validation
        await element.blur()
        await asyncio.sleep(0.5)
        
        # Re-focus and blur to ensure JS validation runs
        await element.focus()
        await asyncio.sleep(0.2)
        await element.blur()
        await asyncio.sleep(0.3)
        
        return True
        
    except Exception as e:
        logger.error(f"Focus/blur fill failed for {selector}: {e}")
        return False


async def attempt_continue_with_errors(
    page: Page,
    continue_selectors: List[str],
    max_attempts: int = 3
) -> Dict[str, Any]:
    """Attempt to continue despite validation errors.
    
    Returns:
        Dict with success status and details
    """
    result = {
        "success": False,
        "attempts": 0,
        "final_url": page.url,
        "errors_before": [],
        "errors_after": [],
        "method_used": None
    }
    
    # Get initial errors
    result["errors_before"] = await check_form_errors(page)
    logger.info(f"🚨 Attempting to continue despite {len(result['errors_before'])} validation errors")
    
    for attempt in range(max_attempts):
        result["attempts"] = attempt + 1
        logger.info(f"📄 Continue attempt {attempt + 1}/{max_attempts}")
        
        # Try each continue selector
        for selector in continue_selectors:
            try:
                if await element_exists(page, selector):
                    logger.info(f"  Trying continue button: {selector}")
                    
                    # Take screenshot before clicking
                    await take_screenshot(page, f"continue_attempt_{attempt + 1}.png")
                    
                    # Click continue button
                    if await safe_click(page, selector):
                        logger.info(f"  ✅ Clicked continue button: {selector}")
                        
                        # Wait for page change or new content
                        await asyncio.sleep(2)
                        await wait_for_page_load(page, timeout=10000)
                        
                        # Check if we're on a new page
                        new_url = page.url
                        if new_url != result["final_url"]:
                            logger.info(f"  🎉 Page changed! {result['final_url']} → {new_url}")
                            result["success"] = True
                            result["final_url"] = new_url
                            result["method_used"] = selector
                            result["errors_after"] = await check_form_errors(page)
                            return result
                        else:
                            logger.warning(f"  ⚠️ Page didn't change after clicking {selector}")
                    else:
                        logger.warning(f"  ❌ Failed to click continue button: {selector}")
                        
            except Exception as e:
                logger.error(f"  💥 Error with continue selector {selector}: {e}")
        
        # Wait before next attempt
        if attempt < max_attempts - 1:
            await asyncio.sleep(1)
    
    # Final error check
    result["errors_after"] = await check_form_errors(page)
    result["final_url"] = page.url
    
    logger.warning(f"❌ Failed to continue after {max_attempts} attempts")
    return result


async def scroll_to_element(page: Page, selector: str) -> bool:
    """Scroll element into view."""
    try:
        element = page.locator(selector)
        await element.scroll_into_view_if_needed()
        await asyncio.sleep(0.5)
        return True
    except Exception as e:
        logger.warning(f"Failed to scroll to {selector}: {e}")
        return False

def get_random_delay(min_seconds: float = 0.5, max_seconds: float = 2.0) -> float:
    """Get random delay to simulate human behavior."""
    return random.uniform(min_seconds, max_seconds)

async def human_like_delay(min_seconds: float = 0.1, max_seconds: float = 0.3) -> None:
    """Add human-like delay between actions."""
    delay = get_random_delay(min_seconds, max_seconds)
    await asyncio.sleep(delay)


async def dump_page_html_to_console(page: Page, title: str = "Page HTML") -> None:
    """Print page HTML to console for debugging."""
    try:
        html = await page.content()
        logger.info(f"\n{'='*80}")
        logger.info(f"🔍 {title}")
        logger.info(f"URL: {page.url}")
        logger.info(f"{'='*80}")
        logger.info(html)
        logger.info(f"{'='*80}\n")
    except Exception as e:
        logger.error(f"Failed to dump page HTML: {e}")


async def dump_form_elements_to_console(page: Page, title: str = "Form Elements Analysis") -> None:
    """Print all form elements to console for debugging."""
    try:
        logger.info(f"\n{'='*80}")
        logger.info(f"🔍 {title}")
        logger.info(f"URL: {page.url}")
        logger.info(f"{'='*80}")
        
        # Get all form-related elements
        elements = await page.query_selector_all("input, select, textarea, button")
        logger.info(f"Found {len(elements)} form elements:")
        
        for i, elem in enumerate(elements, 1):
            try:
                tag = await elem.evaluate("el => el.tagName.toLowerCase()")
                name = await elem.get_attribute("name") or ""
                id_attr = await elem.get_attribute("id") or ""
                type_attr = await elem.get_attribute("type") or ""
                placeholder = await elem.get_attribute("placeholder") or ""
                value = await elem.get_attribute("value") or ""
                class_attr = await elem.get_attribute("class") or ""
                is_visible = await elem.is_visible()
                is_enabled = await elem.is_enabled()
                
                # Format element info
                info = f"  {i:2}. <{tag}"
                if name:
                    info += f" name='{name}'"
                if id_attr:
                    info += f" id='{id_attr}'"
                if type_attr:
                    info += f" type='{type_attr}'"
                if placeholder:
                    info += f" placeholder='{placeholder[:30]}'"
                if value:
                    info += f" value='{value[:30]}'"
                if class_attr:
                    info += f" class='{class_attr[:30]}'"
                
                info += f"> visible={is_visible} enabled={is_enabled}"
                logger.info(info)
                
            except Exception as elem_error:
                logger.debug(f"  {i:2}. Error analyzing element: {elem_error}")
        
        logger.info(f"{'='*80}\n")
        
    except Exception as e:
        logger.error(f"Failed to dump form elements: {e}")


async def dump_form_html_to_console(page: Page, title: str = "Forms HTML") -> None:
    """Print all forms HTML to console for debugging."""
    try:
        logger.info(f"\n{'='*80}")
        logger.info(f"🔍 {title}")
        logger.info(f"URL: {page.url}")
        logger.info(f"{'='*80}")
        
        # Get all forms
        forms = await page.query_selector_all("form")
        logger.info(f"Found {len(forms)} form(s):")
        
        for i, form in enumerate(forms, 1):
            try:
                form_html = await form.inner_html()
                form_action = await form.get_attribute("action") or "no-action"
                form_method = await form.get_attribute("method") or "no-method"
                form_id = await form.get_attribute("id") or "no-id"
                form_name = await form.get_attribute("name") or "no-name"
                
                logger.info(f"\n--- Form {i} ---")
                logger.info(f"ID: {form_id}, Name: {form_name}")
                logger.info(f"Action: {form_action}, Method: {form_method}")
                logger.info("HTML:")
                logger.info(form_html)
                logger.info(f"--- End Form {i} ---\n")
                
            except Exception as form_error:
                logger.debug(f"Error analyzing form {i}: {form_error}")
        
        logger.info(f"{'='*80}\n")
        
    except Exception as e:
        logger.error(f"Failed to dump forms HTML: {e}")


async def suggest_selectors_for_field(page: Page, field_name: str) -> List[str]:
    """Suggest possible selectors for a given field name."""
    suggestions = []
    
    try:
        # Common patterns for different field types
        field_patterns = {
            "surname": ["surname", "last.?name", "family.?name"],
            "surnames": ["surname", "last.?name", "family.?name"],
            "given_name": ["given.?name", "first.?name", "fname"],
            "given_names": ["given.?name", "first.?name", "fname"],
            "email": ["email", "e.?mail"],
            "phone": ["phone", "tel", "mobile"],
            "address": ["address", "street", "addr"],
            "city": ["city", "town"],
            "state": ["state", "province", "region"],
            "country": ["country", "nation"],
            "zip": ["zip", "postal", "post.?code"],
            "date": ["date", "dob", "birth"],
        }
        
        patterns = field_patterns.get(field_name.lower(), [field_name.lower()])
        
        # Get all input/select elements
        elements = await page.query_selector_all("input, select, textarea")
        
        for element in elements:
            try:
                name = await element.get_attribute("name") or ""
                id_attr = await element.get_attribute("id") or ""
                placeholder = await element.get_attribute("placeholder") or ""
                
                # Check if any pattern matches
                for pattern in patterns:
                    if re.search(pattern, name, re.IGNORECASE):
                        suggestions.append(f"input[name='{name}']")
                    if re.search(pattern, id_attr, re.IGNORECASE):
                        suggestions.append(f"input[id='{id_attr}']")
                    if re.search(pattern, placeholder, re.IGNORECASE):
                        suggestions.append(f"input[placeholder*='{placeholder}']")
                        
            except Exception:
                continue
        
        # Remove duplicates while preserving order
        seen = set()
        unique_suggestions = []
        for suggestion in suggestions:
            if suggestion not in seen:
                seen.add(suggestion)
                unique_suggestions.append(suggestion)
        
        return unique_suggestions[:5]  # Return top 5 suggestions
        
    except Exception as e:
        logger.error(f"Failed to suggest selectors: {e}")
        return []


async def save_application_data(app_data: ApplicationData, file_path: str = "session_data/application_data.json") -> bool:
    """Save key application data for resume functionality."""
    try:
        # Create directory if it doesn't exist
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Update timestamp
        app_data.last_updated = datetime.now()
        
        # Save to file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(app_data.model_dump(), f, indent=2, default=str)
        
        logger.info(f"🔐 Application data saved to {file_path}")
        logger.debug(f"Saved data: ID={app_data.application_id}, Surname={app_data.surname}")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to save application data: {e}")
        return False


async def load_application_data(file_path: str = "session_data/application_data.json") -> Optional[ApplicationData]:
    """Load key application data for resume functionality."""
    try:
        if not Path(file_path).exists():
            logger.info("No saved application data found")
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convert datetime strings back to datetime objects
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'last_updated' in data and isinstance(data['last_updated'], str):
            data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        
        app_data = ApplicationData(**data)
        logger.info(f"🔐 Application data loaded from {file_path}")
        logger.info(f"Loaded data: ID={app_data.application_id}, Surname={app_data.surname}")
        
        return app_data
        
    except Exception as e:
        logger.error(f"Failed to load application data: {e}")
        return None


def create_application_data() -> ApplicationData:
    """Create new empty application data structure."""
    return ApplicationData()


async def save_form_after_fix(page: Page, form_name: str = "form") -> bool:
    """Click Save button after fixing validation errors to update form state.
    
    DS-160 forms often require clicking Save after fixing errors 
    before the validation state updates properly.
    
    Args:
        page: Playwright page object
        form_name: Name of the form for logging purposes
        
    Returns:
        bool: True if Save button was found and clicked, False otherwise
    """
    logger.info(f"💾 Clicking Save button to update {form_name} validation")
    
    save_selectors = [
        "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnSave']",
        "input[id='ctl00_SiteContentPlaceHolder_FormView1_btnSave']", 
        "input[value='Save']",
        "input[name*='btnSave']",
        "button[name*='btnSave']",
        "input[type='submit'][value*='Save']"
    ]
    
    for save_selector in save_selectors:
        if await element_exists(page, save_selector, timeout=2000):
            if await safe_click(page, save_selector):
                logger.info(f"✅ Clicked Save button: {save_selector}")
                
                # Wait for save to complete and page to update
                await human_like_delay(2.0, 3.0)
                await wait_for_page_load(page, timeout=10000)
                
                return True
            else:
                logger.warning(f"Found Save button but failed to click: {save_selector}")
    
    logger.warning(f"⚠️ Could not find Save button for {form_name}")
    return False


async def navigate_to_next_page(
    page: Page,
    current_section: str,
    timeout: int = 30000,
    verify_navigation: bool = True
) -> Dict[str, Any]:
    """Navigate to the next page in DS-160 form with robust ASP.NET WebForm handling.
    
    This function handles the complex ASP.NET postback navigation patterns used in DS-160 forms.
    It tries multiple navigation strategies and verifies successful page transitions.
    
    Args:
        page: Playwright page object
        current_section: Name of current section for logging
        timeout: Max time to wait for navigation
        verify_navigation: Whether to verify URL change occurred
        
    Returns:
        Dict with navigation results:
        {
            "success": bool,
            "method_used": str,
            "old_url": str,
            "new_url": str,
            "errors": List[str],
            "took_seconds": float
        }
    """
    start_time = datetime.now()
    initial_url = page.url
    result = {
        "success": False,
        "method_used": None,
        "old_url": initial_url,
        "new_url": initial_url,
        "errors": [],
        "took_seconds": 0.0
    }
    
    logger.info(f"🚀 Navigating from {current_section} page: {initial_url}")
    
    # Strategy 1: Family-specific navigation selectors
    family_nav_selectors = [
        # Divorced spouse specific button - highest priority
        "input[value='No – Continue Form']",
        "input[value*='Continue Form']",
        
        # Family form navigation (highest priority - exact match)
        "input[name='ctl00$ucNavigateOption$ucNavPanel$ctl01$btnNextPageComplete']",
        "input[id='ctl00_ucNavigateOption_ucNavPanel_ctl01_btnNextPageComplete']",
        
        # Family navigation with partial matches
        "input[name*='btnNextPageComplete']",
        "input[value='Next: Family']",
        "input[value*='Next']",
        
        # General navigation patterns
        "input[name*='ucNavPanel'][name*='ctl01'][value*='Next']",
        "input[name*='ucNavigateOption'][value*='Next']"
    ]
    
    # Strategy 2: Generic DS-160 navigation selectors
    generic_nav_selectors = [
        # Standard DS-160 continue buttons
        "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnContinue']",
        "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']",
        "input[value='Continue']",
        "input[value='Next']",
        
        # Form view navigation
        "input[name*='FormView1'][value*='Continue']",
        "input[name*='FormView1'][value*='Next']",
        
        # Submit buttons that act as navigation
        "input[type='submit'][value*='Continue']",
        "input[type='submit'][value*='Next']",
        "button[type='submit']"
    ]
    
    # Strategy 3: JavaScript postback patterns
    js_postback_patterns = [
        # Direct WebForm postback execution
        "__doPostBack('ctl00$ucNavigateOption$ucNavPanel$ctl01$btnNextPageComplete','')",
        "__doPostBack('ctl00$SiteContentPlaceHolder$FormView1$btnContinue','')",
        "__doPostBack('ctl00$SiteContentPlaceHolder$FormView1$btnNext','')",
    ]
    
    navigation_strategies = [
        {
            "name": "family_navigation",
            "selectors": family_nav_selectors,
            "description": "Family form specific navigation buttons"
        },
        {
            "name": "generic_navigation", 
            "selectors": generic_nav_selectors,
            "description": "Generic DS-160 navigation buttons"
        }
    ]
    
    # Try each navigation strategy
    for strategy in navigation_strategies:
        strategy_name = strategy["name"]
        selectors = strategy["selectors"]
        description = strategy["description"]
        
        logger.info(f"📋 Trying strategy: {strategy_name} ({description})")
        
        for i, selector in enumerate(selectors, 1):
            try:
                logger.debug(f"  {i}/{len(selectors)}: {selector}")
                
                # Check if element exists and is clickable
                if await element_exists(page, selector, timeout=3000):
                    element = page.locator(selector)
                    
                    # Ensure element is visible and enabled
                    is_visible = await element.is_visible()
                    is_enabled = await element.is_enabled()
                    
                    if not is_visible:
                        logger.debug(f"    Element not visible: {selector}")
                        continue
                    if not is_enabled:
                        logger.debug(f"    Element not enabled: {selector}")
                        continue
                    
                    logger.info(f"  ✅ Found clickable navigation button: {selector}")
                    
                    # Take screenshot before navigation
                    await take_screenshot(page, f"before_navigation_{current_section}.png")
                    
                    # Scroll element into view and click
                    await scroll_to_element(page, selector)
                    await human_like_delay(0.5, 1.0)
                    
                    # Click the navigation button
                    success = await safe_click(page, selector, timeout=10000)
                    if not success:
                        logger.warning(f"    Failed to click: {selector}")
                        continue
                    
                    logger.info(f"  🎯 Clicked navigation button: {selector}")
                    
                    # Wait for navigation to complete
                    navigation_success = await wait_for_navigation_complete(
                        page, initial_url, timeout=timeout
                    )
                    
                    if navigation_success["success"]:
                        elapsed = (datetime.now() - start_time).total_seconds()
                        result.update({
                            "success": True,
                            "method_used": f"{strategy_name}:{selector}",
                            "new_url": page.url,
                            "took_seconds": elapsed
                        })
                        
                        logger.info(f"🎉 Navigation successful! {initial_url} → {page.url}")
                        logger.info(f"⏱️ Navigation took {elapsed:.2f}s using {strategy_name}")
                        
                        # Take screenshot after successful navigation
                        await take_screenshot(page, f"after_navigation_{current_section}.png")
                        return result
                    else:
                        logger.warning(f"    Click succeeded but navigation failed: {navigation_success}")
                        result["errors"].extend(navigation_success.get("errors", []))
                
                else:
                    logger.debug(f"    Element not found: {selector}")
                    
            except Exception as e:
                error_msg = f"Error with selector {selector}: {e}"
                logger.error(f"    💥 {error_msg}")
                result["errors"].append(error_msg)
        
        # Small delay between strategies
        await human_like_delay(0.5, 1.0)
    
    # Strategy 4: Try JavaScript postback as last resort
    logger.info("📋 Trying JavaScript postback strategy (last resort)")
    for js_code in js_postback_patterns:
        try:
            logger.info(f"  Executing: {js_code}")
            
            await page.evaluate(js_code)
            await human_like_delay(1.0, 2.0)
            
            # Wait for navigation
            navigation_success = await wait_for_navigation_complete(
                page, initial_url, timeout=timeout//2
            )
            
            if navigation_success["success"]:
                elapsed = (datetime.now() - start_time).total_seconds()
                result.update({
                    "success": True,
                    "method_used": f"javascript:{js_code}",
                    "new_url": page.url,
                    "took_seconds": elapsed
                })
                
                logger.info(f"🎉 JavaScript navigation successful! {initial_url} → {page.url}")
                return result
                
        except Exception as e:
            error_msg = f"JavaScript postback failed: {e}"
            logger.error(f"  💥 {error_msg}")
            result["errors"].append(error_msg)
    
    # Navigation failed
    elapsed = (datetime.now() - start_time).total_seconds()
    result["took_seconds"] = elapsed
    result["new_url"] = page.url
    
    logger.error(f"❌ Navigation failed after {elapsed:.2f}s")
    logger.error(f"Tried {len(family_nav_selectors) + len(generic_nav_selectors)} selectors + {len(js_postback_patterns)} JS patterns")
    
    # Take screenshot of final state for debugging
    await take_screenshot(page, f"navigation_failed_{current_section}.png")
    
    # Dump form elements for debugging
    await dump_form_elements_to_console(page, f"Navigation Failed - {current_section}")
    
    return result


async def wait_for_navigation_complete(
    page: Page,
    initial_url: str,
    timeout: int = 30000
) -> Dict[str, Any]:
    """Wait for ASP.NET postback navigation to complete.
    
    Args:
        page: Playwright page object
        initial_url: URL before navigation attempt
        timeout: Max time to wait for navigation
        
    Returns:
        Dict with navigation completion results
    """
    result = {
        "success": False,
        "final_url": page.url,
        "url_changed": False,
        "page_loaded": False,
        "errors": []
    }
    
    start_time = datetime.now()
    check_interval = 0.5  # Check every 500ms
    max_attempts = int(timeout / (check_interval * 1000))
    
    logger.debug(f"⏳ Waiting for navigation from {initial_url} (timeout: {timeout}ms)")
    
    for attempt in range(max_attempts):
        try:
            current_url = page.url
            
            # Check if URL has changed
            if current_url != initial_url:
                logger.debug(f"✅ URL changed: {initial_url} → {current_url}")
                result["url_changed"] = True
                result["final_url"] = current_url
                
                # Wait for page to fully load
                try:
                    await page.wait_for_load_state("domcontentloaded", timeout=5000)
                    await page.wait_for_load_state("networkidle", timeout=10000)
                    result["page_loaded"] = True
                    logger.debug("✅ Page loading completed")
                    
                except Exception as load_error:
                    logger.debug(f"⚠️ Page load timeout (continuing anyway): {load_error}")
                
                result["success"] = True
                elapsed = (datetime.now() - start_time).total_seconds()
                logger.debug(f"🎉 Navigation completed in {elapsed:.2f}s")
                return result
            
            # Check for any form errors that might prevent navigation
            try:
                errors = await check_form_errors(page)
                if errors:
                    result["errors"] = errors
                    logger.debug(f"⚠️ Form errors detected during navigation wait: {errors}")
            except Exception:
                pass
            
            # Wait before next check
            await asyncio.sleep(check_interval)
            
        except Exception as e:
            error_msg = f"Error during navigation wait: {e}"
            logger.debug(error_msg)
            result["errors"].append(error_msg)
    
    # Timeout reached
    elapsed = (datetime.now() - start_time).total_seconds()
    result["final_url"] = page.url
    
    if page.url != initial_url:
        logger.warning(f"⚠️ URL changed but navigation timeout: {elapsed:.2f}s")
        result["url_changed"] = True
        result["success"] = True  # Consider it successful if URL changed
    else:
        logger.error(f"❌ Navigation timeout - URL unchanged after {elapsed:.2f}s")
    
    return result


def format_address_line1(street_line1: str) -> str:
    """Format address line 1 to match expected format: 'Number Street Name street'
    
    Examples:
        'Tverskaya Street 456, Apt 78' -> '456 Tverskaya street, Apt 78'
        'Nevsky Prospect 123, Apt 45' -> '123 Nevsky Prospect street, Apt 45' 
        'Abay Street 93' -> '93 Abay street'
        'Lenin Avenue 15A' -> '15A Lenin Avenue street'
    
    Args:
        street_line1: Original address line from data
        
    Returns:
        Formatted address line with number first and 'street' suffix
    """
    if not street_line1:
        return street_line1
        
    # Common patterns to match:
    # 1. "Street Name Number[, additional info]"
    # 2. "Avenue Name Number[, additional info]" 
    # 3. "Prospect Name Number[, additional info]"
    
    # Pattern to extract: [Street Type] [Name] [Number][, rest]
    pattern = r'^(.+?)\s+(Street|Avenue|Prospect|Boulevard|Road|Lane)\s+(\d+[A-Za-z]*)(,.*)?$'
    match = re.match(pattern, street_line1, re.IGNORECASE)
    
    if match:
        name = match.group(1).strip()  # Street name part
        street_type = match.group(2).lower()  # Street type (convert to lowercase)
        number = match.group(3)  # Number part
        rest = match.group(4) or ""  # Additional info like ", Apt 78"
        
        # Format as: "Number Name street[, rest]"  
        # Always use lowercase "street" regardless of original type
        formatted = f"{number} {name} street{rest}"
        logger.debug(f"Address formatted: '{street_line1}' -> '{formatted}'")
        return formatted
    
    # If no match, try alternative patterns
    # Pattern for "Name Number" without explicit street type
    alt_pattern = r'^(.+?)\s+(\d+[A-Za-z]*)(,.*)?$'
    alt_match = re.match(alt_pattern, street_line1)
    
    if alt_match:
        name = alt_match.group(1).strip()
        number = alt_match.group(2)
        rest = alt_match.group(3) or ""
        
        # Format as: "Number Name street[, rest]"
        formatted = f"{number} {name} street{rest}"
        logger.debug(f"Address formatted (alt): '{street_line1}' -> '{formatted}'")
        return formatted
    
    # If no pattern matches, return original
    logger.debug(f"Address unchanged (no pattern match): '{street_line1}'")
    return street_line1


def format_russian_state_province(state_province: str, country: str = "") -> str:
    """Format Russian state/province to use oblast/republic instead of districts.
    
    For Russia, DS-160 expects oblasts and republics, not districts or cities.
    
    Examples:
        'MOSCOW DISTRICT' -> 'MOSCOW OBLAST' 
        'MOSCOW CITY' -> 'MOSCOW OBLAST'
        'LENINGRAD DISTRICT' -> 'LENINGRAD OBLAST'
        'TATARSTAN DISTRICT' -> 'REPUBLIC OF TATARSTAN'
        'CHECHNYA DISTRICT' -> 'CHECHEN REPUBLIC'
    
    Args:
        state_province: Original state/province from data
        country: Country code or name (optional, to confirm this applies to Russia)
        
    Returns:
        Formatted state/province for Russian regions
    """
    if not state_province:
        return state_province
        
    # Only apply to Russia
    if country and "RUSSIA" not in country.upper():
        return state_province
        
    state_upper = state_province.upper()
    
    # Common republics that should use "REPUBLIC OF" format
    republic_mappings = {
        'TATARSTAN': 'REPUBLIC OF TATARSTAN',
        'BASHKORTOSTAN': 'REPUBLIC OF BASHKORTOSTAN', 
        'CHECHNYA': 'CHECHEN REPUBLIC',
        'CHUVASHIA': 'CHUVASH REPUBLIC',
        'DAGESTAN': 'REPUBLIC OF DAGESTAN',
        'INGUSHETIA': 'REPUBLIC OF INGUSHETIA',
        'KABARDINO-BALKARIA': 'KABARDINO-BALKAR REPUBLIC',
        'KALMYKIA': 'REPUBLIC OF KALMYKIA',
        'KARACHAY-CHERKESSIA': 'KARACHAY-CHERKESS REPUBLIC',
        'KARELIA': 'REPUBLIC OF KARELIA',
        'KOMI': 'KOMI REPUBLIC',
        'MARI EL': 'MARI EL REPUBLIC',
        'MORDOVIA': 'REPUBLIC OF MORDOVIA',
        'NORTH OSSETIA': 'REPUBLIC OF NORTH OSSETIA-ALANIA',
        'SAKHA': 'SAKHA REPUBLIC (YAKUTIA)',
        'TUVA': 'REPUBLIC of TUVA',
        'UDMURTIA': 'UDMURT REPUBLIC'
    }
    
    # Check for republic names first
    for republic_key, republic_full in republic_mappings.items():
        if republic_key in state_upper:
            logger.debug(f"Russian state formatted (republic): '{state_province}' -> '{republic_full}'")
            return republic_full
    
    # Special cases for federal cities
    federal_cities = {
        'MOSCOW': 'MOSCOW OBLAST',
        'ST. PETERSBURG': 'LENINGRAD OBLAST',
        'SAINT PETERSBURG': 'LENINGRAD OBLAST',
        'PETERSBURG': 'LENINGRAD OBLAST',
        'SEVASTOPOL': 'SEVASTOPOL (FEDERAL CITY)'
    }
    
    for city_key, oblast in federal_cities.items():
        if city_key in state_upper:
            logger.debug(f"Russian state formatted (federal city): '{state_province}' -> '{oblast}'")
            return oblast
    
    # Convert districts to oblasts
    if 'DISTRICT' in state_upper:
        # Remove "DISTRICT" and add "OBLAST"
        base_name = state_upper.replace(' DISTRICT', '').replace('DISTRICT', '').strip()
        formatted = f"{base_name} OBLAST"
        logger.debug(f"Russian state formatted (district->oblast): '{state_province}' -> '{formatted}'")
        return formatted
        
    # Convert cities to oblasts (common pattern)
    if 'CITY' in state_upper:
        base_name = state_upper.replace(' CITY', '').replace('CITY', '').strip()
        formatted = f"{base_name} OBLAST"
        logger.debug(f"Russian state formatted (city->oblast): '{state_province}' -> '{formatted}'")
        return formatted
    
    # If already has "OBLAST" or "REPUBLIC", keep as is
    if 'OBLAST' in state_upper or 'REPUBLIC' in state_upper:
        logger.debug(f"Russian state unchanged (already correct): '{state_province}'")
        return state_province
    
    # Default: add OBLAST to any other Russian region
    if state_province and not any(word in state_upper for word in ['OBLAST', 'REPUBLIC', 'KRAI', 'AUTONOMOUS']):
        formatted = f"{state_province} OBLAST"
        logger.debug(f"Russian state formatted (default->oblast): '{state_province}' -> '{formatted}'")
        return formatted
    
    logger.debug(f"Russian state unchanged (no pattern match): '{state_province}'")
    return state_province


# New utility functions for nationality/residence field management
async def count_existing_nationality_fields(page: Page) -> int:
    """Count how many nationality fields already exist on the page using multiple selector strategies."""
    count = 0
    try:
        for i in range(15):  # DS-160 supports up to 15
            ctl_index = f"ctl{i:02d}"
            
            # Try multiple selector patterns for nationality fields
            field_selectors = [
                f"select[id='ctl00_SiteContentPlaceHolder_FormView1_dtlOTHER_NATL_{ctl_index}_ddlOTHER_NATL']",  # Primary
                f"select[name*='dtlOTHER_NATL'][name*='ddlOTHER_NATL'][name*='{ctl_index}']",                    # Alternative by name
                f"select[id*='dtlOTHER_NATL_{ctl_index}'][id*='ddlOTHER_NATL']",                                # Partial ID match
                f"select[id*='ddlOTHER_NATL'][id*='{ctl_index}']",                                             # Shorter ID match
            ]
            
            field_found = False
            for selector in field_selectors:
                if await page.locator(selector).count() > 0:
                    field_found = True
                    break
                    
            if field_found:
                count += 1
            else:
                break  # Stop at first missing field
                
        logger.debug(f"Found {count} existing nationality fields using alternative selectors")
        return count
    except Exception as e:
        logger.error(f"Error counting nationality fields: {e}")
        return 0


async def count_existing_residence_fields(page: Page) -> int:
    """Count how many permanent residence fields already exist on the page."""
    count = 0
    try:
        for i in range(15):  # DS-160 supports up to 15
            ctl_index = f"ctl{i:02d}"
            field_selector = f"select[id='ctl00_SiteContentPlaceHolder_FormView1_dtlOthPermResCntry_{ctl_index}_ddlOthPermResCntry']"
            
            if await page.locator(field_selector).count() > 0:
                count += 1
            else:
                break  # Stop at first missing field
                
        logger.debug(f"Found {count} existing residence fields")
        return count
    except Exception as e:
        logger.error(f"Error counting residence fields: {e}")
        return 0


async def count_existing_companion_fields(page: Page) -> int:
    """Count how many travel companion fields already exist on the page."""
    count = 0
    try:
        for i in range(15):  # DS-160 supports up to 15 companions
            ctl_index = f"ctl{i:02d}"
            # Check for surname field as primary indicator of companion field existence
            field_selector = f"input[name='ctl00$SiteContentPlaceHolder$FormView1$dlTravelCompanions${ctl_index}$tbxSurname']"
            
            if await page.locator(field_selector).count() > 0:
                count += 1
            else:
                break  # Stop at first missing field
                
        logger.debug(f"Found {count} existing companion fields")
        return count
    except Exception as e:
        logger.error(f"Error counting companion fields: {e}")
        return 0


async def smart_country_select(page: Page, selector: str, country_name: str) -> bool:
    """Smart country selection with multiple fallback strategies."""
    if not country_name:
        return False
        
    strategies = [
        ("exact_match", lambda: safe_select_by_text(page, selector, country_name)),
        ("uppercase", lambda: safe_select_by_text(page, selector, country_name.upper())),
        ("lowercase", lambda: safe_select_by_text(page, selector, country_name.lower())),
        ("title_case", lambda: safe_select_by_text(page, selector, country_name.title())),
        ("normalized", lambda: safe_select_by_text(page, selector, normalize_country_name(country_name)))
    ]
    
    for strategy_name, strategy_func in strategies:
        try:
            logger.debug(f"Trying {strategy_name} for country '{country_name}'")
            if await strategy_func():
                logger.info(f"✅ Successfully selected country '{country_name}' using {strategy_name}")
                return True
        except Exception as e:
            logger.debug(f"Strategy {strategy_name} failed: {e}")
            continue
    
    logger.warning(f"❌ All strategies failed for country: {country_name}")
    return False


def normalize_country_name(country: str) -> str:
    """Normalize country name for better matching."""
    if not country:
        return ""
    
    # Common normalizations
    normalizations = {
        "UNITED STATES": "UNITED STATES OF AMERICA",
        "USA": "UNITED STATES OF AMERICA", 
        "US": "UNITED STATES OF AMERICA",
        "UK": "UNITED KINGDOM",
        "RUSSIA": "RUSSIAN FEDERATION",
        "IRAN": "ISLAMIC REPUBLIC OF IRAN",
        "VENEZUELA": "BOLIVARIAN REPUBLIC OF VENEZUELA",
        "BOLIVIA": "PLURINATIONAL STATE OF BOLIVIA",
        "CONGO": "DEMOCRATIC REPUBLIC OF THE CONGO"
    }
    
    country_upper = country.upper().strip()
    return normalizations.get(country_upper, country)


async def verify_field_selection(page: Page, selector: str, expected_value: str) -> bool:
    """Verify that a select field has the expected value selected."""
    try:
        element = await page.locator(selector).first
        if not element:
            return False
            
        # Get the selected value
        selected_value = await element.evaluate("el => el.value")
        selected_text = await element.evaluate("el => el.options[el.selectedIndex]?.text || ''")
        
        # Check both value and text
        if selected_text and (expected_value.upper() in selected_text.upper() or 
                            selected_text.upper() in expected_value.upper()):
            logger.debug(f"✅ Field verification passed: '{selected_text}' matches '{expected_value}'")
            return True
        else:
            logger.debug(f"❌ Field verification failed: '{selected_text}' != '{expected_value}'")
            return False
            
    except Exception as e:
        logger.error(f"Error verifying field {selector}: {e}")
        return False


def should_mailing_address_be_same(home_address, mailing_address) -> bool:
    """Determine if mailing address should be marked as same as home address.
    
    Args:
        home_address: Home address object with street_line1, city, state_province, etc. (can be None)
        mailing_address: Mailing address object with same fields
        
    Returns:
        True if addresses are identical and should be marked as same
    """
    if not mailing_address:
        logger.debug("No mailing address provided - defaulting to same as home")
        return True
    
    if not home_address:
        logger.debug("No home address provided - addresses are the same (using mailing as home)")
        return True
    
    # Extract and normalize fields for comparison
    def normalize_field(field):
        """Normalize field for comparison (handle None, empty, whitespace)."""
        if field is None:
            return ""
        return str(field).strip().upper()
    
    # Compare all address fields
    home_street = normalize_field(getattr(home_address, 'street_line1', ''))
    mail_street = normalize_field(getattr(mailing_address, 'street_line1', ''))
    
    home_city = normalize_field(getattr(home_address, 'city', ''))  
    mail_city = normalize_field(getattr(mailing_address, 'city', ''))
    
    home_state = normalize_field(getattr(home_address, 'state_province', ''))
    mail_state = normalize_field(getattr(mailing_address, 'state_province', ''))
    
    home_postal = normalize_field(getattr(home_address, 'postal_zone_zip_code', ''))
    mail_postal = normalize_field(getattr(mailing_address, 'postal_zone_zip_code', ''))
    
    home_country = normalize_field(getattr(home_address, 'country_region', ''))
    mail_country = normalize_field(getattr(mailing_address, 'country_region', ''))
    
    # Check if all fields match
    fields_match = (
        home_street == mail_street and
        home_city == mail_city and
        home_state == mail_state and
        home_postal == mail_postal and
        home_country == mail_country
    )
    
    # Detailed logging for debugging
    logger.debug(f"Address comparison details:")
    logger.debug(f"  Street: home='{home_street}' vs mail='{mail_street}' → {'✅' if home_street == mail_street else '❌'}")
    logger.debug(f"  City:   home='{home_city}' vs mail='{mail_city}' → {'✅' if home_city == mail_city else '❌'}")
    logger.debug(f"  State:  home='{home_state}' vs mail='{mail_state}' → {'✅' if home_state == mail_state else '❌'}")
    logger.debug(f"  Postal: home='{home_postal}' vs mail='{mail_postal}' → {'✅' if home_postal == mail_postal else '❌'}")
    logger.debug(f"  Country: home='{home_country}' vs mail='{mail_country}' → {'✅' if home_country == mail_country else '❌'}")
    logger.info(f"📮 Address comparison result: {'SAME' if fields_match else 'DIFFERENT'}")
    
    return fields_match


async def save_and_continue_with_retry(
    page: Page, 
    max_attempts: int = 3,
    section_name: str = "form section",
    continue_selectors: List[str] = None
) -> bool:
    """
    Reliable DS-160 form navigation with URL-based success detection.
    Uses URL change detection instead of fragile DOM element checking.
    
    Args:
        page: Playwright page object
        max_attempts: Maximum number of retry attempts (default: 3)
        section_name: Name of the section being processed (for logging)
        continue_selectors: List of CSS selectors for continue/next buttons
        
    Returns:
        True if successfully navigated to next page, False otherwise
    """
    if not continue_selectors:
        continue_selectors = [
            "input[value*='Continue']",
            "input[value*='Next']", 
            "input[value*='Save']",
            "input[name*='Next']",
            "input[name*='Continue']"
        ]
    
    original_url = page.url
    logger.info(f"🔄 Starting {section_name} submission from: {original_url}")
    
    for attempt in range(1, max_attempts + 1):
        try:
            logger.info(f"📤 Attempt {attempt}/{max_attempts} for {section_name}")
            
            # Step 1: Click the continue button
            button_clicked = False
            for selector in continue_selectors:
                try:
                    # Use .first to handle multiple matches and fix "strict mode violation"
                    button = page.locator(selector).first
                    if await button.is_visible(timeout=2000):
                        await button.click()
                        button_clicked = True
                        logger.info(f"✅ Clicked button: {selector}")
                        break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            if not button_clicked:
                logger.warning(f"⚠️ No continue button found on attempt {attempt}")
                await asyncio.sleep(2)
                continue
            
            # Step 2: Wait for URL change (reliable success indicator)
            try:
                await page.wait_for_function(
                    f"window.location.href !== '{original_url}'",
                    timeout=15000
                )
                new_url = page.url
                logger.info(f"✅ {section_name} SUCCESS: {original_url} → {new_url}")
                return True
                
            except Exception:  # TimeoutError or other navigation issues
                logger.warning(f"⚠️ No URL change detected on attempt {attempt}")
                
                # Step 3: Check for actual validation errors (red text, error messages)
                validation_errors = await check_for_validation_errors(page)
                if validation_errors:
                    logger.error(f"❌ Validation errors found:")
                    for error in validation_errors[:3]:  # Show first 3 errors
                        logger.error(f"   📋 {error}")
                    await take_screenshot(page, f"{section_name.lower().replace(' ', '_')}_validation_error_attempt_{attempt}.png")
                    # Don't retry validation errors - they need manual intervention
                    return False
                
                # No validation errors = probably network/timing issue, retry
                logger.info(f"🔄 Retrying {section_name} (attempt {attempt}/{max_attempts})")
                retry_delay = 3 * attempt  # Exponential backoff: 3, 6, 9 seconds
                await asyncio.sleep(retry_delay)
                
        except Exception as e:
            logger.error(f"❌ Error on attempt {attempt}: {e}")
            await take_screenshot(page, f"{section_name.lower().replace(' ', '_')}_error_attempt_{attempt}.png")
            await asyncio.sleep(2)
    
    logger.error(f"❌ {section_name} failed after {max_attempts} attempts")
    return False


async def check_for_validation_errors(page: Page) -> List[str]:
    """
    Detect actual DS-160 validation errors (vs. successful navigation).
    DS-160 shows red error text when validation fails.
    
    Returns:
        List of error messages found on the page
    """
    error_patterns = [
        ".errortext",           # Common DS-160 error class
        "[style*='color: red']", # Inline red text
        "[style*='color:red']",  # No space variant
        ".validation-summary",   # ASP.NET validation
        "[class*='error']",     # Generic error classes
        "span[style*='color:red']"  # Red span text
    ]
    
    errors = []
    for pattern in error_patterns:
        try:
            error_elements = await page.locator(pattern).all()
            for element in error_elements:
                if await element.is_visible():
                    text = (await element.text_content() or "").strip()
                    if text and len(text) > 0:
                        # Filter out common false positives
                        if not any(ignore in text.lower() for ignore in ["tooltip", "help", "example"]):
                            errors.append(text[:100])  # Truncate long errors
        except:
            continue
    
    return list(set(errors))  # Remove duplicates


async def get_form_validation_errors(page: Page) -> List[str]:
    """
    Extract validation error messages from DS-160 forms.
    
    Returns:
        List of error messages found on the page
    """
    errors = []
    
    try:
        # Common DS-160 error selectors
        error_selectors = [
            ".errorSummary",
            ".validation-summary-errors", 
            "[class*='error']",
            "[class*='Error']",
            "div[style*='color: red']",
            "span[style*='color: red']",
            ".field-validation-error"
        ]
        
        for selector in error_selectors:
            try:
                elements = await page.query_selector_all(selector)
                for element in elements:
                    if await element.is_visible():
                        text = await element.text_content()
                        if text and text.strip():
                            # Clean up error text
                            clean_text = text.strip().replace('\n', ' ').replace('\r', '')
                            if len(clean_text) > 10:  # Filter out very short errors
                                errors.append(clean_text)
            except Exception:
                continue
                
        # Look for specific DS-160 error patterns in page content
        page_content = await page.content()
        error_patterns = [
            r"has not been completed",
            r"Please correct all areas in error",
            r"required field",
            r"must be selected",
            r"invalid format"
        ]
        
        for pattern in error_patterns:
            matches = re.findall(pattern, page_content, re.IGNORECASE)
            for match in matches:
                if match not in [e for e in errors if match.lower() in e.lower()]:
                    errors.append(match)
        
    except Exception as e:
        logger.debug(f"Error checking validation errors: {e}")
    
    return list(set(errors))  # Remove duplicates 