"""
Adaptive form filler that uses page analysis to dynamically fill DS-160 forms.

This module provides intelligent form filling that adapts to the actual questions
present on each page, making the bot more robust to changes in the DS-160 form.
"""

import logging
from typing import Any, Dict, Optional
from playwright.async_api import Page

from .page_analyzer import DS160<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QuestionInfo
from .data_loader import DS160Data
from .utils import wait_for_element, safe_click, safe_fill, human_like_delay, take_screenshot

logger = logging.getLogger(__name__)


class AdaptiveFormFiller:
    """Adaptive form filler that analyzes pages and fills forms dynamically."""
    
    def __init__(self, page: Page):
        self.page = page
        self.analyzer = DS160PageAnalyzer(page)
    
    async def fill_previous_us_travel_adaptive(self, data: DS160Data) -> bool:
        """
        Adaptively fill Previous US Travel page based on detected questions.
        
        Args:
            data: DS160 form data
            
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info("🤖 Starting adaptive Previous US Travel form filling...")
        
        try:
            # Analyze the page to detect available questions
            await self.analyzer.debug_page_elements()  # Debug info
            detected_questions = await self.analyzer.analyze_page()
            
            if not detected_questions:
                logger.warning("⚠️ No questions detected on this page")
                await take_screenshot(self.page, "no_questions_detected.png")
                return False
            
            # Process each detected question
            success_count = 0
            total_questions = len(detected_questions)
            
            for question in detected_questions:
                logger.info(f"🎯 Processing question: {question.data_field}")
                
                try:
                    if await self._fill_question_adaptive(question, data):
                        success_count += 1
                        logger.info(f"✅ Successfully filled: {question.data_field}")
                    else:
                        logger.warning(f"⚠️ Failed to fill: {question.data_field}")
                except Exception as e:
                    logger.error(f"❌ Error filling {question.data_field}: {e}")
                    continue
            
            logger.info(f"📊 Filled {success_count}/{total_questions} questions successfully")
            
            # Check for form errors
            errors = await self._check_form_errors()
            if errors:
                logger.error(f"❌ Form validation errors: {errors}")
                await take_screenshot(self.page, "adaptive_form_errors.png")
                return False
            
            # Continue to next page
            if await self._click_continue_button():
                logger.info("✅ Successfully completed Previous US Travel (adaptive)")
                return True
            else:
                logger.error("❌ Could not find or click continue button")
                return False
                
        except Exception as e:
            logger.error(f"❌ Adaptive form filling failed: {e}")
            await take_screenshot(self.page, "adaptive_form_error.png")
            return False
    
    async def _fill_question_adaptive(self, question: QuestionInfo, data: DS160Data) -> bool:
        """Fill a single question based on its detected information."""
        
        # Get the data value for this question
        data_value = self._get_data_value(question.data_field, data)
        
        if data_value is None:
            logger.debug(f"No data available for {question.data_field}")
            return await self._fill_default_value(question)
        
        if question.question_type == "radio":
            return await self._fill_radio_question(question, data_value)
        elif question.question_type == "text":
            return await self._fill_text_question(question, data_value)
        elif question.question_type == "select":
            return await self._fill_select_question(question, data_value)
        else:
            logger.warning(f"Unknown question type: {question.question_type}")
            return False
    
    async def _fill_radio_question(self, question: QuestionInfo, data_value: Any) -> bool:
        """Fill a radio button question."""
        
        # Determine answer based on data
        answer = self._determine_radio_answer(data_value)
        
        if answer == "yes":
            selectors = question.selectors.get("yes", [])
        elif answer == "no":
            selectors = question.selectors.get("no", [])
        else:
            logger.warning(f"Invalid radio answer: {answer}")
            return False
        
        # Try each selector until one works
        for selector in selectors:
            try:
                if await wait_for_element(self.page, selector, timeout=3000):
                    await safe_click(self.page, selector)
                    logger.info(f"✅ Selected '{answer}' using selector: {selector}")
                    await human_like_delay()
                    
                    # Fill explanation if needed and available
                    if answer == "yes" and question.explanation_field:
                        await self._fill_explanation(question, data_value)
                    
                    return True
            except Exception as e:
                logger.debug(f"Selector {selector} failed: {e}")
                continue
        
        logger.warning(f"Could not click any selector for {question.data_field}")
        return False
    
    async def _fill_explanation(self, question: QuestionInfo, data_value: Any) -> bool:
        """Fill explanation text if the data has an explanation field."""
        
        # Check if data_value has explanation
        explanation = None
        if hasattr(data_value, 'explanation'):
            explanation = data_value.explanation
        elif hasattr(data_value, 'answer') and hasattr(data_value, 'explanation'):
            explanation = data_value.explanation
        
        if not explanation:
            logger.debug(f"No explanation provided for {question.data_field}")
            return True
        
        # Try text selectors
        text_selectors = question.selectors.get("text", [])
        for selector in text_selectors:
            try:
                if await wait_for_element(self.page, selector, timeout=3000):
                    await safe_fill(self.page, selector, explanation)
                    logger.info(f"✅ Filled explanation using selector: {selector}")
                    await human_like_delay()
                    return True
            except Exception as e:
                logger.debug(f"Text selector {selector} failed: {e}")
                continue
        
        logger.warning(f"Could not fill explanation for {question.data_field}")
        return False
    
    async def _fill_text_question(self, question: QuestionInfo, data_value: Any) -> bool:
        """Fill a text input question."""
        
        text_selectors = question.selectors.get("text", [])
        text_value = str(data_value) if data_value else ""
        
        for selector in text_selectors:
            try:
                if await wait_for_element(self.page, selector, timeout=3000):
                    await safe_fill(self.page, selector, text_value)
                    logger.info(f"✅ Filled text '{text_value}' using selector: {selector}")
                    await human_like_delay()
                    return True
            except Exception as e:
                logger.debug(f"Text selector {selector} failed: {e}")
                continue
        
        logger.warning(f"Could not fill text for {question.data_field}")
        return False
    
    async def _fill_select_question(self, question: QuestionInfo, data_value: Any) -> bool:
        """Fill a select dropdown question."""
        # Implementation would be similar to text/radio
        logger.debug(f"Select question filling not implemented yet for {question.data_field}")
        return True
    
    async def _fill_default_value(self, question: QuestionInfo) -> bool:
        """Fill default value when no data is available."""
        
        if question.question_type == "radio":
            # Default to "No" for most questions
            selectors = question.selectors.get("no", [])
            for selector in selectors:
                try:
                    if await wait_for_element(self.page, selector, timeout=3000):
                        await safe_click(self.page, selector)
                        logger.info(f"✅ Selected default 'No' for {question.data_field}")
                        await human_like_delay()
                        return True
                except Exception as e:
                    logger.debug(f"Default selector {selector} failed: {e}")
                    continue
        
        logger.warning(f"Could not fill default value for {question.data_field}")
        return False
    
    def _get_data_value(self, data_field: str, data: DS160Data) -> Any:
        """Get data value from DS160Data using dot notation path."""
        
        try:
            # Split the path and navigate through the data object
            parts = data_field.split('.')
            current = data
            
            for part in parts:
                if hasattr(current, part):
                    current = getattr(current, part)
                else:
                    logger.debug(f"Data field not found: {data_field} (missing: {part})")
                    return None
            
            return current
            
        except Exception as e:
            logger.debug(f"Error getting data value for {data_field}: {e}")
            return None
    
    def _determine_radio_answer(self, data_value: Any) -> str:
        """Determine radio button answer (yes/no) from data value."""
        
        if data_value is None:
            return "no"
        
        # Handle RefusalInfo objects
        if hasattr(data_value, 'answer'):
            answer = data_value.answer.lower() if data_value.answer else "no"
        else:
            answer = str(data_value).lower()
        
        # Determine yes/no
        if answer in ["yes", "y", "true", "1"]:
            return "yes"
        else:
            return "no"
    
    async def _check_form_errors(self) -> List[str]:
        """Check for form validation errors."""
        errors = []
        
        # Common error selectors in DS-160
        error_selectors = [
            ".validation-summary-errors",
            ".field-validation-error", 
            "*:has-text('error')",
            "*:has-text('required')",
            ".error",
            "[style*='color: red']"
        ]
        
        for selector in error_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                for element in elements:
                    text = await element.text_content()
                    if text and text.strip() and len(text.strip()) > 5:
                        errors.append(text.strip())
            except:
                continue
        
        return errors
    
    async def _click_continue_button(self) -> bool:
        """Click continue/next button to proceed."""
        
        continue_selectors = [
            "input[value*='Continue']",
            "input[value*='Next']", 
            "input[name*='btnNext']",
            "input[type='submit']",
            "button:has-text('Continue')",
            "button:has-text('Next')"
        ]
        
        for selector in continue_selectors:
            try:
                if await wait_for_element(self.page, selector, timeout=3000):
                    await safe_click(self.page, selector)
                    logger.info(f"✅ Clicked continue button: {selector}")
                    await human_like_delay(2.0, 3.0)  # Wait for page load
                    return True
            except Exception as e:
                logger.debug(f"Continue selector {selector} failed: {e}")
                continue
        
        return False