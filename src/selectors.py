"""CSS/XPath selectors for DS-160 form elements."""
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class SelectorConfig:
    """Configuration for element selectors."""
    css: Optional[str] = None
    xpath: Optional[str] = None
    text: Optional[str] = None
    index: Optional[int] = None
    wait_for: bool = True
    timeout: int = 3000

class DS160Selectors:
    """DS-160 form selectors organized by page/section."""
    
    def __init__(self, selectors_file: str = "config/selectors.yaml"):
        """Initialize selectors from configuration file."""
        self.selectors_file = Path(selectors_file)
        self._selectors: Dict[str, Any] = {}
        self.load_selectors()
    
    def load_selectors(self) -> None:
        """Load selectors from YAML configuration file."""
        if self.selectors_file.exists():
            with open(self.selectors_file, 'r') as f:
                self._selectors = yaml.safe_load(f) or {}
        else:
            # Create default selectors if file doesn't exist
            self._selectors = self._get_default_selectors()
            self.save_selectors()
    
    def save_selectors(self) -> None:
        """Save current selectors to YAML file."""
        self.selectors_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.selectors_file, 'w') as f:
            yaml.dump(self._selectors, f, default_flow_style=False, sort_keys=False)
    
    def get(self, selector_path: str, default: Optional[str] = None) -> Optional[str]:
        """Get selector by dot-notation path."""
        try:
            keys = selector_path.split('.')
            current = self._selectors
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    logger.warning(f"Selector path not found: {selector_path}")
                    return default
            
            # Handle both string selectors and lists of selectors
            if isinstance(current, list):
                return current  # Return the whole list
            elif isinstance(current, str):
                return current
            else:
                logger.warning(f"Invalid selector type at path {selector_path}: {type(current)}")
                return default
                
        except Exception as e:
            logger.error(f"Error getting selector {selector_path}: {e}")
            return default
    
    def get_config(self, selector_path: str) -> SelectorConfig:
        """Get selector configuration object."""
        selector_data = self._get_selector_data(selector_path)
        
        if isinstance(selector_data, str):
            return SelectorConfig(css=selector_data)
        elif isinstance(selector_data, dict):
            return SelectorConfig(**selector_data)
        else:
            return SelectorConfig(css=f"[data-testid='{selector_path}']")
    
    def _get_selector_data(self, selector_path: str) -> Any:
        """Get raw selector data."""
        keys = selector_path.split('.')
        current = self._selectors
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def _get_default_selectors(self) -> Dict[str, Any]:
        """Get default DS-160 selectors."""
        return {
            # Start page
            "start": {
                "start_application": [
                    "input[value='START AN APPLICATION']",
                    "button:has-text('START AN APPLICATION')",
                    "a:has-text('START AN APPLICATION')",
                    "input[value*='START']",
                    "button:has-text('Start')",
                    "a:has-text('Start')",
                    "[onclick*='start']",
                    ".start-button",
                    "#start-button"
                ],
                "website_terms_checkbox": [
                    "input[name='TermsConditions']",
                    "input[type='checkbox']",
                    "input[name*='terms']",
                    "input[name*='Terms']"
                ],
                "continue_button": [
                    "input[value='Continue']",
                    "button:has-text('Continue')",
                    "input[value*='Continue']",
                    "button:has-text('Next')",
                    "input[value='Next']"
                ]
            },
            
            # Personal Information Page 1
            "personal": {
                "surname": {
                    "css": "input[name='Surname']",
                    "wait_for": True
                },
                "given_names": "input[name='GivenName']",
                "full_name": "input[name='FullName']",
                "other_names_used": {
                    "has_other_names_no": "input[name='HasOtherNames'][value='false']",
                    "has_other_names_yes": "input[name='HasOtherNames'][value='true']",
                    "other_name_1": "input[name='OtherName1']",
                    "other_name_2": "input[name='OtherName2']"
                },
                "gender": {
                    "male": "input[name='Gender'][value='MALE']",
                    "female": "input[name='Gender'][value='FEMALE']"
                },
                "marital_status": {
                    "single": "input[name='MaritalStatus'][value='SINGLE']",
                    "married": "input[name='MaritalStatus'][value='MARRIED']",
                    "divorced": "input[name='MaritalStatus'][value='DIVORCED']",
                    "widowed": "input[name='MaritalStatus'][value='WIDOWED']",
                    "separated": "input[name='MaritalStatus'][value='SEPARATED']"
                },
                "date_of_birth": {
                    "day": "select[name='DOBDay']",
                    "month": "select[name='DOBMonth']",
                    "year": "select[name='DOBYear']"
                },
                "city_of_birth": "input[name='POBCity']",
                "country_of_birth": "select[name='POBCountry']",
                "nationality": "select[name='COC']",
                "national_id_number": "input[name='NationalID']",
                "continue_button": "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']"
            },
            
            # Contact Information
            "contact": {
                "home_address": {
                    "street_address": "input[name='StreetAddress1']",
                    "street_address_2": "input[name='StreetAddress2']",
                    "city": "input[name='City']",
                    "state_province": "input[name='StateProvince']",
                    "postal_code": "input[name='PostalCode']",
                    "country": "select[name='Country']"
                },
                "mailing_address": {
                    "same_as_home_yes": "input[name='MailingAddressSame'][value='true']",
                    "same_as_home_no": "input[name='MailingAddressSame'][value='false']",
                    "street_address": "input[name='MailingStreetAddress1']",
                    "city": "input[name='MailingCity']",
                    "state_province": "input[name='MailingStateProvince']",
                    "postal_code": "input[name='MailingPostalCode']",
                    "country": "select[name='MailingCountry']"
                },
                "phone_number": "input[name='PhoneNumber']",
                "email": "input[name='EmailAddress']",
                "confirm_email": "input[name='ConfirmEmail']",
                "continue_button": "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']"
            },
            
            # Passport Information
            "passport": {
                "passport_number": "input[name='PassportNumber']",
                "passport_book_number": "input[name='PassportBookNumber']",
                "country_of_issuance": "select[name='PPTIssuingCountry']",
                "issuance_date": {
                    "day": "select[name='PPTIssuedDay']",
                    "month": "select[name='PPTIssuedMonth']",
                    "year": "select[name='PPTIssuedYear']"
                },
                "expiration_date": {
                    "day": "select[name='PPTExpirationDay']",
                    "month": "select[name='PPTExpirationMonth']",
                    "year": "select[name='PPTExpirationYear']"
                },
                "lost_passport": {
                    "yes": "input[name='LostPassport'][value='true']",
                    "no": "input[name='LostPassport'][value='false']"
                },
                "continue_button": "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']"
            },
            
            # Travel Information
            "travel": {
                "purpose_of_trip": "select[name='PurposeOfTrip']",
                "other_purpose": "textarea[name='OtherPurpose']",
                "intended_arrival_date": {
                    "day": "select[name='IntendedArrivalDay']",
                    "month": "select[name='IntendedArrivalMonth']",
                    "year": "select[name='IntendedArrivalYear']"
                },
                "intended_length_of_stay": "input[name='IntendedLengthOfStay']",
                "address_in_us": {
                    "street_address": "input[name='USContactStreetAddress']",
                    "city": "input[name='USContactCity']",
                    "state": "select[name='USContactState']",
                    "zip_code": "input[name='USContactZip']"
                },
                "contact_person": "input[name='USContactPerson']",
                "contact_phone": "input[name='USContactPhone']",
                "email_address": "input[name='USContactEmail']",
                "who_is_paying": "select[name='PayingForTrip']",
                "continue_button": "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']"
            },
            
            # Family Information
            "family": {
                "father": {
                    "surname": "input[name='FatherSurname']",
                    "given_names": "input[name='FatherGivenName']",
                    "date_of_birth": {
                        "day": "select[name='FatherDOBDay']",
                        "month": "select[name='FatherDOBMonth']",
                        "year": "select[name='FatherDOBYear']"
                    }
                },
                "mother": {
                    "surname": "input[name='MotherSurname']",
                    "given_names": "input[name='MotherGivenName']",
                    "date_of_birth": {
                        "day": "select[name='MotherDOBDay']",
                        "month": "select[name='MotherDOBMonth']",
                        "year": "select[name='MotherDOBYear']"
                    }
                },
                "relatives_in_us": {
                    "has_relatives_no": "input[name='RelativesInUS'][value='false']",
                    "has_relatives_yes": "input[name='RelativesInUS'][value='true']"
                },
                "continue_button": "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']"
            },
            
            # Work/Education Information
            "work_education": {
                "occupation": "input[name='PresentOccupation']",
                "employer_name": "input[name='EmployerName']",
                "employer_address": {
                    "street_address": "input[name='EmployerStreetAddress']",
                    "city": "input[name='EmployerCity']",
                    "state_province": "input[name='EmployerStateProvince']",
                    "postal_code": "input[name='EmployerPostalCode']",
                    "country": "select[name='EmployerCountry']"
                },
                "employer_phone": "input[name='EmployerPhone']",
                "monthly_salary": "input[name='MonthlySalary']",
                "education": {
                    "institution_name": "input[name='EducationInstitutionName']",
                    "institution_address": {
                        "street_address": "input[name='EducationStreetAddress']",
                        "city": "input[name='EducationCity']",
                        "state_province": "input[name='EducationStateProvince']",
                        "postal_code": "input[name='EducationPostalCode']",
                        "country": "select[name='EducationCountry']"
                    },
                    "course_of_study": "input[name='CourseOfStudy']",
                    "attendance_from": {
                        "month": "select[name='EducationFromMonth']",
                        "year": "select[name='EducationFromYear']"
                    },
                    "attendance_to": {
                        "month": "select[name='EducationToMonth']",
                        "year": "select[name='EducationToYear']"
                    }
                },
                "continue_button": "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']"
            },
            
            # Security Questions
            "security": {
                "communicable_disease": {
                    "yes": "input[name='CommunicableDisease'][value='true']",
                    "no": "input[name='CommunicableDisease'][value='false']"
                },
                "mental_disorder": {
                    "yes": "input[name='MentalDisorder'][value='true']",
                    "no": "input[name='MentalDisorder'][value='false']"
                },
                "drug_addiction": {
                    "yes": "input[name='DrugAddiction'][value='true']",
                    "no": "input[name='DrugAddiction'][value='false']"
                },
                "arrested_convicted": {
                    "yes": "input[name='ArrestedConvicted'][value='true']",
                    "no": "input[name='ArrestedConvicted'][value='false']"
                },
                "controlled_substance": {
                    "yes": "input[name='ControlledSubstance'][value='true']",
                    "no": "input[name='ControlledSubstance'][value='false']"
                },
                "continue_button": "input[name='ctl00$SiteContentPlaceHolder$FormView1$btnNext']"
            },
            
            # Common elements
            "common": {
                "captcha": {
                    "image": "img[id*='captcha']",
                    "input": "input[name='captcha']",
                    "refresh": "a[id*='refresh']"
                },
                "error_message": ".error, .validation-error, [class*='error']",
                "success_message": ".success, [class*='success']",
                "loading_indicator": ".loading, [class*='loading'], [class*='spinner']",
                "next_button": "input[value='Next'], input[value='Continue'], button[type='submit']",
                "previous_button": "input[value='Previous'], input[value='Back']",
                "save_button": "input[value='Save'], button[id*='save']"
            },
            
            # Navigation
            "navigation": {
                "page_indicator": ".page-number, [class*='step']",
                "progress_bar": ".progress, [class*='progress']",
                "breadcrumb": ".breadcrumb, [class*='breadcrumb']"
            }
        }

# Create global selector instance
selectors = DS160Selectors() 