"""
Dynamic page analyzer for DS-160 forms.

This module provides intelligent analysis of DS-160 pages to detect available questions,
match them with data fields, and find the appropriate selectors dynamically.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from playwright.async_api import Page

logger = logging.getLogger(__name__)


@dataclass
class QuestionInfo:
    """Information about a detected question on the page."""
    question_text: str
    question_type: str  # 'radio', 'text', 'select', 'textarea', 'checkbox'
    data_field: str  # corresponding field in DS160Data
    selectors: Dict[str, List[str]]  # {'yes': [...], 'no': [...], 'text': [...]}
    is_required: bool = True
    explanation_field: Optional[str] = None


class DS160PageAnalyzer:
    """Analyzes DS-160 pages to detect questions and generate appropriate selectors."""
    
    # Question patterns for different DS-160 sections
    QUESTION_PATTERNS = {
        # Previous US Travel patterns
        "us_travel": {
            "pattern": r"Have you ever been (?:in|to) the (?:U\.S\.|United States)",
            "type": "radio",
            "data_field": "previous_us_travel.has_ever_been_in_us",
            "radio_base": "rblPREV_US_TRAVEL_IND"
        },
        "us_visa": {
            "pattern": r"Have you ever been issued a (?:U\.S\.|United States) (?:Visa|visa)",
            "type": "radio", 
            "data_field": "previous_us_travel.has_ever_been_issued_us_visa",
            "radio_base": "rblPREV_VISA_IND"
        },
        "visa_refused": {
            "pattern": r"Have you ever been refused.*(?:U\.S\.|United States).*(?:Visa|visa|admission)",
            "type": "radio",
            "data_field": "previous_us_travel.has_ever_been_refused_admission_or_visa",
            "radio_base": "rblPREV_VISA_REFUSED_IND",
            "explanation_base": "tbxPREV_VISA_REFUSED_EXPL"
        },
        "immigrant_petition": {
            "pattern": r"Has anyone ever filed.*immigrant petition.*on your behalf",
            "type": "radio",
            "data_field": "previous_us_travel.has_immigrant_petition_been_filed", 
            "radio_base": "rblIV_PETITION_IND",
            "explanation_base": "tbxIV_PETITION_EXPL"
        },
        "travel_authorization": {
            "pattern": r"Have you ever been denied.*travel authorization",
            "type": "radio",
            "data_field": "previous_us_travel.has_ever_been_denied_travel_authorization",
            "radio_base": "rblTRAVEL_AUTH.*DENIED",
            "explanation_base": "tbxTRAVEL_AUTH.*DENIED"
        },
        "us_license": {
            "pattern": r"Have you ever (?:held|been issued).*(?:U\.S\.|United States).*(?:driver|license)",
            "type": "radio",
            "data_field": "previous_us_travel.has_ever_held_us_license",
            "radio_base": "rblPREV_US_DRIVER_LIC_IND"
        },
        
        # Contact Information patterns  
        "additional_phone": {
            "pattern": r"Do you have any additional phone numbers",
            "type": "radio",
            "data_field": "contact_info.has_additional_phones",
            "radio_base": "rblAddPhone"
        },
        "additional_email": {
            "pattern": r"Do you have any additional email addresses",
            "type": "radio", 
            "data_field": "contact_info.has_additional_emails",
            "radio_base": "rblAddEmail"
        },
        "social_media": {
            "pattern": r"Do you have.*social media",
            "type": "radio",
            "data_field": "contact_info.has_social_media",
            "radio_base": "rblAddSocial"
        },
        
        # Security Questions patterns
        "communicable_disease": {
            "pattern": r"Do you have.*communicable disease.*public health significance",
            "type": "radio",
            "data_field": "security_and_background.part1_medical_and_health.has_communicable_disease",
            "radio_base": "rblDisease",
            "explanation_base": "tbxDisease"
        },
        "mental_disorder": {
            "pattern": r"Do you have.*mental.*physical disorder",
            "type": "radio",
            "data_field": "security_and_background.part1_medical_and_health.has_mental_or_physical_disorder", 
            "radio_base": "rblDisorder",
            "explanation_base": "tbxDisorder"
        }
    }
    
    def __init__(self, page: Page):
        self.page = page
        self.detected_questions: List[QuestionInfo] = []
    
    async def analyze_page(self) -> List[QuestionInfo]:
        """
        Analyze the current page to detect all available questions.
        
        Returns:
            List of QuestionInfo objects for detected questions
        """
        logger.info("🔍 Analyzing page for available questions...")
        
        self.detected_questions.clear()
        
        # Get all text content from the page
        page_text = await self.page.text_content("body") or ""
        
        # Get all radio button groups
        radio_groups = await self._find_radio_groups()
        
        # Get all text inputs and textareas
        text_inputs = await self._find_text_inputs()
        
        # Get all select dropdowns
        selects = await self._find_selects()
        
        logger.info(f"Found {len(radio_groups)} radio groups, {len(text_inputs)} text inputs, {len(selects)} selects")
        
        # Match questions with patterns
        for question_key, pattern_info in self.QUESTION_PATTERNS.items():
            question_match = await self._match_question_pattern(
                question_key, pattern_info, page_text, radio_groups, text_inputs
            )
            if question_match:
                self.detected_questions.append(question_match)
                logger.info(f"✅ Detected question: {question_key}")
            else:
                logger.debug(f"❌ Question not found: {question_key}")
        
        # Log summary
        logger.info(f"🎯 Successfully detected {len(self.detected_questions)} questions on this page")
        for q in self.detected_questions:
            logger.info(f"  - {q.data_field}: {q.question_text[:50]}...")
        
        return self.detected_questions
    
    async def _find_radio_groups(self) -> Dict[str, List[Dict]]:
        """Find all radio button groups on the page."""
        radio_groups = {}
        
        # Find all radio buttons
        radios = await self.page.query_selector_all("input[type='radio']")
        
        for radio in radios:
            try:
                name = await radio.get_attribute("name") or ""
                value = await radio.get_attribute("value") or ""
                id_attr = await radio.get_attribute("id") or ""
                
                if not name:
                    continue
                
                if name not in radio_groups:
                    radio_groups[name] = []
                
                radio_groups[name].append({
                    "element": radio,
                    "name": name,
                    "value": value,
                    "id": id_attr,
                    "selector": f"input[id='{id_attr}']" if id_attr else f"input[name='{name}'][value='{value}']"
                })
            except Exception as e:
                logger.debug(f"Error processing radio button: {e}")
                continue
        
        return radio_groups
    
    async def _find_text_inputs(self) -> List[Dict]:
        """Find all text inputs and textareas on the page."""
        text_inputs = []
        
        # Find text inputs
        inputs = await self.page.query_selector_all("input[type='text'], textarea")
        
        for input_elem in inputs:
            try:
                name = await input_elem.get_attribute("name") or ""
                id_attr = await input_elem.get_attribute("id") or ""
                tag = await input_elem.evaluate("el => el.tagName.toLowerCase()")
                
                text_inputs.append({
                    "element": input_elem,
                    "name": name,
                    "id": id_attr,
                    "type": tag,
                    "selector": f"input[id='{id_attr}']" if id_attr else f"{tag}[name='{name}']"
                })
            except Exception as e:
                logger.debug(f"Error processing text input: {e}")
                continue
        
        return text_inputs
    
    async def _find_selects(self) -> List[Dict]:
        """Find all select dropdowns on the page."""
        selects = []
        
        select_elements = await self.page.query_selector_all("select")
        
        for select in select_elements:
            try:
                name = await select.get_attribute("name") or ""
                id_attr = await select.get_attribute("id") or ""
                
                selects.append({
                    "element": select,
                    "name": name,
                    "id": id_attr,
                    "selector": f"select[id='{id_attr}']" if id_attr else f"select[name='{name}']"
                })
            except Exception as e:
                logger.debug(f"Error processing select: {e}")
                continue
        
        return selects
    
    async def _match_question_pattern(
        self, 
        question_key: str, 
        pattern_info: Dict, 
        page_text: str, 
        radio_groups: Dict, 
        text_inputs: List[Dict]
    ) -> Optional[QuestionInfo]:
        """Match a question pattern with elements on the page."""
        
        pattern = pattern_info["pattern"]
        
        # Check if question text exists on page
        if not re.search(pattern, page_text, re.IGNORECASE | re.DOTALL):
            return None
        
        # Extract question text
        match = re.search(pattern + r"[^?]*\?", page_text, re.IGNORECASE | re.DOTALL)
        question_text = match.group(0).strip() if match else pattern_info["pattern"]
        
        # Find matching radio buttons
        radio_base = pattern_info.get("radio_base", "")
        matching_radios = self._find_matching_radio_group(radio_groups, radio_base)
        
        if not matching_radios and pattern_info["type"] == "radio":
            logger.debug(f"No matching radio buttons found for {question_key} (base: {radio_base})")
            return None
        
        # Build selectors
        selectors = {"yes": [], "no": [], "text": []}
        
        if matching_radios:
            for radio in matching_radios:
                value = radio["value"].upper()
                if value in ["Y", "YES", "1"]:
                    selectors["yes"].append(radio["selector"])
                elif value in ["N", "NO", "0"]:
                    selectors["no"].append(radio["selector"])
        
        # Find explanation field if specified
        explanation_field = None
        if "explanation_base" in pattern_info:
            explanation_base = pattern_info["explanation_base"]
            for text_input in text_inputs:
                if re.search(explanation_base, text_input["name"], re.IGNORECASE):
                    selectors["text"].append(text_input["selector"])
                    explanation_field = text_input["name"]
                    break
        
        return QuestionInfo(
            question_text=question_text,
            question_type=pattern_info["type"],
            data_field=pattern_info["data_field"],
            selectors=selectors,
            explanation_field=explanation_field
        )
    
    def _find_matching_radio_group(self, radio_groups: Dict, radio_base: str) -> List[Dict]:
        """Find radio group that matches the base pattern."""
        for group_name, radios in radio_groups.items():
            if re.search(radio_base, group_name, re.IGNORECASE):
                return radios
        return []
    
    async def get_question_by_data_field(self, data_field: str) -> Optional[QuestionInfo]:
        """Get detected question by its data field path."""
        for question in self.detected_questions:
            if question.data_field == data_field:
                return question
        return None
    
    async def debug_page_elements(self):
        """Debug function to log all page elements for analysis."""
        logger.info("🐛 DEBUG: Page elements analysis")
        
        # All radio buttons
        radios = await self.page.query_selector_all("input[type='radio']")
        logger.info(f"Found {len(radios)} radio buttons:")
        for i, radio in enumerate(radios[:10]):  # Limit to first 10
            try:
                name = await radio.get_attribute("name") or "no-name"
                value = await radio.get_attribute("value") or "no-value"
                id_attr = await radio.get_attribute("id") or "no-id"
                logger.info(f"  Radio {i+1}: name='{name}', value='{value}', id='{id_attr}'")
            except:
                continue
        
        # All text inputs
        text_inputs = await self.page.query_selector_all("input[type='text'], textarea")
        logger.info(f"Found {len(text_inputs)} text inputs:")
        for i, input_elem in enumerate(text_inputs[:10]):  # Limit to first 10
            try:
                name = await input_elem.get_attribute("name") or "no-name"
                id_attr = await input_elem.get_attribute("id") or "no-id"
                tag = await input_elem.evaluate("el => el.tagName.toLowerCase()")
                logger.info(f"  {tag.upper()} {i+1}: name='{name}', id='{id_attr}'")
            except:
                continue
        
        # Page text preview
        page_text = await self.page.text_content("body") or ""
        question_lines = [line.strip() for line in page_text.split('\n') 
                         if line.strip() and '?' in line and len(line.strip()) > 20][:5]
        logger.info("Sample questions found on page:")
        for i, line in enumerate(question_lines):
            logger.info(f"  Q{i+1}: {line[:100]}...")