"""Family information handler for DS-160 forms."""

from enum import Enum
from typing import Optional, Dict, Callable
from datetime import date
from loguru import logger

from ..utils import (
    safe_fill, safe_select, safe_click, safe_select_by_text,
    take_screenshot, human_like_delay, element_exists,
    format_date_for_form, safe_fill_slow, verify_field_value,
    safe_fill_with_focus_blur, navigate_to_next_page, safe_fill_aspnet_field,
    safe_select_aspnet_field
)

class MaritalStatusCode(str, Enum):
    """Коды семейного положения из DS-160 HTML форм."""
    SINGLE = "S"
    MARRIED = "M"
    COMMON_LAW = "C" 
    CIVIL_UNION = "P"
    WIDOWED = "W"
    DIVORCED = "D"
    LEGALLY_SEPARATED = "L"
    OTHER = "O"

class FamilyPageType(str, Enum):
    """Типы дополнительных семейных страниц."""
    SPOUSE = "Spouse"           # Для M, C, P
    DIVORCED = "Divorced"       # Для D
    DECEASED = "Deceased"       # Для W
    SEPARATED = "Separated"     # Для L

class SpouseAddressType(str, Enum):
    """Типы адреса супруга."""
    HOME = "H"          # Same as Home Address
    MAILING = "M"       # Same as Mailing Address  
    US_CONTACT = "U"    # Same as U.S. Contact Address
    DO_NOT_KNOW = "D"   # Do Not Know
    OTHER = "O"         # Other (Specify Address)

class FamilyHandler:
    """Обработчик семейной информации для всех типов семейного положения."""
    
    def __init__(self, page, config=None):
        self.page = page
        self.config = config
        
        # Маппинг семейного статуса к типу страницы
        self.status_to_page_mapping = {
            MaritalStatusCode.MARRIED: FamilyPageType.SPOUSE,
            MaritalStatusCode.COMMON_LAW: FamilyPageType.SPOUSE,
            MaritalStatusCode.CIVIL_UNION: FamilyPageType.SPOUSE,
            MaritalStatusCode.DIVORCED: FamilyPageType.DIVORCED,
            MaritalStatusCode.WIDOWED: FamilyPageType.DECEASED,
            MaritalStatusCode.LEGALLY_SEPARATED: FamilyPageType.SEPARATED,
            # SINGLE - no additional page
            # OTHER - conditional: depends on explanation and spouse data
        }
        
        # Обработчики для каждого типа страницы
        self.page_handlers: Dict[FamilyPageType, Callable] = {
            FamilyPageType.SPOUSE: self.fill_spouse_info,
            FamilyPageType.DIVORCED: self.fill_divorced_spouse_info,
            FamilyPageType.DECEASED: self.fill_deceased_spouse_info,
            FamilyPageType.SEPARATED: self.fill_separated_spouse_info,
        }
        
        # Селекторы для страницы супруга
        self.spouse_selectors = {
            "surnames": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSpouseSurname']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSpouseSurname']",
                "input[name*='tbxSpouseSurname']"
            ],
            "given_names": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSpouseGivenName']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSpouseGivenName']",
                "input[name*='tbxSpouseGivenName']"
            ],
            "birth_day": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlDOBDay']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlDOBDay']",
                "select[name*='ddlDOBDay']"
            ],
            "birth_month": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlDOBMonth']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlDOBMonth']",
                "select[name*='ddlDOBMonth']"
            ],
            "birth_year": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxDOBYear']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxDOBYear']",
                "input[name*='tbxDOBYear']"
            ],
            "nationality": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlSpouseNatDropDownList']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlSpouseNatDropDownList']",
                "select[name*='ddlSpouseNatDropDownList']"
            ],
            "birth_city": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSpousePOBCity']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSpousePOBCity']",
                "input[name*='tbxSpousePOBCity']"
            ],
            "birth_city_na": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$cbexSPOUSE_POB_CITY_NA']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_cbexSPOUSE_POB_CITY_NA']",
                "input[name*='cbexSPOUSE_POB_CITY_NA']"
            ],
            "birth_country": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlSpousePOBCountry']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlSpousePOBCountry']",
                "select[name*='ddlSpousePOBCountry']"
            ],
            "address_type": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlSpouseAddressType']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlSpouseAddressType']",
                "select[name*='ddlSpouseAddressType']"
            ],
            # Поля для "Other" адреса
            "addr_line1": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSPOUSE_ADDR_LN1']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSPOUSE_ADDR_LN1']",
                "input[name*='tbxSPOUSE_ADDR_LN1']"
            ],
            "addr_line2": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSPOUSE_ADDR_LN2']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSPOUSE_ADDR_LN2']",
                "input[name*='tbxSPOUSE_ADDR_LN2']"
            ],
            "addr_city": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSPOUSE_ADDR_CITY']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSPOUSE_ADDR_CITY']",
                "input[name*='tbxSPOUSE_ADDR_CITY']"
            ],
            "addr_state": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSPOUSE_ADDR_STATE']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSPOUSE_ADDR_STATE']",
                "input[name*='tbxSPOUSE_ADDR_STATE']"
            ],
            "addr_state_na": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$cbexSPOUSE_ADDR_STATE_NA']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_cbexSPOUSE_ADDR_STATE_NA']",
                "input[name*='cbexSPOUSE_ADDR_STATE_NA']"
            ],
            "addr_postal": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSPOUSE_ADDR_POSTAL_CD']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSPOUSE_ADDR_POSTAL_CD']",
                "input[name*='tbxSPOUSE_ADDR_POSTAL_CD']"
            ],
            "addr_postal_na": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$cbexSPOUSE_ADDR_POSTAL_CD_NA']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_cbexSPOUSE_ADDR_POSTAL_CD_NA']",
                "input[name*='cbexSPOUSE_ADDR_POSTAL_CD_NA']"
            ],
            "addr_country": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlSPOUSE_ADDR_CNTRY']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlSPOUSE_ADDR_CNTRY']",
                "select[name*='ddlSPOUSE_ADDR_CNTRY']"
            ]
        }
        
        # Селекторы для страницы умершего супруга (widowed)
        self.deceased_selectors = {
            "surnames": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSURNAME']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSURNAME']",
                "input[name*='tbxSURNAME']"
            ],
            "given_names": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxGIVEN_NAME']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxGIVEN_NAME']",
                "input[name*='tbxGIVEN_NAME']"
            ],
            "birth_day": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlDOBDay']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlDOBDay']",
                "select[name*='ddlDOBDay']"
            ],
            "birth_month": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlDOBMonth']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlDOBMonth']",
                "select[name*='ddlDOBMonth']"
            ],
            "birth_year": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxDOBYear']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxDOBYear']",
                "input[name*='tbxDOBYear']"
            ],
            "nationality": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlSpouseNatDropDownList']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlSpouseNatDropDownList']",
                "select[name*='ddlSpouseNatDropDownList']"
            ],
            "birth_city": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxSpousePOBCity']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxSpousePOBCity']",
                "input[name*='tbxSpousePOBCity']"
            ],
            "birth_city_unknown": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$cbxSPOUSE_POB_CITY_NA']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_cbxSPOUSE_POB_CITY_NA']",
                "input[name*='cbxSPOUSE_POB_CITY_NA']"
            ],
            "birth_country": [
                "select[name='ctl00$SiteContentPlaceHolder$FormView1$ddlSpousePOBCountry']",
                "select[id='ctl00_SiteContentPlaceHolder_FormView1_ddlSpousePOBCountry']",
                "select[name*='ddlSpousePOBCountry']"
            ]
        }
        
        # Селекторы для страницы разведенных (divorced)
        self.divorced_selectors = {
            "number_of_spouses": [
                "input[name='ctl00$SiteContentPlaceHolder$FormView1$tbxNumberOfPrevSpouses']",
                "input[id='ctl00_SiteContentPlaceHolder_FormView1_tbxNumberOfPrevSpouses']",
                "input[name*='tbxNumberOfPrevSpouses']"
            ],
            # Шаблон для repeater с fallback селекторами - будет заменяться на ctl00, ctl01, etc.
            "spouse_template": {
                "surnames": [
                    "input[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$tbxSURNAME']",
                    "input[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_tbxSURNAME']",
                    "input[name*='DListSpouse${}$tbxSURNAME']",
                    "input[id*='DListSpouse_{}_tbxSURNAME']"
                ],
                "given_names": [
                    "input[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$tbxGIVEN_NAME']",
                    "input[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_tbxGIVEN_NAME']",
                    "input[name*='DListSpouse${}$tbxGIVEN_NAME']",
                    "input[id*='DListSpouse_{}_tbxGIVEN_NAME']"
                ],
                "birth_day": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlDOBDay']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlDOBDay']",
                    "select[name*='DListSpouse${}$ddlDOBDay']"
                ],
                "birth_month": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlDOBMonth']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlDOBMonth']",
                    "select[name*='DListSpouse${}$ddlDOBMonth']"
                ],
                "birth_year": [
                    "input[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$tbxDOBYear']",
                    "input[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_tbxDOBYear']",
                    "input[name*='DListSpouse${}$tbxDOBYear']"
                ],
                "nationality": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlSpouseNatDropDownList']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlSpouseNatDropDownList']",
                    "select[name*='DListSpouse${}$ddlSpouseNatDropDownList']"
                ],
                "marriage_day": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlDomDay']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlDomDay']",
                    "select[name*='DListSpouse${}$ddlDomDay']"
                ],
                "marriage_month": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlDomMonth']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlDomMonth']",
                    "select[name*='DListSpouse${}$ddlDomMonth']"
                ],
                "marriage_year": [
                    "input[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$txtDomYear']",
                    "input[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_txtDomYear']",
                    "input[name*='DListSpouse${}$txtDomYear']"
                ],
                "marriage_end_day": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlDomEndDay']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlDomEndDay']",
                    "select[name*='DListSpouse${}$ddlDomEndDay']"
                ],
                "marriage_end_month": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlDomEndMonth']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlDomEndMonth']",
                    "select[name*='DListSpouse${}$ddlDomEndMonth']"
                ],
                "marriage_end_year": [
                    "input[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$txtDomEndYear']",
                    "input[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_txtDomEndYear']",
                    "input[name*='DListSpouse${}$txtDomEndYear']"
                ],
                "how_ended": [
                    "textarea[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$tbxHowMarriageEnded']",
                    "textarea[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_tbxHowMarriageEnded']",
                    "textarea[name*='DListSpouse${}$tbxHowMarriageEnded']"
                ],
                "country_terminated": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlMarriageEnded_CNTRY']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlMarriageEnded_CNTRY']",
                    "select[name*='DListSpouse${}$ddlMarriageEnded_CNTRY']"
                ],
                "birth_city": [
                    "input[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$tbxSpousePOBCity']",
                    "input[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_tbxSpousePOBCity']",
                    "input[name*='DListSpouse${}$tbxSpousePOBCity']"
                ],
                "birth_country": [
                    "select[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${}$ddlSpousePOBCountry']",
                    "select[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_ddlSpousePOBCountry']",
                    "select[name*='DListSpouse${}$ddlSpousePOBCountry']"
                ],
                "add_another": [
                    "a[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_InsertButtonSpouse']",
                    "a[id*='DListSpouse_{}_InsertButtonSpouse']"
                ],
                "remove": [
                    "a[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{}_DeleteButtonSpouse']",
                    "a[id*='DListSpouse_{}_DeleteButtonSpouse']"
                ]
            }
        }
    
    def requires_additional_family_page(self, marital_status: str, personal_info=None) -> bool:
        """Определяет нужна ли дополнительная семейная страница."""
        if marital_status in self.status_to_page_mapping:
            return True
        
        # Special logic for "OTHER" status
        if marital_status == MaritalStatusCode.OTHER and personal_info:
            # If there's spouse data, show spouse page
            if hasattr(personal_info, 'spouse') and personal_info.spouse:
                return True
            # Could add more logic based on explanation text
        
        return False
    
    def get_family_page_type(self, marital_status: str, personal_info=None) -> Optional[FamilyPageType]:
        """Возвращает тип семейной страницы для данного статуса."""
        if marital_status in self.status_to_page_mapping:
            return self.status_to_page_mapping[marital_status]
        
        # Special logic for "OTHER" status
        if marital_status == MaritalStatusCode.OTHER and personal_info:
            # If there's spouse data, treat as spouse page
            if hasattr(personal_info, 'spouse') and personal_info.spouse:
                return FamilyPageType.SPOUSE
        
        return None
    
    async def handle_additional_family_page(self, data) -> bool:
        """Обработать дополнительную семейную страницу в зависимости от статуса."""
        print("!!! FAMILY_HANDLER: handle_additional_family_page CALLED !!!", flush=True)
        logger.info("="*60)
        logger.info("🚀 [FAMILY_HANDLER] Starting handle_additional_family_page")
        
        marital_status = data.personal_info.marital_status
        personal_info = data.personal_info
        
        # Детальное логирование типа и значения marital_status
        logger.info(f"📊 [FAMILY_HANDLER] marital_status type: {type(marital_status)}")
        logger.info(f"📊 [FAMILY_HANDLER] marital_status value: {marital_status}")
        if hasattr(marital_status, 'value'):
            logger.info(f"📊 [FAMILY_HANDLER] marital_status.value: {marital_status.value}")
        
        # Логирование доступных ключей в маппинге
        logger.info(f"📊 [FAMILY_HANDLER] Available mapping keys: {list(self.status_to_page_mapping.keys())}")
        for key in self.status_to_page_mapping.keys():
            logger.info(f"   Key: {key} (type: {type(key)}, value: {key.value if hasattr(key, 'value') else key})")
        
        # Преобразование marital_status к строке для сравнения
        if hasattr(marital_status, 'value'):
            marital_status_str = marital_status.value
        else:
            marital_status_str = str(marital_status)
        
        logger.info(f"🔍 [FAMILY_HANDLER] Looking for marital_status_str: '{marital_status_str}'")
        
        # Проверка требований страницы
        requires = self.requires_additional_family_page(marital_status, personal_info)
        logger.info(f"📊 [FAMILY_HANDLER] requires_additional_family_page: {requires}")
        
        if not requires:
            logger.info(f"⚠️ [FAMILY_HANDLER] No additional family page required for status: {marital_status}")
            return True
        
        # Определение типа страницы с правильным сравнением
        page_type = None
        for status_key, page_value in self.status_to_page_mapping.items():
            status_key_str = status_key.value if hasattr(status_key, 'value') else str(status_key)
            logger.debug(f"   Comparing: '{status_key_str}' == '{marital_status_str}'")
            if status_key_str == marital_status_str:
                page_type = page_value
                logger.info(f"✅ [FAMILY_HANDLER] Found matching page_type: {page_type}")
                break
        
        if not page_type:
            # Fallback для OTHER статуса
            if marital_status_str == "O" and personal_info:
                if hasattr(personal_info, 'spouse') and personal_info.spouse:
                    page_type = FamilyPageType.SPOUSE
                    logger.info(f"✅ [FAMILY_HANDLER] Using SPOUSE page for OTHER status with spouse data")
        
        logger.info(f"📊 [FAMILY_HANDLER] Final page_type: {page_type}")
        
        if not page_type:
            logger.warning(f"❌ [FAMILY_HANDLER] No page_type found for marital_status: {marital_status}")
            return True
        
        # Поиск обработчика
        handler = self.page_handlers.get(page_type)
        logger.info(f"📊 [FAMILY_HANDLER] Handler found: {handler.__name__ if handler else 'None'}")
        
        if not handler:
            logger.warning(f"❌ [FAMILY_HANDLER] No handler found for page type: {page_type}")
            return True  # Continue without error
        
        logger.info(f"🚀 [FAMILY_HANDLER] Calling handler: {handler.__name__}")
        result = await handler(data)
        logger.info(f"📊 [FAMILY_HANDLER] Handler result: {result}")
        logger.info("="*60)
        return result
    
    async def fill_spouse_info(self, data) -> bool:
        """Заполнить информацию о супруге (complete_family2.aspx?node=Spouse)."""
        logger.info("🏠 Starting spouse information filling process")
        
        try:
            # Debug page information first
            current_url = self.page.url
            page_title = await self.page.title()
            logger.info(f"🌐 Current URL: {current_url}")
            logger.info(f"📄 Page title: {page_title}")
            
            # Take screenshot immediately
            await take_screenshot(self.page, "spouse_page_entry.png")
            
            # Debug information
            logger.info(f"DEBUG: Marital status = {data.personal_info.marital_status}")
            logger.info(f"DEBUG: Has spouse attribute = {hasattr(data.personal_info, 'spouse')}")
            if hasattr(data.personal_info, 'spouse'):
                logger.info(f"DEBUG: Spouse is None = {data.personal_info.spouse is None}")
                if data.personal_info.spouse:
                    logger.info(f"DEBUG: Spouse name = {data.personal_info.spouse.given_names} {data.personal_info.spouse.surnames}")
            
            spouse = data.personal_info.spouse
            if not spouse:
                logger.warning("⚠️ Spouse page detected but no spouse information provided in data")
                logger.warning("💡 This usually means the data file has maritalStatus='MARRIED' but missing spouse section")
                logger.warning("🔧 Please add spouse information to your JSON data file or change maritalStatus")
                logger.error(f"DEBUG: personal_info attributes = {list(vars(data.personal_info).keys())}")
                
                # Take screenshot for debugging
                await take_screenshot(self.page, "spouse_page_no_data_error.png")
                
                # Return False to indicate error - this should stop the process
                return False
            
            # Take initial screenshot
            await take_screenshot(self.page, "spouse_info_start.png")
            
            # Debug page info
            page_title = await self.page.title()
            page_url = self.page.url
            logger.info(f"Spouse page - Title: {page_title}, URL: {page_url}")
            
            # Fill spouse surnames
            logger.info("Filling spouse surnames...")
            success = False
            for selector in self.spouse_selectors["surnames"]:
                if await safe_fill(self.page, selector, spouse.surnames):
                    logger.info(f"✅ Filled spouse surnames with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to fill spouse surnames")
                await take_screenshot(self.page, "spouse_surnames_error.png")
                return False
            await human_like_delay(0.5, 1.5)
            
            # Fill spouse given names
            logger.info("Filling spouse given names...")
            success = False
            for selector in self.spouse_selectors["given_names"]:
                if await safe_fill(self.page, selector, spouse.given_names):
                    logger.info(f"✅ Filled spouse given names with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to fill spouse given names")
                await take_screenshot(self.page, "spouse_given_names_error.png")
                return False
            await human_like_delay(0.5, 1.5)
            
            # Fill spouse date of birth
            if spouse.date_of_birth:
                birth_date = format_date_for_form(spouse.date_of_birth)
                
                # Birth day
                logger.info("Selecting spouse birth day...")
                success = False
                for selector in self.spouse_selectors["birth_day"]:
                    if await safe_select(self.page, selector, birth_date["day"]):
                        logger.info(f"✅ Selected spouse birth day with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to select spouse birth day")
                    await take_screenshot(self.page, "spouse_birth_day_error.png")
                    return False
                await human_like_delay(0.5, 1.5)
                
                # Birth month
                logger.info("Selecting spouse birth month...")
                success = False
                for selector in self.spouse_selectors["birth_month"]:
                    if await safe_select(self.page, selector, birth_date["month"]):
                        logger.info(f"✅ Selected spouse birth month with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to select spouse birth month")
                    await take_screenshot(self.page, "spouse_birth_month_error.png")
                    return False
                await human_like_delay(0.5, 1.5)
                
                # Birth year
                logger.info("Filling spouse birth year...")
                success = False
                for selector in self.spouse_selectors["birth_year"]:
                    if await safe_fill(self.page, selector, birth_date["year"]):
                        logger.info(f"✅ Filled spouse birth year with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to fill spouse birth year")
                    await take_screenshot(self.page, "spouse_birth_year_error.png")
                    return False
                await human_like_delay(0.5, 1.5)
            
            # Fill spouse nationality
            logger.info("Selecting spouse nationality...")
            success = False
            for selector in self.spouse_selectors["nationality"]:
                if await safe_select_by_text(self.page, selector, spouse.nationality):
                    logger.info(f"✅ Selected spouse nationality with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to select spouse nationality")
                await take_screenshot(self.page, "spouse_nationality_error.png")
                return False
            await human_like_delay(0.5, 1.5)
            
            # Fill place of birth
            if spouse.place_of_birth:
                # Birth city
                if spouse.place_of_birth.city:
                    logger.info("Filling spouse birth city...")
                    success = False
                    for selector in self.spouse_selectors["birth_city"]:
                        if await safe_fill(self.page, selector, spouse.place_of_birth.city):
                            logger.info(f"✅ Filled spouse birth city with: {selector}")
                            success = True
                            break
                    
                    if not success:
                        logger.warning("Failed to fill spouse birth city, checking 'Do Not Know'")
                        for selector in self.spouse_selectors["birth_city_na"]:
                            if await safe_click(self.page, selector):
                                logger.info(f"✅ Checked 'Do Not Know' for birth city with: {selector}")
                                break
                    await human_like_delay(0.5, 1.5)
                
                # Birth country
                logger.info("Selecting spouse birth country...")
                success = False
                for selector in self.spouse_selectors["birth_country"]:
                    if await safe_select_by_text(self.page, selector, spouse.place_of_birth.country_region):
                        logger.info(f"✅ Selected spouse birth country with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to select spouse birth country")
                    await take_screenshot(self.page, "spouse_birth_country_error.png")
                    return False
                await human_like_delay(0.5, 1.5)
            
            # Handle spouse address
            await self._handle_spouse_address(spouse)
            
            logger.info("✅ Successfully filled spouse information")
            await take_screenshot(self.page, "spouse_info_completed.png")
            
            # Navigate to next page
            logger.info("🚀 Navigating to next page after filling spouse information")
            navigation_result = await navigate_to_next_page(
                self.page, 
                current_section="spouse_information",
                timeout=30000
            )
            
            if navigation_result["success"]:
                logger.info(f"🎉 Successfully navigated from spouse page")
                logger.info(f"   Method: {navigation_result['method_used']}")
                logger.info(f"   Time: {navigation_result['took_seconds']:.2f}s")
                logger.info(f"   Old URL: {navigation_result['old_url']}")
                logger.info(f"   New URL: {navigation_result['new_url']}")
                return True
            else:
                logger.error(f"❌ Navigation failed after filling spouse information")
                logger.error(f"   Errors: {navigation_result['errors']}")
                logger.error(f"   Time spent: {navigation_result['took_seconds']:.2f}s")
                
                # Take debugging screenshot
                await take_screenshot(self.page, "spouse_navigation_failed.png")
                
                # Navigation failed - return False to stop the infinite loop
                logger.error("❌ Navigation failed after filling spouse information - returning False")
                logger.error("🛑 This will stop the bot to prevent infinite loops")
                return False
            
        except Exception as e:
            logger.error(f"Error filling spouse information: {str(e)}")
            await take_screenshot(self.page, "spouse_info_error.png")
            return False
    
    async def _handle_spouse_address(self, spouse) -> bool:
        """Обработать адрес супруга."""
        logger.info("Handling spouse address...")
        
        # Определить тип адреса
        address_type = getattr(spouse, 'address_type', SpouseAddressType.HOME)
        
        # Выбрать тип адреса
        logger.info(f"Selecting spouse address type: {address_type}")
        success = False
        for selector in self.spouse_selectors["address_type"]:
            if await safe_select(self.page, selector, address_type):
                logger.info(f"✅ Selected spouse address type with: {selector}")
                success = True
                break
        
        if not success:
            logger.error("Failed to select spouse address type")
            await take_screenshot(self.page, "spouse_address_type_error.png")
            return False
        await human_like_delay(1.0, 2.0)
        
        # Если выбран "Other", заполнить адрес
        if address_type == SpouseAddressType.OTHER and spouse.address:
            logger.info("Filling spouse 'Other' address...")
            return await self._fill_spouse_other_address(spouse.address)
        
        return True
    
    async def _fill_spouse_other_address(self, address) -> bool:
        """Заполнить 'Other' адрес супруга."""
        logger.info("Filling spouse other address fields...")
        
        # Address line 1
        if address.street_line1:
            success = False
            for selector in self.spouse_selectors["addr_line1"]:
                if await safe_fill(self.page, selector, address.street_line1):
                    logger.info(f"✅ Filled spouse address line 1 with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to fill spouse address line 1")
                return False
            await human_like_delay(0.5, 1.5)
        
        # Address line 2 (optional)
        if hasattr(address, 'street_line2') and address.street_line2:
            for selector in self.spouse_selectors["addr_line2"]:
                if await safe_fill(self.page, selector, address.street_line2):
                    logger.info(f"✅ Filled spouse address line 2 with: {selector}")
                    break
            await human_like_delay(0.5, 1.5)
        
        # City
        if address.city:
            success = False
            for selector in self.spouse_selectors["addr_city"]:
                if await safe_fill(self.page, selector, address.city):
                    logger.info(f"✅ Filled spouse address city with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to fill spouse address city")
                return False
            await human_like_delay(0.5, 1.5)
        
        # State/Province
        if hasattr(address, 'state_province') and address.state_province:
            success = False
            for selector in self.spouse_selectors["addr_state"]:
                if await safe_fill(self.page, selector, address.state_province):
                    logger.info(f"✅ Filled spouse address state with: {selector}")
                    success = True
                    break
            
            if not success:
                # Check "Does Not Apply" if state field is not applicable
                for selector in self.spouse_selectors["addr_state_na"]:
                    if await safe_click(self.page, selector):
                        logger.info(f"✅ Checked 'Does Not Apply' for state with: {selector}")
                        break
            await human_like_delay(0.5, 1.5)
        
        # Postal code
        if hasattr(address, 'postal_zone_zip_code') and address.postal_zone_zip_code:
            success = False
            for selector in self.spouse_selectors["addr_postal"]:
                if await safe_fill(self.page, selector, address.postal_zone_zip_code):
                    logger.info(f"✅ Filled spouse address postal code with: {selector}")
                    success = True
                    break
            
            if not success:
                # Check "Does Not Apply" if postal code is not applicable
                for selector in self.spouse_selectors["addr_postal_na"]:
                    if await safe_click(self.page, selector):
                        logger.info(f"✅ Checked 'Does Not Apply' for postal code with: {selector}")
                        break
            await human_like_delay(0.5, 1.5)
        
        # Country
        if address.country_region:
            success = False
            for selector in self.spouse_selectors["addr_country"]:
                if await safe_select_by_text(self.page, selector, address.country_region):
                    logger.info(f"✅ Selected spouse address country with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to select spouse address country")
                return False
            await human_like_delay(0.5, 1.5)
        
        return True
    
    async def fill_divorced_spouse_info(self, data) -> bool:
        """complete_family4.aspx?node=PrevSpouse - для DIVORCED."""
        print("!!! DIVORCED_SPOUSE: fill_divorced_spouse_info CALLED !!!", flush=True)
        logger.info("="*60)
        logger.info("🚀 [DIVORCED_SPOUSE] ENTERED fill_divorced_spouse_info method")
        logger.info("="*60)
        
        try:
            # Debug data access
            logger.info(f"🔍 [DIVORCED_SPOUSE] Checking for divorce_info in data.personal_info")
            logger.info(f"📊 [DIVORCED_SPOUSE] Has divorce_info attr: {hasattr(data.personal_info, 'divorce_info')}")
            
            divorce_info = data.personal_info.divorce_info
            logger.info(f"📊 [DIVORCED_SPOUSE] divorce_info is None: {divorce_info is None}")
            
            if not divorce_info:
                logger.error("Divorce information is required but not provided in data")
                # Additional debugging
                logger.error(f"🐛 DEBUG: personal_info attributes: {dir(data.personal_info)}")
                return False
                
            logger.info(f"✅ [DIVORCED_SPOUSE] Found divorce_info with {len(divorce_info.former_spouses)} former spouse(s)")
            logger.info(f"📊 [DIVORCED_SPOUSE] Number of former spouses: {divorce_info.number_of_former_spouses}")
            
            for i, spouse in enumerate(divorce_info.former_spouses):
                logger.info(f"📊 [DIVORCED_SPOUSE] Spouse {i+1}: surnames='{spouse.surnames}', given_names='{spouse.given_names}'")
            
            # Take initial screenshot
            await take_screenshot(self.page, "divorced_spouse_start.png")
            
            # Debug page info
            page_title = await self.page.title()
            page_url = self.page.url
            logger.info(f"Divorced spouse page - Title: {page_title}, URL: {page_url}")
            
            # Fill number of former spouses
            logger.info(f"Filling number of former spouses: {divorce_info.number_of_former_spouses}")
            success = False
            for selector in self.divorced_selectors["number_of_spouses"]:
                if await safe_fill(self.page, selector, str(divorce_info.number_of_former_spouses)):
                    logger.info(f"✅ Selected number of former spouses with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to fill number of former spouses")
                await take_screenshot(self.page, "divorced_number_error.png")
                return False
            await human_like_delay(1.0, 2.0)  # Wait for form to update
            
            # Fill information for each former spouse
            logger.info(f"🔄 [DIVORCED_SPOUSE] Starting to fill {len(divorce_info.former_spouses)} former spouse(s)")
            
            for i, spouse in enumerate(divorce_info.former_spouses):
                logger.info(f"📊 [DIVORCED_SPOUSE] Processing spouse {i+1}/{len(divorce_info.former_spouses)}: {spouse.surnames} {spouse.given_names}")
                
                # Generate repeater ID (ctl00, ctl01, ctl02, etc.)
                repeater_id = f"ctl{i:02d}"
                logger.info(f"📊 [DIVORCED_SPOUSE] Generated repeater_id: {repeater_id}")
                
                logger.info(f"🚀 [DIVORCED_SPOUSE] Calling _fill_single_former_spouse for spouse {i+1}")
                success = await self._fill_single_former_spouse(spouse, repeater_id, i+1)
                logger.info(f"📊 [DIVORCED_SPOUSE] _fill_single_former_spouse returned: {success}")
                if not success:
                    logger.error(f"Failed to fill former spouse {i+1}")
                    return False
                
                # If not the last spouse and we have more than one, add another entry
                if i < len(divorce_info.former_spouses) - 1:
                    logger.info(f"Adding another former spouse entry...")
                    add_another_clicked = False
                    for selector_template in self.divorced_selectors["spouse_template"]["add_another"]:
                        add_selector = selector_template.format(repeater_id)
                        if await safe_click(self.page, add_selector):
                            logger.info(f"✅ Added another former spouse entry with: {add_selector}")
                            add_another_clicked = True
                            await human_like_delay(2.0, 3.0)  # Wait for new entry to appear
                            break
                    if not add_another_clicked:
                        logger.warning(f"Failed to add another former spouse entry with all selectors")
            
            logger.info("✅ Successfully filled divorced spouse information")
            await take_screenshot(self.page, "divorced_spouse_completed.png")
            
            # Navigate to next page
            logger.info("🚀 Navigating to next page after filling divorced spouse information")
            navigation_result = await navigate_to_next_page(
                self.page, 
                current_section="divorced_spouse_information",
                timeout=30000
            )
            
            if navigation_result["success"]:
                logger.info(f"🎉 Successfully navigated from divorced spouse page")
                logger.info(f"   Method: {navigation_result['method_used']}")
                logger.info(f"   New URL: {navigation_result['new_url']}")
                return True
            else:
                logger.error(f"❌ Navigation failed after filling divorced spouse information")
                logger.error(f"   Errors: {navigation_result['errors']}")
                
                # Take debugging screenshot
                await take_screenshot(self.page, "divorced_spouse_navigation_failed.png")
                
                logger.error("❌ Navigation failed after filling divorced spouse information - returning False")
                logger.error("🛑 This will stop the bot to prevent infinite loops")
                return False
            
        except Exception as e:
            logger.error(f"Error filling divorced spouse information: {str(e)}")
            await take_screenshot(self.page, "divorced_spouse_error.png")
            return False
    
    async def _fill_single_former_spouse(self, spouse, repeater_id: str, spouse_number: int) -> bool:
        """Fill information for a single former spouse using bulletproof ASP.NET automation."""
        logger.info("="*60)
        logger.info(f"🚀 [DIVORCED_SPOUSE_{spouse_number}] Starting ASP.NET repeater filling")
        logger.info(f"📊 [DIVORCED_SPOUSE_{spouse_number}] Repeater ID: {repeater_id}")
        logger.info(f"📊 [DIVORCED_SPOUSE_{spouse_number}] Data: surnames='{spouse.surnames}', given_names='{spouse.given_names}'")
        logger.info("="*60)
        
        def convert_month_format(month_text: str, target_format: str) -> str:
            """Convert month between text and numeric formats for different dropdown types."""
            MONTH_MAPPING = {
                "JAN": "1", "FEB": "2", "MAR": "3", "APR": "4",
                "MAY": "5", "JUN": "6", "JUL": "7", "AUG": "8", 
                "SEP": "9", "OCT": "10", "NOV": "11", "DEC": "12"
            }
            if target_format == "numeric":
                return MONTH_MAPPING.get(month_text, month_text)
            return month_text  # Keep text format
        
        try:
            # Generate enhanced selectors for ASP.NET DataList repeater
            logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Generating ASP.NET repeater selectors...")
            
            # Create comprehensive selector sets
            surname_selectors = self._generate_repeater_selectors(repeater_id, "tbxSURNAME")
            given_name_selectors = self._generate_repeater_selectors(repeater_id, "tbxGIVEN_NAME")
            
            logger.info(f"📊 [DIVORCED_SPOUSE_{spouse_number}] Generated {len(surname_selectors)} surname selectors")
            logger.info(f"📊 [DIVORCED_SPOUSE_{spouse_number}] Generated {len(given_name_selectors)} given name selectors")
            
            # Fill surnames using bulletproof ASP.NET approach
            logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Filling surnames with ASP.NET-aware method")
            surname_filled = await safe_fill_aspnet_field(
                self.page,
                surname_selectors,
                spouse.surnames,
                f"Former Spouse {spouse_number} Surnames",
                timeout=15000,
                verify_value=True
            )
            
            if not surname_filled:
                logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to fill surnames")
                await self._debug_page_state(f"surnames_failed_spouse_{spouse_number}")
                return False
            
            await human_like_delay(0.8, 1.2)
            
            # Fill given names using bulletproof ASP.NET approach
            logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Filling given names with ASP.NET-aware method")
            given_names_filled = await safe_fill_aspnet_field(
                self.page,
                given_name_selectors,
                spouse.given_names,
                f"Former Spouse {spouse_number} Given Names",
                timeout=15000,
                verify_value=True
            )
            
            if not given_names_filled:
                logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to fill given names")
                await self._debug_page_state(f"given_names_failed_spouse_{spouse_number}")
                return False
            
            await human_like_delay(1.0, 1.5)
            
            # Fill date of birth
            try:
                birth_date = format_date_for_form(spouse.date_of_birth)
                logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Filling birth date: {birth_date}")
                
                # Birth day using ASP.NET-aware dropdown
                if birth_date["day"]:
                    birth_day_selectors = self._generate_repeater_selectors(repeater_id, "ddlDOBDay", "select")
                    birth_day_filled = await safe_select_aspnet_field(
                        self.page,
                        birth_day_selectors,
                        birth_date["day"],
                        f"Former Spouse {spouse_number} Birth Day",
                        timeout=15000,
                        by_text=False,
                        verify_value=True
                    )
                    if not birth_day_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select birth day")
                        return False
                await human_like_delay(0.3, 0.8)
                
                # Birth month using ASP.NET-aware dropdown
                if birth_date["month"]:
                    birth_month_selectors = self._generate_repeater_selectors(repeater_id, "ddlDOBMonth", "select")
                    birth_month_filled = await safe_select_aspnet_field(
                        self.page,
                        birth_month_selectors,
                        birth_date["month"],
                        f"Former Spouse {spouse_number} Birth Month",
                        timeout=15000,
                        by_text=False,
                        verify_value=True
                    )
                    if not birth_month_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select birth month")
                        return False
                await human_like_delay(0.3, 0.8)
                
                # Birth year using ASP.NET-aware text field
                if birth_date["year"]:
                    birth_year_selectors = self._generate_repeater_selectors(repeater_id, "tbxDOBYear")
                    birth_year_filled = await safe_fill_aspnet_field(
                        self.page,
                        birth_year_selectors,
                        birth_date["year"],
                        f"Former Spouse {spouse_number} Birth Year",
                        timeout=15000,
                        verify_value=True
                    )
                    if not birth_year_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to fill birth year")
                        return False
                await human_like_delay(0.5, 1.0)
            except Exception as e:
                logger.error(f"Error processing birth date for former spouse {spouse_number}: {e}")
                return False
            
            # Fill nationality using ASP.NET-aware dropdown
            nationality_selectors = self._generate_repeater_selectors(repeater_id, "ddlSpouseNatDropDownList", "select")
            nationality_filled = await safe_select_aspnet_field(
                self.page,
                nationality_selectors,
                spouse.nationality,
                f"Former Spouse {spouse_number} Nationality",
                timeout=15000,
                by_text=True,
                verify_value=True
            )
            if not nationality_filled:
                logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select nationality")
                return False
            await human_like_delay(0.5, 1.0)
            
            # Fill birth place city using ASP.NET-aware text field
            if spouse.place_of_birth and spouse.place_of_birth.city:
                birth_city_selectors = self._generate_repeater_selectors(repeater_id, "tbxSpousePOBCity", "input")
                birth_city_filled = await safe_fill_aspnet_field(
                    self.page,
                    birth_city_selectors,
                    spouse.place_of_birth.city,
                    f"Former Spouse {spouse_number} Birth City",
                    timeout=15000,
                    verify_value=True
                )
                if not birth_city_filled:
                    logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to fill birth city")
                    return False
                await human_like_delay(0.5, 1.0)

            # Fill birth place country using ASP.NET-aware dropdown
            if spouse.place_of_birth and spouse.place_of_birth.country_region:
                birth_country_selectors = self._generate_repeater_selectors(repeater_id, "ddlSpousePOBCountry", "select")
                birth_country_filled = await safe_select_aspnet_field(
                    self.page,
                    birth_country_selectors,
                    spouse.place_of_birth.country_region,
                    f"Former Spouse {spouse_number} Birth Country",
                    timeout=15000,
                    by_text=True,
                    verify_value=True
                )
                if not birth_country_filled:
                    logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select birth country")
                    return False
                await human_like_delay(0.5, 1.0)
            
            # Fill date of marriage
            try:
                marriage_date = format_date_for_form(spouse.date_of_marriage)
                logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Filling marriage date: {marriage_date}")
                
                # Marriage day using ASP.NET-aware dropdown
                if marriage_date["day"]:
                    marriage_day_selectors = self._generate_repeater_selectors(repeater_id, "ddlDomDay", "select")
                    marriage_day_filled = await safe_select_aspnet_field(
                        self.page,
                        marriage_day_selectors,
                        marriage_date["day"],
                        f"Former Spouse {spouse_number} Marriage Day",
                        timeout=15000,
                        by_text=False,
                        verify_value=True
                    )
                    if not marriage_day_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select marriage day")
                        return False
                await human_like_delay(0.3, 0.8)
                
                # Marriage month using ASP.NET-aware dropdown (numeric format)
                if marriage_date["month"]:
                    marriage_month_selectors = self._generate_repeater_selectors(repeater_id, "ddlDomMonth", "select")
                    marriage_month_numeric = convert_month_format(marriage_date["month"], "numeric")
                    logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Converting marriage month: '{marriage_date['month']}' -> '{marriage_month_numeric}'")
                    marriage_month_filled = await safe_select_aspnet_field(
                        self.page,
                        marriage_month_selectors,
                        marriage_month_numeric,
                        f"Former Spouse {spouse_number} Marriage Month",
                        timeout=15000,
                        by_text=False,
                        verify_value=True
                    )
                    if not marriage_month_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select marriage month")
                        return False
                await human_like_delay(0.3, 0.8)
                
                # Marriage year using ASP.NET-aware text field
                if marriage_date["year"]:
                    marriage_year_selectors = self._generate_repeater_selectors(repeater_id, "txtDomYear")
                    marriage_year_filled = await safe_fill_aspnet_field(
                        self.page,
                        marriage_year_selectors,
                        marriage_date["year"],
                        f"Former Spouse {spouse_number} Marriage Year",
                        timeout=15000,
                        verify_value=True
                    )
                    if not marriage_year_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to fill marriage year")
                        return False
                await human_like_delay(0.5, 1.0)
            except Exception as e:
                logger.error(f"Error processing marriage date for former spouse {spouse_number}: {e}")
                return False
            
            # Fill date marriage ended
            try:
                marriage_end_date = format_date_for_form(spouse.date_marriage_ended)
                logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Filling marriage end date: {marriage_end_date}")
                
                # Marriage end day using ASP.NET-aware dropdown
                if marriage_end_date["day"]:
                    marriage_end_day_selectors = self._generate_repeater_selectors(repeater_id, "ddlDomEndDay", "select")
                    marriage_end_day_filled = await safe_select_aspnet_field(
                        self.page,
                        marriage_end_day_selectors,
                        marriage_end_date["day"],
                        f"Former Spouse {spouse_number} Marriage End Day",
                        timeout=15000,
                        by_text=False,
                        verify_value=True
                    )
                    if not marriage_end_day_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select marriage end day")
                        return False
                await human_like_delay(0.3, 0.8)
                
                # Marriage end month using ASP.NET-aware dropdown (numeric format)
                if marriage_end_date["month"]:
                    marriage_end_month_selectors = self._generate_repeater_selectors(repeater_id, "ddlDomEndMonth", "select")
                    marriage_end_month_numeric = convert_month_format(marriage_end_date["month"], "numeric")
                    logger.info(f"🔧 [DIVORCED_SPOUSE_{spouse_number}] Converting marriage end month: '{marriage_end_date['month']}' -> '{marriage_end_month_numeric}'")
                    marriage_end_month_filled = await safe_select_aspnet_field(
                        self.page,
                        marriage_end_month_selectors,
                        marriage_end_month_numeric,
                        f"Former Spouse {spouse_number} Marriage End Month",
                        timeout=15000,
                        by_text=False,
                        verify_value=True
                    )
                    if not marriage_end_month_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select marriage end month")
                        return False
                await human_like_delay(0.3, 0.8)
                
                # Marriage end year using ASP.NET-aware text field
                if marriage_end_date["year"]:
                    marriage_end_year_selectors = self._generate_repeater_selectors(repeater_id, "txtDomEndYear")
                    marriage_end_year_filled = await safe_fill_aspnet_field(
                        self.page,
                        marriage_end_year_selectors,
                        marriage_end_date["year"],
                        f"Former Spouse {spouse_number} Marriage End Year",
                        timeout=15000,
                        verify_value=True
                    )
                    if not marriage_end_year_filled:
                        logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to fill marriage end year")
                        return False
                await human_like_delay(0.5, 1.0)
            except Exception as e:
                logger.error(f"Error processing marriage end date for former spouse {spouse_number}: {e}")
                return False
            
            # Fill how marriage ended using ASP.NET-aware textarea field
            how_ended_selectors = self._generate_repeater_selectors(repeater_id, "tbxHowMarriageEnded", "textarea")
            how_ended_filled = await safe_fill_aspnet_field(
                self.page,
                how_ended_selectors,
                spouse.how_marriage_ended,
                f"Former Spouse {spouse_number} How Marriage Ended",
                timeout=15000,
                verify_value=True
            )
            if not how_ended_filled:
                logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to fill how marriage ended")
                return False
            await human_like_delay(0.5, 1.0)
            
            # Fill country where marriage was terminated using ASP.NET-aware dropdown
            country_terminated_selectors = self._generate_repeater_selectors(repeater_id, "ddlMarriageEnded_CNTRY", "select")
            country_terminated_filled = await safe_select_aspnet_field(
                self.page,
                country_terminated_selectors,
                spouse.marriage_termination_country,
                f"Former Spouse {spouse_number} Marriage Termination Country",
                timeout=15000,
                by_text=True,
                verify_value=True
            )
            if not country_terminated_filled:
                logger.error(f"❌ [DIVORCED_SPOUSE_{spouse_number}] Failed to select marriage termination country")
                return False
            await human_like_delay(0.5, 1.0)
            
            logger.info(f"✅ Successfully filled former spouse {spouse_number}")
            return True
            
        except Exception as e:
            logger.error(f"Error filling former spouse {spouse_number}: {str(e)}")
            return False
    
    async def fill_deceased_spouse_info(self, data) -> bool:
        """complete_family5.aspx?node=DeceasedSpouse - для WIDOWED."""
        logger.info("Filling deceased spouse information")
        
        try:
            deceased = data.personal_info.deceased_spouse
            if not deceased:
                logger.error("Deceased spouse information is required but not provided in data")
                return False
            
            # Take initial screenshot
            await take_screenshot(self.page, "deceased_spouse_start.png")
            
            # Debug page info
            page_title = await self.page.title()
            page_url = self.page.url
            logger.info(f"Deceased spouse page - Title: {page_title}, URL: {page_url}")
            
            # Fill deceased spouse surnames
            logger.info("Filling deceased spouse surnames...")
            success = False
            for selector in self.deceased_selectors["surnames"]:
                if await safe_fill(self.page, selector, deceased.surnames):
                    logger.info(f"✅ Filled deceased spouse surnames with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to fill deceased spouse surnames")
                await take_screenshot(self.page, "deceased_surnames_error.png")
                return False
            await human_like_delay(0.5, 1.5)
            
            # Fill deceased spouse given names
            logger.info("Filling deceased spouse given names...")
            success = False
            for selector in self.deceased_selectors["given_names"]:
                if await safe_fill(self.page, selector, deceased.given_names):
                    logger.info(f"✅ Filled deceased spouse given names with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to fill deceased spouse given names")
                await take_screenshot(self.page, "deceased_given_names_error.png")
                return False
            await human_like_delay(0.5, 1.5)
            
            # Fill deceased spouse date of birth
            if deceased.date_of_birth:
                birth_date = format_date_for_form(deceased.date_of_birth)
                
                # Birth day
                logger.info("Selecting deceased spouse birth day...")
                success = False
                for selector in self.deceased_selectors["birth_day"]:
                    if await safe_select(self.page, selector, birth_date["day"]):
                        logger.info(f"✅ Selected deceased spouse birth day with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to select deceased spouse birth day")
                    await take_screenshot(self.page, "deceased_birth_day_error.png")
                    return False
                await human_like_delay(0.5, 1.5)
                
                # Birth month
                logger.info("Selecting deceased spouse birth month...")
                success = False
                for selector in self.deceased_selectors["birth_month"]:
                    if await safe_select(self.page, selector, birth_date["month"]):
                        logger.info(f"✅ Selected deceased spouse birth month with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to select deceased spouse birth month")
                    await take_screenshot(self.page, "deceased_birth_month_error.png")
                    return False
                await human_like_delay(0.5, 1.5)
                
                # Birth year
                logger.info("Filling deceased spouse birth year...")
                success = False
                for selector in self.deceased_selectors["birth_year"]:
                    if await safe_fill(self.page, selector, birth_date["year"]):
                        logger.info(f"✅ Filled deceased spouse birth year with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to fill deceased spouse birth year")
                    await take_screenshot(self.page, "deceased_birth_year_error.png")
                    return False
                await human_like_delay(0.5, 1.5)
            
            # Fill deceased spouse nationality
            logger.info("Selecting deceased spouse nationality...")
            success = False
            for selector in self.deceased_selectors["nationality"]:
                if await safe_select_by_text(self.page, selector, deceased.nationality):
                    logger.info(f"✅ Selected deceased spouse nationality with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to select deceased spouse nationality")
                await take_screenshot(self.page, "deceased_nationality_error.png")
                return False
            await human_like_delay(0.5, 1.5)
            
            # Fill place of birth city
            if deceased.place_of_birth_city_unknown:
                # Check "Do Not Know" checkbox
                logger.info("Checking 'Do Not Know' for birth city...")
                success = False
                for selector in self.deceased_selectors["birth_city_unknown"]:
                    if await safe_click(self.page, selector):
                        logger.info(f"✅ Checked 'Do Not Know' for birth city with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to check 'Do Not Know' for birth city")
                    await take_screenshot(self.page, "deceased_birth_city_unknown_error.png")
                    return False
            elif deceased.place_of_birth_city:
                # Fill birth city
                logger.info("Filling deceased spouse birth city...")
                success = False
                for selector in self.deceased_selectors["birth_city"]:
                    if await safe_fill(self.page, selector, deceased.place_of_birth_city):
                        logger.info(f"✅ Filled deceased spouse birth city with: {selector}")
                        success = True
                        break
                
                if not success:
                    logger.error("Failed to fill deceased spouse birth city")
                    await take_screenshot(self.page, "deceased_birth_city_error.png")
                    return False
            await human_like_delay(0.5, 1.5)
            
            # Fill birth country
            logger.info("Selecting deceased spouse birth country...")
            success = False
            for selector in self.deceased_selectors["birth_country"]:
                if await safe_select_by_text(self.page, selector, deceased.place_of_birth_country):
                    logger.info(f"✅ Selected deceased spouse birth country with: {selector}")
                    success = True
                    break
            
            if not success:
                logger.error("Failed to select deceased spouse birth country")
                await take_screenshot(self.page, "deceased_birth_country_error.png")
                return False
            await human_like_delay(0.5, 1.5)
            
            logger.info("✅ Successfully filled deceased spouse information")
            await take_screenshot(self.page, "deceased_spouse_completed.png")
            
            # Navigate to next page
            logger.info("🚀 Navigating to next page after filling deceased spouse information")
            navigation_result = await navigate_to_next_page(
                self.page, 
                current_section="deceased_spouse_information",
                timeout=30000
            )
            
            if navigation_result["success"]:
                logger.info(f"🎉 Successfully navigated from deceased spouse page")
                logger.info(f"   Method: {navigation_result['method_used']}")
                logger.info(f"   New URL: {navigation_result['new_url']}")
                return True
            else:
                logger.error(f"❌ Navigation failed after filling deceased spouse information")
                logger.error(f"   Errors: {navigation_result['errors']}")
                
                # Take debugging screenshot
                await take_screenshot(self.page, "deceased_spouse_navigation_failed.png")
                
                logger.error("❌ Navigation failed after filling deceased spouse information - returning False")
                logger.error("🛑 This will stop the bot to prevent infinite loops")
                return False
            
        except Exception as e:
            logger.error(f"Error filling deceased spouse information: {str(e)}")
            await take_screenshot(self.page, "deceased_spouse_error.png")
            return False
    
    async def fill_separated_spouse_info(self, data) -> bool:
        """complete_family2.aspx?node=Separated - для LEGALLY_SEPARATED."""
        logger.info("Filling separated spouse information")
        logger.warning("Separated spouse page handler not implemented yet")
        # TODO: Получить селекторы для страницы разделенных
        return True
    
    # TODO: Перенести fill_family_info из form_filler.py сюда
    async def fill_family_info(self, data) -> bool:
        """Заполнить основную семейную информацию (отец, мать, родственники)."""
        logger.info("Filling family information - delegating to form_filler for now")
        # Пока что делегируем к старой реализации
        # Позже перенесем логику сюда
        return True
    
    def _generate_repeater_selectors(self, repeater_id: str, field_name: str, element_type: str = "input") -> list:
        """Generate comprehensive selector list for ASP.NET DataList repeater fields.
        
        Args:
            repeater_id: The repeater control index (ctl00, ctl01, etc.)
            field_name: The field name (tbxSURNAME, ddlDOBDay, etc.)
            element_type: The HTML element type (input, select, textarea)
            
        Returns:
            List of selector strings ordered by reliability
        """
        selectors = [
            # Primary selectors - exact name and id matches
            f"{element_type}[name='ctl00$SiteContentPlaceHolder$FormView1$DListSpouse${repeater_id}${field_name}']",
            f"{element_type}[id='ctl00_SiteContentPlaceHolder_FormView1_DListSpouse_{repeater_id}_{field_name}']",
            
            # Secondary selectors - partial matches
            f"{element_type}[name*='DListSpouse${repeater_id}${field_name}']",
            f"{element_type}[id*='DListSpouse_{repeater_id}_{field_name}']",
            
            # Tertiary selectors - broader patterns
            f"{element_type}[name*='{repeater_id}'][name*='{field_name}']",
            f"{element_type}[id*='{repeater_id}'][id*='{field_name}']",
            
            # Fallback selectors - generic patterns
            f"{element_type}[name*='{field_name}'][name*='DListSpouse']",
            f"{element_type}[id*='{field_name}'][id*='DListSpouse']",
        ]
        
        return selectors
    
    async def _debug_page_state(self, screenshot_name: str) -> None:
        """Debug helper to capture page state and available form elements."""
        logger.info(f"🐛 [DEBUG] Capturing page state: {screenshot_name}")
        
        try:
            # Take screenshot
            await take_screenshot(self.page, f"{screenshot_name}.png")
            
            # Log current URL and title
            page_url = self.page.url
            page_title = await self.page.title()
            logger.info(f"🐛 [DEBUG] Page URL: {page_url}")
            logger.info(f"🐛 [DEBUG] Page Title: {page_title}")
            
            # Find all text inputs and log their properties
            text_inputs = await self.page.query_selector_all("input[type='text']")
            logger.info(f"🐛 [DEBUG] Found {len(text_inputs)} text inputs on page:")
            
            for idx, input_elem in enumerate(text_inputs[:10]):  # Limit to first 10
                try:
                    name_attr = await input_elem.get_attribute("name") or ""
                    id_attr = await input_elem.get_attribute("id") or ""
                    value_attr = await input_elem.input_value() or ""
                    visible = await input_elem.is_visible()
                    enabled = await input_elem.is_enabled()
                    
                    logger.info(f"   Input {idx+1}: name='{name_attr}', id='{id_attr}', value='{value_attr}', visible={visible}, enabled={enabled}")
                except Exception as e:
                    logger.warning(f"   Input {idx+1}: Could not get properties - {e}")
            
            # Find all select elements
            select_elements = await self.page.query_selector_all("select")
            logger.info(f"🐛 [DEBUG] Found {len(select_elements)} select elements on page")
            
            # Look for any error messages
            error_elements = await self.page.query_selector_all(".error, .validation-error, [class*='error'], [id*='error']")
            if error_elements:
                logger.warning(f"🐛 [DEBUG] Found {len(error_elements)} potential error elements:")
                for idx, error_elem in enumerate(error_elements[:5]):
                    try:
                        error_text = await error_elem.text_content()
                        if error_text and error_text.strip():
                            logger.warning(f"   Error {idx+1}: {error_text.strip()}")
                    except Exception:
                        pass
                        
        except Exception as e:
            logger.error(f"🐛 [DEBUG] Failed to capture debug info: {e}")