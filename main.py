#!/usr/bin/env python3
"""CLI entry point for DS-160 bot."""
import asyncio
import sys
from pathlib import Path
import click
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config, get_config
from src.bot import DS160Bot
from src.data_loader import create_example_data
from src.utils import load_application_data
import json

@click.group()
@click.option('--config', '-c', default="config/config.yaml", help="Configuration file path")
@click.option('--headless/--headful', default=None, help="Override headless browser mode")
@click.option('--verbose', '-v', is_flag=True, help="Enable verbose logging")
@click.pass_context
def cli(ctx, config, headless, verbose):
    """DS-160 Visa Application Bot CLI."""
    ctx.ensure_object(dict)
    
    # Load configuration
    try:
        ctx.obj['config'] = get_config()
        
        # Override headless mode if specified
        if headless is not None:
            ctx.obj['config'].browser.headless = headless
            
        # Set logging level
        if verbose:
            ctx.obj['config'].logging.level = "DEBUG"
            
    except Exception as e:
        click.echo(f"Error loading configuration: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.option('--data-file', '-d', help="JSON data file path")
@click.option('--resume', '-r', is_flag=True, help="Resume from previous session")
@click.option('--dry-run', is_flag=True, help="Run without submitting")
@click.option('--save-browser-state', '--save-state', is_flag=True, help="Save browser state (cookies, localStorage) for resuming")
@click.option('--load-browser-state', '--load-state', help="Load browser state file path")
@click.option('--browser-state-file', '--state-file', default="session_data/browser_state.json", help="Browser state file path")
@click.pass_context
def run(ctx, data_file, resume, dry_run, save_browser_state, load_browser_state, browser_state_file):
    """Run the DS-160 application bot."""
    config: Config = ctx.obj['config']
    
    # Override data file if provided
    if data_file:
        config.data_file = data_file
    
    # Ensure auto-submit is disabled for dry runs
    if dry_run:
        config.security.auto_submit = False
        click.echo("🔒 Dry run mode: Auto-submit disabled")
    
    # Legal warning
    click.echo("⚠️  LEGAL WARNING:")
    click.echo("⚠️  This bot automates the DS-160 visa application form.")
    click.echo("⚠️  Automating government forms may violate terms of service.")
    click.echo("⚠️  Use at your own risk and legal responsibility.")
    click.echo("⚠️  Manual review of all information is strongly recommended.")
    click.echo()
    
    # Temporarily commented out for testing
    # if not click.confirm("Do you understand and accept these risks?"):
    #     click.echo("Aborted.")
    #     return
    
    # Run the bot
    async def run_bot():
        # Prepare browser state options
        browser_options = {
            'save_browser_state': save_browser_state,
            'load_browser_state': load_browser_state,
            'browser_state_file': browser_state_file
        }
        
        async with DS160Bot(config, browser_options=browser_options) as bot:
            success = await bot.run(data_file, resume)
            return success
    
    try:
        success = asyncio.run(run_bot())
        if success:
            click.echo("✅ Bot completed successfully")
            sys.exit(0)
        else:
            click.echo("❌ Bot failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        click.echo("\n🛑 Bot interrupted by user")
        sys.exit(130)
    except Exception as e:
        click.echo(f"💥 Bot crashed: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.option('--output', '-o', default="data/example_data.json", help="Output file path")
def create_example(output):
    """Create an example data file."""
    try:
        # Create output directory
        output_path = Path(output)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Generate example data
        example_data = create_example_data()
        
        # Save to file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(example_data, f, indent=2, ensure_ascii=False)
        
        click.echo(f"📄 Example data file created: {output}")
        click.echo("📝 Please edit this file with your actual information before running the bot")
        
    except Exception as e:
        click.echo(f"Error creating example data: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.option('--data-file', '-d', required=True, help="JSON data file to validate")
def validate(data_file):
    """Validate a data file."""
    try:
        from src.data_loader import DataLoader
        
        loader = DataLoader(data_file)
        data = asyncio.run(loader.load_data())
        
        # Validate data
        issues = loader.validate_data()
        
        if issues:
            click.echo("❌ Validation failed:")
            for issue in issues:
                click.echo(f"  • {issue}")
            sys.exit(1)
        else:
            click.echo("✅ Data file is valid")
            
            # Show summary
            click.echo(f"\n📊 Data Summary:")
            full_name = f"{data.personal_info.given_names} {data.personal_info.surnames}"
            click.echo(f"  Name: {full_name}")
            click.echo(f"  Country of Origin: {data.nationality_and_residence.country_of_origin}")
            click.echo(f"  Purpose: {data.travel_info.purpose_of_trip}")
            click.echo(f"  Arrival: {data.travel_info.arrival_date}")
            
    except Exception as e:
        click.echo(f"Error validating data: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.option('--output', '-o', default="config/config.yaml", help="Output config file path")
def init_config(output):
    """Initialize configuration file."""
    try:
        config = Config()
        output_path = Path(output)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save default config
        import yaml
        with open(output_path, 'w') as f:
            yaml.dump(config.model_dump(), f, default_flow_style=False)
        
        click.echo(f"⚙️  Configuration file created: {output}")
        click.echo("📝 Please review and modify settings as needed")
        
    except Exception as e:
        click.echo(f"Error creating config: {e}", err=True)
        sys.exit(1)

@cli.command()
def version():
    """Show version information."""
    try:
        from src import __version__
        click.echo(f"DS-160 Bot v{__version__}")
    except ImportError:
        click.echo("DS-160 Bot (version unknown)")

@cli.command()
def doctor():
    """Check system requirements and configuration."""
    click.echo("🔍 Running system diagnostics...")
    
    issues = []
    
    # Check Python version
    if sys.version_info < (3, 10):
        issues.append(f"Python 3.10+ required, found {sys.version}")
    else:
        click.echo("✅ Python version: OK")
    
    # Check required packages
    required_packages = {
        'playwright': 'playwright',
        'pydantic': 'pydantic', 
        'pyyaml': 'yaml',  # pyyaml imports as 'yaml'
        'loguru': 'loguru'
    }
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            click.echo(f"✅ {package_name}: OK")
        except ImportError:
            issues.append(f"Missing package: {package_name}")
    
    # Check Playwright browsers
    try:
        import subprocess
        result = subprocess.run(['playwright', 'install', '--dry-run'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            click.echo("✅ Playwright: OK")
        else:
            issues.append("Playwright browsers not installed")
    except FileNotFoundError:
        issues.append("Playwright CLI not found")
    
    # Check configuration
    try:
        config = get_config()
        click.echo("✅ Configuration: OK")
    except Exception as e:
        issues.append(f"Configuration error: {e}")
    
    # Report results
    if issues:
        click.echo("\n❌ Issues found:")
        for issue in issues:
            click.echo(f"  • {issue}")
        click.echo("\n🔧 Suggested fixes:")
        click.echo("  pip install -r requirements.txt")
        click.echo("  playwright install")
        sys.exit(1)
    else:
        click.echo("\n✅ All checks passed! System ready.")

@cli.command()
def show_saved():
    """Show saved application data for resume."""
    try:
        import asyncio
        saved_data = asyncio.run(load_application_data())
        
        if not saved_data:
            click.echo("📭 No saved application data found")
            return
        
        click.echo("🔐 Saved Application Data:")
        click.echo(f"  Application ID: {saved_data.application_id or 'Not captured'}")
        click.echo(f"  Surname: {saved_data.surname or 'Not filled'}")
        click.echo(f"  Security Answer: {'*' * len(saved_data.security_question_answer) if saved_data.security_question_answer else 'Not set'}")
        click.echo(f"  Birth Year: {saved_data.year_of_birth or 'Not filled'}")
        click.echo(f"  Current Section: {saved_data.current_section or 'Unknown'}")
        click.echo(f"  Last Updated: {saved_data.last_updated}")
        
        if saved_data.application_id and saved_data.surname and saved_data.security_question_answer and saved_data.year_of_birth:
            click.echo("\n✅ You have complete data to resume the application!")
            click.echo("💡 Use: python main.py run --resume")
        else:
            click.echo("\n⚠️  Incomplete data - some fields need to be filled during resume")
        
    except Exception as e:
        click.echo(f"Error loading saved data: {e}", err=True)

if __name__ == '__main__':
    cli() 