"""Tests for configuration module."""
import pytest
import tempfile
import os
from pathlib import Path
import yaml
from src.config import Config, BrowserConfig, get_config

class TestConfig:
    """Test cases for Config class."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        config = Config()
        
        assert config.browser.headless is True
        assert config.browser.browser_type == "chromium"
        assert config.browser.timeout == 30000
        assert config.retry.max_attempts == 3
        assert config.security.auto_submit is False
        assert config.ds160_url == "https://ceac.state.gov/genniv/"
    
    def test_browser_config(self):
        """Test browser configuration."""
        browser_config = BrowserConfig(
            headless=False,
            browser_type="firefox",
            viewport_width=1280,
            viewport_height=720
        )
        
        assert browser_config.headless is False
        assert browser_config.browser_type == "firefox"
        assert browser_config.viewport_width == 1280
        assert browser_config.viewport_height == 720
    
    def test_load_from_file_nonexistent(self):
        """Test loading from non-existent file creates default."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "test_config.yaml"
            config = Config.load_from_file(str(config_path))
            
            # Should create default config
            assert isinstance(config, Config)
            assert config_path.exists()
            
            # File should contain valid YAML
            with open(config_path) as f:
                yaml_data = yaml.safe_load(f)
            assert isinstance(yaml_data, dict)
            assert "browser" in yaml_data
    
    def test_load_from_file_existing(self):
        """Test loading from existing file."""
        test_config = {
            "browser": {
                "headless": False,
                "browser_type": "firefox",
                "timeout": 20000
            },
            "security": {
                "auto_submit": True
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_config, f)
            config_path = f.name
        
        try:
            config = Config.load_from_file(config_path)
            assert config.browser.headless is False
            assert config.browser.browser_type == "firefox"
            assert config.browser.timeout == 20000
            assert config.security.auto_submit is True
        finally:
            Path(config_path).unlink()
    
    def test_load_from_env(self):
        """Test loading from environment variables."""
        # Set test environment variables
        env_vars = {
            "HEADLESS": "false",
            "AUTO_SUBMIT": "true",
            "DATA_FILE": "custom_data.json",
            "CAPTCHA_API_KEY": "test_key"
        }
        
        original_env = {}
        try:
            # Set environment variables
            for key, value in env_vars.items():
                original_env[key] = os.environ.get(key)
                os.environ[key] = value
            
            config = Config.load_from_env()
            
            # Check that environment variables were applied
            assert config.browser.headless is False
            assert config.security.auto_submit is True
            assert config.data_file == "custom_data.json"
            assert config.captcha_service_api_key == "test_key"
            
        finally:
            # Restore original environment
            for key, original_value in original_env.items():
                if original_value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = original_value
    
    def test_get_config_merge(self):
        """Test that get_config properly merges file and env configs."""
        # Create a test config file
        test_config = {
            "browser": {
                "headless": True,
                "browser_type": "chromium",
                "timeout": 30000
            },
            "security": {
                "auto_submit": False
            },
            "data_file": "file_data.json"
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_config, f)
            config_path = f.name
        
        env_vars = {
            "HEADLESS": "false",  # Override file setting
            "DATA_FILE": "env_data.json"  # Override file setting
        }
        
        original_env = {}
        try:
            # Set environment variables
            for key, value in env_vars.items():
                original_env[key] = os.environ.get(key)
                os.environ[key] = value
            
            # Mock the default config file path
            import src.config
            original_load_from_file = src.config.Config.load_from_file
            src.config.Config.load_from_file = lambda cls, path="config/config.yaml": Config.load_from_file(config_path)
            
            config = get_config()
            
            # Environment should override file
            assert config.browser.headless is False  # From env
            assert config.data_file == "env_data.json"  # From env
            assert config.browser.browser_type == "chromium"  # From file
            assert config.security.auto_submit is False  # From file
            
        finally:
            # Restore
            src.config.Config.load_from_file = original_load_from_file
            for key, original_value in original_env.items():
                if original_value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = original_value
            Path(config_path).unlink() 