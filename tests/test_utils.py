"""Tests for utility functions."""
import pytest
import asyncio
from datetime import date, datetime
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from src.utils import (
    format_date_for_form, mask_sensitive, validate_email, validate_phone, format_phone_for_ds160,
    create_session_state, sanitize_filename, get_random_delay,
    retry_async, human_like_delay
)

class TestUtilityFunctions:
    """Test cases for utility functions."""
    
    def test_format_date_for_form_mdy(self):
        """Test date formatting for MDY format."""
        test_date = date(2023, 12, 25)
        result = format_date_for_form(test_date, "mdy")
        
        assert result == {
            "month": "12",
            "day": "25", 
            "year": "2023"
        }
    
    def test_format_date_for_form_dmy(self):
        """Test date formatting for DMY format."""
        test_date = date(2023, 5, 8)
        result = format_date_for_form(test_date, "dmy")
        
        assert result == {
            "day": "8",
            "month": "5",
            "year": "2023"
        }
    
    def test_format_date_for_form_invalid_format(self):
        """Test date formatting with invalid format."""
        test_date = date(2023, 1, 1)
        
        with pytest.raises(ValueError, match="Unsupported date format"):
            format_date_for_form(test_date, "invalid")
    
    def test_mask_sensitive_data(self):
        """Test sensitive data masking."""
        # Test normal case
        result = mask_sensitive("password123", visible_chars=4)
        assert result == "pass*******"
        
        # Test short string
        result = mask_sensitive("abc", visible_chars=4)
        assert result == "***"
        
        # Test empty string
        result = mask_sensitive("", visible_chars=4)
        assert result == ""
        
        # Test custom mask character
        result = mask_sensitive("secret", mask_char="#", visible_chars=2)
        assert result == "se####"
    
    def test_validate_email(self):
        """Test email validation."""
        # Valid emails
        assert validate_email("<EMAIL>") is True
        assert validate_email("<EMAIL>") is True
        assert validate_email("<EMAIL>") is True
        
        # Invalid emails
        assert validate_email("invalid-email") is False
        assert validate_email("@example.com") is False
        assert validate_email("test@") is False
        assert validate_email("<EMAIL>") is False
        assert validate_email("") is False
    
    def test_validate_phone(self):
        """Test phone number validation."""
        # Valid phone numbers
        assert validate_phone("******-123-4567") is True
        assert validate_phone("+44 20 7946 0958") is True
        assert validate_phone("************") is True
        assert validate_phone("(*************") is True
        assert validate_phone("15551234567") is True
        
        # Invalid phone numbers
        assert validate_phone("123") is False  # Too short
        assert validate_phone("123456789012345678") is False  # Too long
        assert validate_phone("") is False
        assert validate_phone("abc-def-ghij") is False

    def test_format_phone_for_ds160(self):
        """Test phone number formatting for DS-160."""
        # Valid formatted phone numbers
        assert format_phone_for_ds160("******-123-4567") == "7001234567"
        assert format_phone_for_ds160("******-123-4567") == "5551234567"  
        assert format_phone_for_ds160("87017023344") == "87017023344"
        assert format_phone_for_ds160("(*************") == "5551234567"
        assert format_phone_for_ds160("+44 20 7946 0958") == "2079460958"
        
        # Country code removal
        assert format_phone_for_ds160("+7700123456") == "7700123456"  # Kazakhstan +7, > 10 digits
        assert format_phone_for_ds160("+15551234567") == "5551234567"  # US +1, exactly 11 digits
        
        # Invalid cases return empty string
        assert format_phone_for_ds160("123") == ""  # Too short
        assert format_phone_for_ds160("") == ""  # Empty
        assert format_phone_for_ds160("abc-def-ghij") == ""  # Non-numeric
        
        # Truncation for too long numbers
        assert format_phone_for_ds160("12345678901234567890") == "123456789012345"  # Truncated to 15
    
    def test_create_session_state(self):
        """Test session state creation."""
        state = create_session_state()
        
        assert isinstance(state, dict)
        assert "started_at" in state
        assert "current_page" in state
        assert "completed_sections" in state
        assert "errors" in state
        assert "form_data" in state
        assert "last_action" in state
        assert "session_id" in state
        
        assert state["current_page"] == "start"
        assert state["completed_sections"] == []
        assert state["errors"] == []
        assert state["form_data"] == {}
        assert state["last_action"] is None
        assert state["session_id"] is None
        
        # Check that started_at is a valid ISO timestamp
        datetime.fromisoformat(state["started_at"])
    
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        # Test invalid characters
        result = sanitize_filename("file<>name.txt")
        assert result == "file__name.txt"
        
        # Test multiple invalid characters
        result = sanitize_filename("file:name|with?chars*.txt")
        assert result == "file_name_with_chars_.txt"
        
        # Test long filename
        long_name = "a" * 300 + ".txt"
        result = sanitize_filename(long_name)
        assert len(result) <= 255
        assert result.endswith(".txt")
        
        # Test normal filename
        result = sanitize_filename("normal_file.txt")
        assert result == "normal_file.txt"
    
    def test_get_random_delay(self):
        """Test random delay generation."""
        delay = get_random_delay(1.0, 2.0)
        assert 1.0 <= delay <= 2.0
        
        # Test default values
        delay = get_random_delay()
        assert 0.5 <= delay <= 2.0
        
        # Test same min/max
        delay = get_random_delay(1.5, 1.5)
        assert delay == 1.5
    
    @pytest.mark.asyncio
    async def test_human_like_delay(self):
        """Test human-like delay function."""
        start_time = datetime.now()
        await human_like_delay(0.1, 0.2)
        end_time = datetime.now()
        
        elapsed = (end_time - start_time).total_seconds()
        assert 0.1 <= elapsed <= 0.3  # Allow some margin for execution time
    
    @pytest.mark.asyncio
    async def test_retry_async_success(self):
        """Test async retry with successful function."""
        call_count = 0
        
        async def successful_function():
            nonlocal call_count
            call_count += 1
            return "success"
        
        result = await retry_async(successful_function, max_attempts=3)
        assert result == "success"
        assert call_count == 1
    
    @pytest.mark.asyncio
    async def test_retry_async_eventual_success(self):
        """Test async retry with eventual success."""
        call_count = 0
        
        async def eventually_successful_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError("Not yet")
            return "success"
        
        result = await retry_async(
            eventually_successful_function, 
            max_attempts=3,
            base_delay=0.01  # Fast for testing
        )
        assert result == "success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_retry_async_max_attempts_exceeded(self):
        """Test async retry when max attempts exceeded."""
        call_count = 0
        
        async def always_failing_function():
            nonlocal call_count
            call_count += 1
            raise ValueError("Always fails")
        
        with pytest.raises(ValueError, match="Always fails"):
            await retry_async(
                always_failing_function,
                max_attempts=3,
                base_delay=0.01
            )
        
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_retry_async_with_args_kwargs(self):
        """Test async retry with function arguments."""
        async def function_with_args(arg1, arg2, kwarg1=None):
            return f"{arg1}-{arg2}-{kwarg1}"
        
        result = await retry_async(
            function_with_args,
            "test",
            "value",
            kwarg1="keyword",
            max_attempts=1
        )
        
        assert result == "test-value-keyword"

class TestAsyncUtilities:
    """Test async utility functions that require mocked browser objects."""
    
    @pytest.fixture
    def mock_page(self):
        """Create a mock Playwright page object."""
        page = AsyncMock()
        page.locator.return_value = AsyncMock()
        return page
    
    @pytest.mark.asyncio
    async def test_wait_for_element_success(self, mock_page):
        """Test successful element waiting."""
        from src.utils import wait_for_element
        
        mock_locator = AsyncMock()
        mock_page.locator.return_value = mock_locator
        
        result = await wait_for_element(mock_page, "test-selector")
        
        assert result == mock_locator
        mock_page.locator.assert_called_once_with("test-selector")
        mock_locator.wait_for.assert_called_once_with(state="visible", timeout=30000)
    
    @pytest.mark.asyncio
    async def test_wait_for_element_timeout(self, mock_page):
        """Test element waiting with timeout."""
        from src.utils import wait_for_element
        
        mock_locator = AsyncMock()
        mock_locator.wait_for.side_effect = Exception("Timeout")
        mock_page.locator.return_value = mock_locator
        
        result = await wait_for_element(mock_page, "test-selector", timeout=1000)
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_safe_click_success(self, mock_page):
        """Test successful element clicking."""
        from src.utils import safe_click
        
        mock_locator = AsyncMock()
        mock_page.locator.return_value = mock_locator
        
        result = await safe_click(mock_page, "test-selector")
        
        assert result is True
        mock_locator.click.assert_called_once_with(force=False)
    
    @pytest.mark.asyncio
    async def test_safe_fill_success(self, mock_page):
        """Test successful form filling."""
        from src.utils import safe_fill
        
        mock_locator = AsyncMock()
        mock_page.locator.return_value = mock_locator
        
        result = await safe_fill(mock_page, "input-selector", "test value")
        
        assert result is True
        mock_locator.clear.assert_called_once()
        mock_locator.fill.assert_called_once_with("test value")
    
    @pytest.mark.asyncio
    async def test_element_exists_true(self, mock_page):
        """Test element existence check - exists."""
        from src.utils import element_exists
        
        mock_locator = AsyncMock()
        mock_page.locator.return_value = mock_locator
        
        result = await element_exists(mock_page, "test-selector")
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_element_exists_false(self, mock_page):
        """Test element existence check - doesn't exist."""
        from src.utils import element_exists
        
        mock_locator = AsyncMock()
        mock_locator.wait_for.side_effect = Exception("Not found")
        mock_page.locator.return_value = mock_locator
        
        result = await element_exists(mock_page, "test-selector")
        
        assert result is False 