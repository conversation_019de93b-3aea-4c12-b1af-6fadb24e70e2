"""Tests for data loader module."""
import pytest
import json
import tempfile
from pathlib import Path
from datetime import date
from src.data_loader import DataLoader, DS160Data, create_example_data, Gender, MaritalStatus

class TestDataLoader:
    """Test cases for DataLoader class."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample test data."""
        return create_example_data()
    
    @pytest.fixture
    def temp_data_file(self, sample_data):
        """Create temporary data file for testing."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_data, f)
            return f.name
    
    def test_create_example_data(self):
        """Test example data creation."""
        data = create_example_data()
        assert isinstance(data, dict)
        assert "personal_info" in data
        assert "passport_info" in data
        assert "contact_info" in data
        assert "travel_info" in data
    
    @pytest.mark.asyncio
    async def test_load_valid_data(self, temp_data_file):
        """Test loading valid data."""
        loader = DataLoader(temp_data_file)
        data = await loader.load_data()
        
        assert isinstance(data, DS160Data)
        assert data.personal_info.surname == "Doe"
        assert data.personal_info.gender == Gender.MALE
        assert data.personal_info.marital_status == MaritalStatus.SINGLE
        assert isinstance(data.personal_info.date_of_birth, date)
    
    @pytest.mark.asyncio
    async def test_load_nonexistent_file(self):
        """Test loading non-existent file."""
        loader = DataLoader("nonexistent_file.json")
        
        with pytest.raises(FileNotFoundError):
            await loader.load_data()
    
    @pytest.mark.asyncio
    async def test_load_invalid_json(self):
        """Test loading invalid JSON."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content")
            temp_file = f.name
        
        loader = DataLoader(temp_file)
        
        with pytest.raises(ValueError, match="Invalid JSON"):
            await loader.load_data()
        
        # Cleanup
        Path(temp_file).unlink()
    
    def test_validate_data_success(self, temp_data_file):
        """Test successful data validation."""
        loader = DataLoader(temp_data_file)
        # Need to load data first
        import asyncio
        data = asyncio.run(loader.load_data())
        
        issues = loader.validate_data()
        assert isinstance(issues, list)
        # Should have no issues with valid data
        assert len(issues) == 0 or all("future" in issue.lower() for issue in issues)
    
    def test_validate_data_missing_fields(self):
        """Test validation with missing required fields."""
        invalid_data = {
            "personal_info": {
                "surname": "",  # Empty surname should fail
                "given_names": "John",
                "gender": "Male",
                "marital_status": "Single",
                "date_of_birth": "1990-01-01",
                "city_of_birth": "City",
                "country_of_birth": "Country",
                "nationality": "Nationality"
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(invalid_data, f)
            temp_file = f.name
        
        try:
            loader = DataLoader(temp_file)
            import asyncio
            with pytest.raises(ValueError):
                asyncio.run(loader.load_data())
        finally:
            Path(temp_file).unlink()
    
    def test_date_conversion(self, temp_data_file):
        """Test date string to date object conversion."""
        loader = DataLoader(temp_data_file)
        
        # Test date field detection
        assert loader._is_date_field("date_of_birth")
        assert loader._is_date_field("start_date")
        assert loader._is_date_field("expiration_date")
        assert not loader._is_date_field("regular_field")
        
        # Test date conversion
        test_data = {
            "date_of_birth": "1990-01-15",
            "regular_field": "not a date",
            "nested": {
                "start_date": "2020-01-01"
            }
        }
        
        converted = loader._convert_dates(test_data)
        assert isinstance(converted["date_of_birth"], date)
        assert converted["regular_field"] == "not a date"
        assert isinstance(converted["nested"]["start_date"], date)

class TestDS160DataModel:
    """Test cases for DS160Data model validation."""
    
    def test_personal_info_validation(self):
        """Test personal info validation."""
        from src.data_loader import PersonalInfo
        
        # Valid data
        valid_data = {
            "surname": "Smith",
            "given_names": "John",
            "gender": "Male",
            "marital_status": "Single",
            "date_of_birth": date(1990, 1, 15),
            "city_of_birth": "Toronto",
            "country_of_birth": "Canada",
            "nationality": "Canada"
        }
        
        personal_info = PersonalInfo(**valid_data)
        assert personal_info.full_name == "John Smith"
        assert personal_info.surname == "Smith"
    
    def test_passport_info_validation(self):
        """Test passport info validation."""
        from src.data_loader import PassportInfo
        
        # Valid passport
        valid_data = {
            "passport_number": "AB123456",
            "country_of_issuance": "Canada",
            "issuance_date": date(2020, 1, 1),
            "expiration_date": date(2030, 1, 1)
        }
        
        passport_info = PassportInfo(**valid_data)
        assert passport_info.passport_number == "AB123456"
        
        # Invalid passport (expiration before issuance)
        invalid_data = {
            "passport_number": "AB123456",
            "country_of_issuance": "Canada",
            "issuance_date": date(2030, 1, 1),
            "expiration_date": date(2020, 1, 1)
        }
        
        with pytest.raises(ValueError, match="Expiration date must be after issuance date"):
            PassportInfo(**invalid_data)
    
    def test_travel_info_validation(self):
        """Test travel info validation."""
        from src.data_loader import TravelInfo, Address, VisaPurpose
        from datetime import date, timedelta
        
        future_date = date.today() + timedelta(days=30)
        
        address_data = {
            "street_address": "123 Main St",
            "city": "New York",
            "state_province": "NY",
            "postal_code": "10001",
            "country": "United States"
        }
        
        valid_data = {
            "purpose_of_trip": VisaPurpose.TOURISM,
            "intended_arrival_date": future_date,
            "intended_length_of_stay": 14,
            "address_in_us": Address(**address_data),
            "who_is_paying": "Self"
        }
        
        travel_info = TravelInfo(**valid_data)
        assert travel_info.purpose_of_trip == VisaPurpose.TOURISM
        assert travel_info.intended_length_of_stay == 14 