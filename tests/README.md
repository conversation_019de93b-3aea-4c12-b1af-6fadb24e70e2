# DS-160 Bot Testing Suite

Этот каталог содержит comprehensive testing suite для DS-160 бота, включая тесты для всех выявленных проблем.

## 📁 Структура тестов

```
tests/
├── README.md                          # Этот файл
├── conftest.py                        # Pytest конфигурация и fixtures
├── test_form_filling_issues.py        # Основные тесты проблемных случаев
├── test_data_loader.py                # Существующие тесты data loader
├── test_utils.py                      # Существующие тесты утилит
├── test_config.py                     # Существующие тесты конфигурации
└── test_data/                         # Тестовые данные
    └── issues/                        # Данные для проблемных случаев
        ├── dual_nationality_3_countries.json
        ├── social_media_2_instagram.json
        ├── us_visit_history_yes.json
        ├── visa_refused_yes_with_explanation.json
        ├── parents_both_in_us.json
        └── ...
```

## 🚀 Быстрый старт

### Установка зависимостей
```bash
# Основные зависимости
pip install -r requirements.txt

# Дополнительные зависимости для тестирования (если нужны)
pip install pytest-asyncio pytest-mock
```

### Запуск всех тестов
```bash
# Все тесты
pytest tests/ -v

# Только новые тесты проблем
pytest tests/test_form_filling_issues.py -v

# С coverage
pytest tests/ --cov=src --cov-report=html
```

### Запуск конкретных групп тестов
```bash
# Только unit tests
pytest tests/ -m unit -v

# Только integration tests
pytest tests/ -m integration -v

# Только тесты проблем
pytest tests/ -m issues -v

# Только regression tests
pytest tests/ -m regression -v
```

## 🎯 Тестирование конкретных проблем

### Issue #3: Двойное гражданство
```bash
pytest -k "dual_nationality" -v
```

### Issue #8: Социальные сети
```bash
pytest -k "social_media" -v
```

### Issue #5: История визитов в США
```bash
pytest -k "us_visit_history" -v
```

### Issue #6: Отказы в визе
```bash
pytest -k "visa_refusal" -v
```

### Issue #10-11: Родители в США
```bash
pytest -k "parents_in_us" -v
```

## 📊 Test Markers

Тесты помечены следующими markers:

- `@pytest.mark.unit` - Unit тесты (быстрые, изолированные)
- `@pytest.mark.integration` - Integration тесты (полные сценарии)
- `@pytest.mark.issues` - Тесты для конкретных выявленных проблем
- `@pytest.mark.regression` - Regression тесты (проверяют что исправления не ломают работающую функциональность)
- `@pytest.mark.asyncio` - Асинхронные тесты

## 🧪 Mock Testing Framework

### MockDS160Page
Основной mock object для симуляции Playwright Page:

```python
async def test_example(mock_page):
    # Настройка mock
    mock_page.set_element_exists("input[name='test']", True)
    
    # Действие
    await mock_page.fill("input[name='test']", "value")
    await mock_page.click("button[type='submit']")
    
    # Проверка
    assert mock_page.get_filled_value("input[name='test']") == "value"
    assert mock_page.was_clicked("button[type='submit']")
```

### TestDataFactory
Фабрика для создания тестовых данных:

```python
def test_example(test_data_factory):
    # Создание тестовых данных
    data = test_data_factory.create_dual_nationality_data()
    
    # Использование в тесте
    assert len(data["nationalityAndResidence"]["otherNationalities"]) > 1
```

## 📝 Написание новых тестов

### 1. Unit Test Template
```python
@pytest.mark.unit
def test_new_functionality():
    """Test description."""
    # Arrange
    data = create_test_data()
    
    # Act
    result = function_under_test(data)
    
    # Assert
    assert result.success
```

### 2. Integration Test Template
```python
@pytest.mark.asyncio
@pytest.mark.integration
async def test_new_integration(mock_page):
    """Integration test description."""
    # Arrange
    data = load_test_data("test_case.json")
    mock_page.set_element_exists("selector", True)
    
    # Act
    result = await form_filling_function(mock_page, data)
    
    # Assert
    assert result.success
    assert mock_page.was_clicked("expected_selector")
```

### 3. Issue Test Template
```python
@pytest.mark.asyncio
@pytest.mark.issues
async def test_issue_123_description(mock_page):
    """Test for Issue #123: Problem description."""
    # Arrange - Load specific test data for this issue
    test_data_path = Path(__file__).parent / "test_data" / "issues" / "issue_123.json"
    with open(test_data_path, 'r') as f:
        data_dict = json.load(f)
    
    loader = DataLoader.from_dict(data_dict)
    data = await loader.load_data()
    
    # Mock setup
    mock_page.set_element_exists("problematic_selector", True)
    
    # Act
    result = await function_with_issue(mock_page, data)
    
    # Assert - Verify the issue is fixed
    assert result.success
    assert expected_behavior_happened
```

## 🗂️ Создание тестовых данных

### Для новой проблемы:
1. Создайте JSON файл в `tests/test_data/issues/`
2. Используйте существующие файлы как template
3. Убедитесь что данные воспроизводят проблему
4. Добавьте edge cases

### Пример структуры:
```json
{
  "personalInfo": {
    "surnames": "TEST_SURNAME",
    "givenNames": "TEST_NAME",
    // ... basic required fields
  },
  "specificProblemArea": {
    // Data that triggers the specific issue
    "problematicField": "value_that_causes_issue"
  }
}
```

## 🔧 Debugging тестов

### Включить verbose output:
```bash
pytest tests/test_form_filling_issues.py::TestClass::test_method -v -s
```

### Запустить с debugger:
```bash
pytest tests/test_form_filling_issues.py::test_method -v -s --pdb
```

### Показать весь output:
```bash
pytest tests/test_form_filling_issues.py -v -s --tb=long
```

## ⚡ Performance

### Быстрые тесты (unit):
```bash
pytest tests/ -m unit  # ~2-5 секунд
```

### Все тесты:
```bash
pytest tests/  # ~10-30 секунд
```

### Параллельное выполнение:
```bash
pip install pytest-xdist
pytest tests/ -n auto  # Использовать все CPU cores
```

## 📈 Coverage

### HTML отчет:
```bash
pytest tests/ --cov=src --cov-report=html
# Открыть htmlcov/index.html в браузере
```

### Terminal отчет:
```bash
pytest tests/ --cov=src --cov-report=term-missing
```

### Цель coverage:
- **Unit tests**: >95%
- **Integration tests**: >90%
- **Overall**: >80% (enforced by pytest.ini)

## 🐛 Известные ограничения

1. **Mock limitations**: Мы имитируем DS-160 HTML, но реальная форма может отличаться
2. **Selector updates**: Если DS-160 сайт обновляется, селекторы могут перестать работать
3. **JavaScript behavior**: Мы не имитируем JavaScript поведение форм
4. **Network timing**: Реальные задержки сети не имитируются

## 🔄 Continuous Integration

Тесты настроены для автоматического запуска в CI/CD:

```yaml
# .github/workflows/test.yml example
- name: Run tests
  run: |
    pytest tests/ --cov=src --cov-report=xml
    pytest tests/ -m "not slow"  # Exclude slow tests in CI
```

## 🆘 Troubleshooting

### Тесты не находят модули:
```bash
export PYTHONPATH="${PYTHONPATH}:/path/to/visa-ds160"
pytest tests/
```

### Async warnings:
```bash
pip install pytest-asyncio
# Или добавить в pytest.ini: asyncio_mode = auto
```

### Import errors:
```bash
# Запускать из корня проекта
cd /path/to/visa-ds160
pytest tests/
```

---

## 📞 Поддержка

При проблемах с тестами:

1. Проверьте что все зависимости установлены
2. Запустите тесты из корня проекта
3. Проверьте pytest.ini конфигурацию
4. Посмотрите на существующие тесты как примеры

**Быстрая помощь:**
```bash
# Проверить окружение
python -m pytest --version
python -c "import src; print('Imports OK')"

# Запустить простейший тест
pytest tests/test_config.py -v
```