"""Pytest configuration and fixtures for DS-160 bot testing."""

import pytest
import asyncio
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, AsyncMock

from src.data_loader import DataLoader, DS160Data


class MockDS160Page:
    """Mock implementation of Playwright Page for DS-160 testing."""
    
    def __init__(self):
        self.html_content = ""
        self.filled_fields: Dict[str, str] = {}
        self.clicked_elements: List[str] = []
        self.selected_options: Dict[str, str] = {}
        self.url = "https://ceac.state.gov/genniv/test"
        self._element_exists_map: Dict[str, bool] = {}
        
    async def set_content(self, html: str):
        """Set HTML content for the mock page."""
        self.html_content = html
        
    async def fill(self, selector: str, value: str, **kwargs) -> bool:
        """Mock fill method."""
        if self.element_exists(selector):
            self.filled_fields[selector] = value
            return True
        return False
        
    async def click(self, selector: str, **kwargs) -> bool:
        """Mock click method."""
        if self.element_exists(selector):
            self.clicked_elements.append(selector)
            return True
        return False
        
    async def select_option(self, selector: str, value: str, **kwargs) -> bool:
        """Mock select option method."""
        if self.element_exists(selector):
            self.selected_options[selector] = value
            return True
        return False
        
    async def wait_for_selector(self, selector: str, timeout: int = 30000) -> Mock:
        """Mock wait for selector."""
        if self.element_exists(selector):
            mock_element = Mock()
            return mock_element
        raise Exception(f"Selector {selector} not found")
        
    async def wait_for_load_state(self, state: str = "load", timeout: int = 30000):
        """Mock wait for load state."""
        pass
        
    async def text_content(self, selector: str) -> Optional[str]:
        """Mock text content method."""
        if self.element_exists(selector):
            return "Mock text content"
        return None
        
    def element_exists(self, selector: str) -> bool:
        """Check if element exists in mock page."""
        return self._element_exists_map.get(selector, True)  # Default to True for simplicity
        
    def set_element_exists(self, selector: str, exists: bool):
        """Set whether an element exists."""
        self._element_exists_map[selector] = exists
        
    def get_filled_value(self, selector: str) -> Optional[str]:
        """Get value that was filled into a field."""
        return self.filled_fields.get(selector)
        
    def was_clicked(self, selector: str) -> bool:
        """Check if element was clicked."""
        return selector in self.clicked_elements
        
    def get_selected_option(self, selector: str) -> Optional[str]:
        """Get selected option value."""
        return self.selected_options.get(selector)
        
    def click_count(self, selector_pattern: str) -> int:
        """Count how many times elements matching pattern were clicked."""
        return len([sel for sel in self.clicked_elements if selector_pattern in sel])
        
    def reset(self):
        """Reset all mock state."""
        self.filled_fields.clear()
        self.clicked_elements.clear()
        self.selected_options.clear()
        self._element_exists_map.clear()


class MockDS160Browser:
    """Mock browser for DS-160 testing."""
    
    def __init__(self):
        self.page = MockDS160Page()
        self.pages = [self.page]
        
    async def new_page(self) -> MockDS160Page:
        """Create new mock page."""
        new_page = MockDS160Page()
        self.pages.append(new_page)
        return new_page
        
    async def close(self):
        """Mock close method."""
        pass


class TestDataFactory:
    """Factory for creating test data for various scenarios."""
    
    @staticmethod
    def create_dual_nationality_data() -> Dict[str, Any]:
        """Create test data with multiple nationalities."""
        return {
            "personalInfo": {
                "surnames": "IVANOV",
                "givenNames": "IVAN",
                "sex": "MALE",
                "maritalStatus": "S",
                "dateOfBirth": "1990-01-01",
                "placeOfBirth": {
                    "city": "ASTANA",
                    "stateProvince": "AKMOLA", 
                    "countryRegion": "KAZAKHSTAN"
                }
            },
            "nationalityAndResidence": {
                "countryOfOrigin": "KAZAKHSTAN",
                "otherNationalities": [
                    {"country": "TURKEY", "hasPassport": "Yes", "passportNumber": "T123456"},
                    {"country": "RUSSIA", "hasPassport": "No"},
                    {"country": "GEORGIA", "hasPassport": "Yes", "passportNumber": "G789012"}
                ]
            },
            "passportInfo": {
                "passportNumber": "K123456",
                "issuingCountry": "KAZAKHSTAN",
                "issuingCity": "ASTANA",
                "issuingStateProvince": "AKMOLA",
                "issuanceDate": "2020-01-01",
                "expirationDate": "2030-01-01"
            },
            "contactInfo": {
                "mailingAddress": {
                    "streetLine1": "TEST STREET 1",
                    "city": "ASTANA",
                    "stateProvince": "AKMOLA",
                    "postalZoneZipCode": "010000",
                    "countryRegion": "KAZAKHSTAN"
                },
                "phoneNumbers": {"primary": "***********"},
                "emailAddresses": {"primary": "<EMAIL>"},
                "socialMedia": []
            },
            "travelInfo": {
                "purposeOfTrip": "TEMP. BUSINESS OR PLEASURE VISITOR (B)",
                "visaClass": "BUSINESS OR TOURISM (TEMPORARY VISITOR) (B1/B2)",
                "hasSpecificTravelPlans": "Yes",
                "arrivalDate": "2025-12-01",
                "arrivalCity": "NEW YORK",
                "usStayAddress": {
                    "streetLine1": "123 MAIN ST",
                    "city": "NEW YORK",
                    "state": "NEW YORK", 
                    "zipCode": "10001"
                },
                "personEntityPaying": "SELF"
            },
            "usContact": {
                "contactPersonSurnames": "SMITH",
                "contactPersonGivenNames": "JOHN",
                "relationshipToYou": "FRIEND",
                "address": {
                    "streetLine1": "456 OAK ST",
                    "city": "LOS ANGELES",
                    "state": "CALIFORNIA",
                    "zipCode": "90210"
                },
                "phone": "***********",
                "email": "<EMAIL>"
            },
            "familyInfo": {
                "father": {
                    "surnames": "IVANOV",
                    "givenNames": "PETROV",
                    "dateOfBirth": "1960-01-01",
                    "isInTheUS": "No",
                    "status": "FOREIGN NATIONAL"
                },
                "mother": {
                    "surnames": "IVANOVA",
                    "givenNames": "MARIA",
                    "dateOfBirth": "1965-01-01", 
                    "isInTheUS": "No",
                    "status": "FOREIGN NATIONAL"
                },
                "hasImmediateRelativesInUS": "No",
                "immediateRelatives": []
            },
            "workAndEducation": {
                "present": {
                    "primaryOccupation": "SOFTWARE ENGINEER",
                    "employerOrSchoolName": "TECH COMPANY",
                    "address": {
                        "streetLine1": "BUSINESS ST 1",
                        "city": "ASTANA",
                        "stateProvince": "AKMOLA",
                        "postalZoneZipCode": "010000",
                        "countryRegion": "KAZAKHSTAN"
                    },
                    "startDate": "2020-01-01",
                    "monthlyIncomeLocal": "500000",
                    "duties": "SOFTWARE DEVELOPMENT"
                },
                "previousWork": [],
                "previousEducation": []
            }
        }
    
    @staticmethod
    def create_multiple_social_media_data() -> Dict[str, Any]:
        """Create test data with multiple social media accounts."""
        base_data = TestDataFactory.create_dual_nationality_data()
        base_data["contactInfo"]["socialMedia"] = [
            {"platform": "INSTAGRAM", "identifier": "test_account_1"},
            {"platform": "INSTAGRAM", "identifier": "test_account_2"}
        ]
        return base_data
    
    @staticmethod
    def create_us_visit_history_data() -> Dict[str, Any]:
        """Create test data with US visit history."""
        base_data = TestDataFactory.create_dual_nationality_data()
        base_data["previousUSTravel"] = {
            "hasEverBeenInUS": "Yes",
            "previousVisits": [
                {"dateArrived": "2023-06-15", "lengthOfStay": "60", "unitOfStay": "DAYS"}
            ],
            "hasEverHeldUSLicense": "No",
            "driversLicenses": [],
            "hasEverBeenIssuedUSVisa": "No",
            "hasEverBeenRefusedAdmissionOrVisa": None,
            "hasImmigrantPetitionBeenFiled": None,
            "hasEverBeenDeniedTravelAuthorization": None
        }
        return base_data
    
    @staticmethod
    def create_visa_refusal_data() -> Dict[str, Any]:
        """Create test data with visa refusal history."""
        base_data = TestDataFactory.create_dual_nationality_data()
        base_data["previousUSTravel"] = {
            "hasEverBeenInUS": "No",
            "previousVisits": [],
            "hasEverHeldUSLicense": "No", 
            "driversLicenses": [],
            "hasEverBeenIssuedUSVisa": "No",
            "hasEverBeenRefusedAdmissionOrVisa": {
                "answer": "Yes",
                "explanation": "Test refusal explanation"
            },
            "hasImmigrantPetitionBeenFiled": None,
            "hasEverBeenDeniedTravelAuthorization": None
        }
        return base_data
    
    @staticmethod
    def create_parents_in_us_data() -> Dict[str, Any]:
        """Create test data with parents in US."""
        base_data = TestDataFactory.create_dual_nationality_data()
        base_data["familyInfo"]["father"]["isInTheUS"] = "Yes"
        base_data["familyInfo"]["father"]["status"] = "U.S. CITIZEN"
        base_data["familyInfo"]["mother"]["isInTheUS"] = "Yes"
        base_data["familyInfo"]["mother"]["status"] = "NONIMMIGRANT"
        return base_data
    
    @staticmethod
    def create_multiple_travel_companions_data() -> Dict[str, Any]:
        """Create test data with multiple travel companions."""
        base_data = TestDataFactory.create_dual_nationality_data()
        base_data["travelCompanions"] = {
            "areOtherPersonsTraveling": "Yes",
            "personsTraveling": [
                {"surnames": "COMPANION1", "givenNames": "FIRST", "relationship": "SPOUSE"},
                {"surnames": "COMPANION2", "givenNames": "SECOND", "relationship": "CHILD"},
                {"surnames": "COMPANION3", "givenNames": "THIRD", "relationship": "PARENT"}
            ],
            "isTravelingAsPartOfGroup": "No",
            "groupName": None
        }
        return base_data


# Pytest fixtures

@pytest.fixture
def mock_page():
    """Provide a mock DS-160 page for testing."""
    return MockDS160Page()


@pytest.fixture 
def mock_browser():
    """Provide a mock DS-160 browser for testing."""
    return MockDS160Browser()


@pytest.fixture
async def mock_ds160_data():
    """Provide basic DS-160 test data."""
    data_dict = TestDataFactory.create_dual_nationality_data()
    loader = DataLoader.from_dict(data_dict)
    return await loader.load_data()


@pytest.fixture
def test_data_factory():
    """Provide test data factory."""
    return TestDataFactory


@pytest.fixture
def test_data_dir():
    """Provide path to test data directory."""
    return Path(__file__).parent / "test_data"


# Helper functions for tests

def assert_field_filled(mock_page: MockDS160Page, selector: str, expected_value: str):
    """Assert that a field was filled with expected value."""
    actual = mock_page.get_filled_value(selector)
    assert actual == expected_value, f"Field {selector}: expected '{expected_value}', got '{actual}'"


def assert_radio_selected(mock_page: MockDS160Page, name: str, value: str):
    """Assert that radio button was selected."""
    selector = f"input[name='{name}'][value='{value}']"
    assert mock_page.was_clicked(selector), f"Radio button {selector} was not selected"


def assert_element_clicked(mock_page: MockDS160Page, selector: str):
    """Assert that element was clicked."""
    assert mock_page.was_clicked(selector), f"Element {selector} was not clicked"


def load_test_data_file(filename: str) -> Dict[str, Any]:
    """Load test data from JSON file."""
    test_data_path = Path(__file__).parent / "test_data" / filename
    with open(test_data_path, 'r', encoding='utf-8') as f:
        return json.load(f)


# Configure pytest for async tests
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests") 
    config.addinivalue_line("markers", "issues: Tests for specific issues")
    config.addinivalue_line("markers", "regression: Regression tests")


# Set event loop policy for Windows
if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())