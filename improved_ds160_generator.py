#!/usr/bin/env python3
"""
Улучшенный DS-160 Data Generator с условной валидацией.

Автоматически требует разные поля в зависимости от выбранных значений:
- marital_status -> требует spouse, deceased_spouse, divorce_info, или explanation
- has_passport -> требует passport_number если "Yes"  
- person_entity_paying -> требует соответствующую sponsor info
- и т.д.
"""

import json
from datetime import date, datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from pydantic import BaseModel, Field, model_validator

# Re-export enums for convenience
class Gender(str, Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"

class MaritalStatus(str, Enum):
    """Marital status with proper DS-160 codes."""
    SINGLE = "S"
    MARRIED = "M" 
    DIVORCED = "D"
    WIDOWED = "W"
    LEGALLY_SEPARATED = "L"
    COMMON_LAW = "C"
    CIVIL_UNION = "P"
    OTHER = "O"

class AddressType(str, Enum):
    """Address type options for DS-160 forms."""
    HOME = "H"
    MAILING = "M"
    US_CONTACT = "U"
    DO_NOT_KNOW = "D"
    OTHER = "O"

class PrimaryOccupation(str, Enum):
    """Primary occupation categories available in DS-160."""
    AGRICULTURE = "A"
    ARTIST_PERFORMER = "AP"
    BUSINESS = "B"
    COMMUNICATIONS = "CM"
    COMPUTER_SCIENCE = "CS"
    CULINARY_FOOD_SERVICES = "C"
    EDUCATION = "ED"
    ENGINEERING = "EN"
    GOVERNMENT = "G"
    HOMEMAKER = "H"
    LEGAL_PROFESSION = "LP"
    MEDICAL_HEALTH = "MH"
    MILITARY = "M"
    NATURAL_SCIENCE = "NS"
    NOT_EMPLOYED = "N"
    PHYSICAL_SCIENCES = "PS"
    RELIGIOUS_VOCATION = "RV"
    RESEARCH = "R"
    RETIRED = "RT"
    SOCIAL_SCIENCE = "SS"
    STUDENT = "S"
    OTHER = "O"

class VisaPurpose(str, Enum):
    BUSINESS_TOURISM = "TEMP. BUSINESS OR PLEASURE VISITOR (B)"
    STUDY = "STUDENT (F)"
    WORK = "TEMPORARY WORKER (H)"
    TRANSIT = "TRANSIT (C)"
    OTHER = "OTHER"

class PersonEntityPaying(str, Enum):
    """Кто оплачивает поездку - определяет какая sponsor info нужна."""
    SELF = "SELF"
    OTHER_PERSON = "OTHER PERSON"
    OTHER_COMPANY = "OTHER COMPANY/ORGANIZATION"
    US_PETITIONER = "U.S. PETITIONER"
    PRESENT_EMPLOYER = "PRESENT EMPLOYER"
    EMPLOYER_IN_US = "EMPLOYER IN THE U.S."

# Pydantic Models with Conditional Validation
class PlaceOfBirthBuilder(BaseModel):
    """Place of birth with validation."""
    city: str = Field(..., min_length=1, max_length=100)
    state_province: Optional[str] = Field(None)
    country_region: str = Field(..., min_length=1, max_length=100)

class OtherNamesUsedBuilder(BaseModel):
    """Other names used with validation."""
    other_surnames: str = Field(..., min_length=1, max_length=100)
    other_given_names: str = Field(..., min_length=1, max_length=100)

class AddressBuilder(BaseModel):
    """Address with validation."""
    street_line1: str = Field(..., min_length=1, max_length=200)
    street_line2: Optional[str] = Field(None, max_length=200)
    city: str = Field(..., min_length=1, max_length=100)
    state_province: Optional[str] = Field(None, max_length=100)
    postal_zone_zip_code: str = Field(..., min_length=1, max_length=20)
    country_region: str = Field(..., min_length=1, max_length=100)

class USAddressBuilder(BaseModel):
    """US address with validation."""
    street_line1: str = Field(..., min_length=1, max_length=200)
    city: str = Field(..., min_length=1, max_length=100)
    state: str = Field(..., min_length=2, max_length=100)  # US states are usually 2+ chars
    zip_code: str = Field(..., min_length=5, max_length=10)  # US ZIP codes

class SpouseInfoBuilder(BaseModel):
    """Spouse information with validation."""
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    date_of_birth: date = Field(..., description="Date of birth")
    place_of_birth: PlaceOfBirthBuilder
    nationality: str = Field(..., min_length=1, max_length=100)
    address_type: AddressType = Field(default=AddressType.HOME)
    address: Optional[AddressBuilder] = Field(None)

    @model_validator(mode='after')
    def validate_address_consistency(self):
        """Если address_type=OTHER, то address обязателен."""
        if self.address_type == AddressType.OTHER and not self.address:
            raise ValueError("Address is required when address_type is 'OTHER'")
        if self.address_type != AddressType.OTHER and self.address:
            self.address_type = AddressType.OTHER  # Автоматически устанавливаем OTHER
        return self

class DeceasedSpouseInfoBuilder(BaseModel):
    """Deceased spouse information for WIDOWED."""
    surnames: Optional[str] = Field(None, max_length=100)
    given_names: Optional[str] = Field(None, max_length=100)
    date_of_birth: Optional[date] = Field(None)
    nationality: Optional[str] = Field(None, max_length=100)
    place_of_birth_city: Optional[str] = Field(None, max_length=100)
    place_of_birth_city_unknown: bool = Field(default=False)
    place_of_birth_country: Optional[str] = Field(None, max_length=100)

class FormerSpouseInfoBuilder(BaseModel):
    """Former spouse information."""
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    date_of_birth: date = Field(...)
    nationality: str = Field(..., min_length=1, max_length=100)
    date_of_marriage: date = Field(...)
    date_marriage_ended: date = Field(...)
    how_marriage_ended: str = Field(..., max_length=4000)
    country_marriage_terminated: str = Field(..., min_length=1, max_length=100)

    @model_validator(mode='after')
    def validate_dates(self):
        """Проверяем логику дат."""
        if self.date_marriage_ended <= self.date_of_marriage:
            raise ValueError("Marriage end date must be after marriage date")
        if self.date_of_birth >= self.date_of_marriage:
            raise ValueError("Marriage date must be after birth date")
        return self

class DivorceInfoBuilder(BaseModel):
    """Divorce information for DIVORCED."""
    number_of_former_spouses: int = Field(..., ge=1, le=10)
    former_spouses: List[FormerSpouseInfoBuilder] = Field(..., min_items=1)

    @model_validator(mode='after')
    def validate_count(self):
        """Количество должно совпадать со списком."""
        if len(self.former_spouses) != self.number_of_former_spouses:
            raise ValueError(f"Number of former spouses ({self.number_of_former_spouses}) must match list length ({len(self.former_spouses)})")
        return self

class OtherNationalityBuilder(BaseModel):
    """Other nationality with conditional passport requirement."""
    country: str = Field(..., min_length=1, max_length=100)
    has_passport: str = Field(default="No", pattern="^(Yes|No)$")
    passport_number: Optional[str] = Field(None, max_length=50)

    @model_validator(mode='after')
    def validate_passport_requirement(self):
        """Если has_passport=Yes, то passport_number обязателен."""
        if self.has_passport == "Yes" and not self.passport_number:
            raise ValueError("Passport number is required when has_passport is 'Yes'")
        if self.has_passport == "No" and self.passport_number:
            raise ValueError("Passport number should not be provided when has_passport is 'No'")
        return self

class PersonSponsorBuilder(BaseModel):
    """Person sponsor information."""
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    relationship: str = Field(..., min_length=1, max_length=100)
    address: USAddressBuilder
    phone: str = Field(..., min_length=1)
    email: str = Field(..., min_length=1)  # Could add email validation

class CompanySponsorBuilder(BaseModel):
    """Company sponsor information."""
    company_name: str = Field(..., min_length=1, max_length=200)
    contact_person_surnames: str = Field(..., min_length=1, max_length=100)
    contact_person_given_names: str = Field(..., min_length=1, max_length=100)
    relationship: str = Field(..., min_length=1, max_length=100)
    address: USAddressBuilder
    phone: str = Field(..., min_length=1)
    email: str = Field(..., min_length=1)

class USPetitionerSponsorBuilder(BaseModel):
    """US Petitioner sponsor information."""
    petitioner_surnames: str = Field(..., min_length=1, max_length=100)
    petitioner_given_names: str = Field(..., min_length=1, max_length=100)
    relationship: str = Field(..., min_length=1, max_length=100)
    address: USAddressBuilder
    phone: str = Field(..., min_length=1)
    email: str = Field(..., min_length=1)

class EmployerSponsorBuilder(BaseModel):
    """Employer sponsor information."""
    employer_name: str = Field(..., min_length=1, max_length=200)
    contact_person_surnames: Optional[str] = Field(None, max_length=100)
    contact_person_given_names: Optional[str] = Field(None, max_length=100)
    address: USAddressBuilder
    phone: str = Field(..., min_length=1)
    email: str = Field(..., min_length=1)

class SponsorInfoBuilder(BaseModel):
    """
    Sponsor information с условной валидацией на основе person_entity_paying.
    
    Автоматически требует правильную sponsor info в зависимости от того,
    кто оплачивает поездку.
    """
    # Поле которое определяет какой sponsor нужен
    person_entity_paying: PersonEntityPaying = Field(...)
    
    # Опциональные sponsor поля - один из них станет обязательным
    person_sponsor: Optional[PersonSponsorBuilder] = Field(None)
    company_sponsor: Optional[CompanySponsorBuilder] = Field(None)
    us_petitioner_sponsor: Optional[USPetitionerSponsorBuilder] = Field(None)
    employer_sponsor: Optional[EmployerSponsorBuilder] = Field(None)

    @model_validator(mode='after')
    def validate_sponsor_consistency(self):
        """Проверяем что указан правильный sponsor для person_entity_paying."""
        errors = []
        
        if self.person_entity_paying == PersonEntityPaying.SELF:
            # Для SELF никакой sponsor не нужен
            if any([self.person_sponsor, self.company_sponsor, self.us_petitioner_sponsor, self.employer_sponsor]):
                errors.append("No sponsor information should be provided when person_entity_paying is SELF")
        
        elif self.person_entity_paying == PersonEntityPaying.OTHER_PERSON:
            if not self.person_sponsor:
                errors.append("Person sponsor information is required when person_entity_paying is OTHER_PERSON")
            if any([self.company_sponsor, self.us_petitioner_sponsor, self.employer_sponsor]):
                errors.append("Only person sponsor should be provided for OTHER_PERSON")
        
        elif self.person_entity_paying == PersonEntityPaying.OTHER_COMPANY:
            if not self.company_sponsor:
                errors.append("Company sponsor information is required when person_entity_paying is OTHER_COMPANY")
            if any([self.person_sponsor, self.us_petitioner_sponsor, self.employer_sponsor]):
                errors.append("Only company sponsor should be provided for OTHER_COMPANY")
        
        elif self.person_entity_paying == PersonEntityPaying.US_PETITIONER:
            if not self.us_petitioner_sponsor:
                errors.append("US Petitioner sponsor information is required when person_entity_paying is US_PETITIONER")
            if any([self.person_sponsor, self.company_sponsor, self.employer_sponsor]):
                errors.append("Only US petitioner sponsor should be provided for US_PETITIONER")
        
        elif self.person_entity_paying in {PersonEntityPaying.PRESENT_EMPLOYER, PersonEntityPaying.EMPLOYER_IN_US}:
            if not self.employer_sponsor:
                errors.append(f"Employer sponsor information is required when person_entity_paying is {self.person_entity_paying}")
            if any([self.person_sponsor, self.company_sponsor, self.us_petitioner_sponsor]):
                errors.append(f"Only employer sponsor should be provided for {self.person_entity_paying}")
        
        if errors:
            raise ValueError("; ".join(errors))
        
        return self

class PersonalInfoBuilder(BaseModel):
    """
    Personal information с условной валидацией на основе marital_status.
    """
    surnames: str = Field(..., min_length=1, max_length=100)
    given_names: str = Field(..., min_length=1, max_length=100)
    full_name_native_alphabet: Optional[str] = Field(None)
    other_names_used: List[OtherNamesUsedBuilder] = Field(default_factory=list)
    telecode_represents_name: str = Field(default="No")
    sex: Gender = Field(...)
    marital_status: MaritalStatus = Field(...)
    date_of_birth: date = Field(...)
    place_of_birth: Optional[PlaceOfBirthBuilder] = Field(None)
    
    # Условные поля на основе marital_status
    marital_status_other_explanation: Optional[str] = Field(None, max_length=4000)
    spouse: Optional[SpouseInfoBuilder] = Field(None)
    deceased_spouse: Optional[DeceasedSpouseInfoBuilder] = Field(None)
    divorce_info: Optional[DivorceInfoBuilder] = Field(None)

    @model_validator(mode='after')
    def validate_marital_status_fields(self):
        """Условная валидация полей на основе marital_status."""
        errors = []
        
        married_statuses = {MaritalStatus.MARRIED, MaritalStatus.COMMON_LAW, MaritalStatus.CIVIL_UNION}
        
        if self.marital_status in married_statuses:
            if not self.spouse:
                errors.append(f"Spouse information is required for marital status '{self.marital_status.name}'")
            # Очистка несовместимых полей
            if self.deceased_spouse:
                errors.append("Cannot have deceased spouse info when currently married")
            if self.divorce_info:
                errors.append("Cannot have divorce info when currently married")
        
        elif self.marital_status == MaritalStatus.WIDOWED:
            if not self.deceased_spouse:
                errors.append("Deceased spouse information is required for WIDOWED status")
            if self.spouse:
                errors.append("Cannot have current spouse info when WIDOWED")
            if self.divorce_info:
                errors.append("Cannot have divorce info when WIDOWED")
        
        elif self.marital_status == MaritalStatus.DIVORCED:
            if not self.divorce_info:
                errors.append("Divorce information is required for DIVORCED status")
            if self.spouse:
                errors.append("Cannot have current spouse info when DIVORCED")
            if self.deceased_spouse:
                errors.append("Cannot have deceased spouse info when DIVORCED")
        
        elif self.marital_status == MaritalStatus.OTHER:
            if not self.marital_status_other_explanation:
                errors.append("Explanation is required when marital status is OTHER")
            if self.marital_status_other_explanation and not self.marital_status_other_explanation.strip():
                errors.append("Marital status explanation cannot be empty for OTHER status")
        
        # Общие проверки
        if self.marital_status != MaritalStatus.OTHER and self.marital_status_other_explanation:
            errors.append("Marital status explanation should only be provided for OTHER status")
        
        if errors:
            raise ValueError("; ".join(errors))
        
        return self

class NationalityAndResidenceBuilder(BaseModel):
    """Nationality with conditional validation for other nationalities."""
    country_of_origin: str = Field(..., min_length=1, max_length=100)
    other_nationalities: List[OtherNationalityBuilder] = Field(default_factory=list)
    is_permanent_resident_of_other_country: str = Field(default="No", pattern="^(Yes|No)$")
    permanent_resident_countries: List[str] = Field(default_factory=list)
    national_identification_number: Optional[str] = Field(None, max_length=50)
    us_social_security_number: Optional[str] = Field(None, max_length=15)
    us_taxpayer_id_number: Optional[str] = Field(None, max_length=15)

    @model_validator(mode='after')
    def validate_permanent_residence(self):
        """Если is_permanent_resident=Yes, то permanent_resident_countries обязателен."""
        if self.is_permanent_resident_of_other_country == "Yes" and not self.permanent_resident_countries:
            raise ValueError("Permanent resident countries list is required when is_permanent_resident_of_other_country is 'Yes'")
        if self.is_permanent_resident_of_other_country == "No" and self.permanent_resident_countries:
            raise ValueError("Permanent resident countries should not be provided when is_permanent_resident_of_other_country is 'No'")
        return self

# Main Builder Class
class ImprovedDS160DataBuilder:
    """
    Улучшенный DS-160 Data Builder с условной валидацией.
    
    Автоматически требует правильные поля в зависимости от выбранных значений:
    - Marital status определяет нужную информацию о супруге/разводе
    - Person entity paying определяет нужную sponsor информацию
    - Has passport определяет нужность passport number
    - И многие другие условия
    """
    
    def __init__(self):
        """Initialize with basic structure."""
        # Required sections - will be validated
        self.personal_info: Optional[PersonalInfoBuilder] = None
        self.nationality_and_residence: Optional[NationalityAndResidenceBuilder] = None
        self.sponsor_info: Optional[SponsorInfoBuilder] = None
        
        # Simple fields for demo (in real app these would also be Pydantic models)
        self.application_info = {"form_type": "DS-160", "application_action": "new"}
        self.passport_info = {}
        self.contact_info = {}
        self.travel_info = {}
        self.us_contact = {}
        self.family_info = {}
        self.work_and_education = {}
        self.security_and_background = None
    
    def set_personal_info(self, **kwargs) -> PersonalInfoBuilder:
        """Set and validate personal information."""
        self.personal_info = PersonalInfoBuilder(**kwargs)
        return self.personal_info
    
    def set_nationality_and_residence(self, **kwargs) -> NationalityAndResidenceBuilder:
        """Set and validate nationality information.""" 
        self.nationality_and_residence = NationalityAndResidenceBuilder(**kwargs)
        return self.nationality_and_residence
    
    def set_sponsor_info(self, **kwargs) -> SponsorInfoBuilder:
        """Set and validate sponsor information."""
        self.sponsor_info = SponsorInfoBuilder(**kwargs)
        return self.sponsor_info
    
    def validate(self) -> List[str]:
        """Validate all data and return issues."""
        issues = []
        
        if not self.personal_info:
            issues.append("Personal info is required")
        else:
            try:
                # Pydantic validation happens automatically when creating the model
                pass
            except Exception as e:
                issues.append(f"Personal info validation error: {e}")
        
        if not self.nationality_and_residence:
            issues.append("Nationality and residence info is required")
        else:
            try:
                # Pydantic validation happens automatically
                pass
            except Exception as e:
                issues.append(f"Nationality validation error: {e}")
        
        # Add more validation as needed
        
        return issues
    
    def generate_json(self) -> Dict[str, Any]:
        """Generate JSON with proper conversion."""
        def convert_pydantic_to_dict(obj):
            if hasattr(obj, 'model_dump'):
                return obj.model_dump(exclude_none=True)
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            else:
                return obj
        
        return {
            "applicationInfo": self.application_info,
            "personalInfo": convert_pydantic_to_dict(self.personal_info) if self.personal_info else None,
            "nationalityAndResidence": convert_pydantic_to_dict(self.nationality_and_residence) if self.nationality_and_residence else None,
            "sponsorInfo": convert_pydantic_to_dict(self.sponsor_info) if self.sponsor_info else None,
            # Add other sections...
        }
    
    def save_json(self, file_path: Union[str, Path], indent: int = 2) -> None:
        """Save to JSON file."""
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        data = self.generate_json()
        data = {k: v for k, v in data.items() if v is not None}
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent, default=str)
        
        print(f"✅ Improved DS-160 data saved to: {file_path}")


def test_improved_conditional_validation():
    """Тест улучшенной условной валидации."""
    print("🧪 Тестируем улучшенную условную валидацию...")
    
    builder = ImprovedDS160DataBuilder()
    
    # Тест 1: Правильная информация для женатого человека
    print("\n1️⃣ Тест MARRIED с spouse info:")
    try:
        spouse = SpouseInfoBuilder(
            surnames="SMITH",
            given_names="JANE",
            date_of_birth=date(1992, 5, 10),
            place_of_birth=PlaceOfBirthBuilder(
                city="BOSTON",
                country_region="USA"
            ),
            nationality="AMERICAN"
        )
        
        personal = builder.set_personal_info(
            surnames="SMITH",
            given_names="JOHN",
            sex=Gender.MALE,
            marital_status=MaritalStatus.MARRIED,
            date_of_birth=date(1990, 1, 1),
            spouse=spouse
        )
        
        print("✅ MARRIED with spouse - validation passed")
        
    except Exception as e:
        print(f"❌ MARRIED validation failed: {e}")
    
    # Тест 2: Ошибка - MARRIED без spouse
    print("\n2️⃣ Тест MARRIED без spouse info (должен упасть):")
    try:
        builder2 = ImprovedDS160DataBuilder()
        builder2.set_personal_info(
            surnames="SMITH",
            given_names="JOHN",
            sex=Gender.MALE,
            marital_status=MaritalStatus.MARRIED,
            date_of_birth=date(1990, 1, 1)
            # spouse=None - НЕ УКАЗАН!
        )
        print("❌ Should have failed!")
        
    except Exception as e:
        print(f"✅ Correctly failed: {e}")
    
    # Тест 3: Other nationality с passport
    print("\n3️⃣ Тест other nationality с passport:")
    try:
        nationality = builder.set_nationality_and_residence(
            country_of_origin="KAZAKHSTAN",
            other_nationalities=[
                OtherNationalityBuilder(
                    country="RUSSIA",
                    has_passport="Yes",
                    passport_number="RF123456"
                )
            ]
        )
        
        print("✅ Other nationality with passport - validation passed")
        
    except Exception as e:
        print(f"❌ Other nationality validation failed: {e}")
    
    # Тест 4: Ошибка - has_passport=Yes но без passport_number
    print("\n4️⃣ Тест has_passport=Yes без passport_number (должен упасть):")
    try:
        builder3 = ImprovedDS160DataBuilder()
        builder3.set_nationality_and_residence(
            country_of_origin="KAZAKHSTAN",
            other_nationalities=[
                OtherNationalityBuilder(
                    country="RUSSIA",
                    has_passport="Yes"
                    # passport_number=None - НЕ УКАЗАН!
                )
            ]
        )
        print("❌ Should have failed!")
        
    except Exception as e:
        print(f"✅ Correctly failed: {e}")
    
    # Тест 5: Sponsor info - OTHER_PERSON
    print("\n5️⃣ Тест sponsor info для OTHER_PERSON:")
    try:
        sponsor = builder.set_sponsor_info(
            person_entity_paying=PersonEntityPaying.OTHER_PERSON,
            person_sponsor=PersonSponsorBuilder(
                surnames="JOHNSON",
                given_names="JANE",
                relationship="FRIEND",
                address=USAddressBuilder(
                    street_line1="123 MAIN ST",
                    city="NEW YORK",
                    state="NY",
                    zip_code="10001"
                ),
                phone="******-123-4567",
                email="<EMAIL>"
            )
        )
        
        print("✅ OTHER_PERSON sponsor - validation passed")
        
    except Exception as e:
        print(f"❌ Sponsor validation failed: {e}")
    
    # Тест 6: Ошибка - OTHER_PERSON без person_sponsor
    print("\n6️⃣ Тест OTHER_PERSON без person_sponsor (должен упасть):")
    try:
        builder4 = ImprovedDS160DataBuilder()
        builder4.set_sponsor_info(
            person_entity_paying=PersonEntityPaying.OTHER_PERSON
            # person_sponsor=None - НЕ УКАЗАН!
        )
        print("❌ Should have failed!")
        
    except Exception as e:
        print(f"✅ Correctly failed: {e}")
    
    # Генерация JSON
    print("\n7️⃣ Генерация JSON:")
    try:
        json_data = builder.generate_json()
        print(f"✅ JSON generated with {len(json_data)} sections")
        
        # Save to file
        builder.save_json("data/improved_validation_test.json")
        
    except Exception as e:
        print(f"❌ JSON generation failed: {e}")
    
    print("\n🎉 Все тесты условной валидации выполнены!")


if __name__ == "__main__":
    test_improved_conditional_validation()