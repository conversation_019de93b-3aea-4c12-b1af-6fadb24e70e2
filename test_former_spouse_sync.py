#!/usr/bin/env python3
"""
Quick Former Spouse Data Validation Test - Synchronous Version
Tests that JSON data contains correct field names for former spouse mapping.
"""

import json
import sys
from pathlib import Path
from typing import List

def test_json_field_names(json_file_path: str) -> bool:
    """Test that JSON contains correct former spouse field names."""
    
    print(f"🧪 Testing JSON field names for: {Path(json_file_path).name}")
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # Check for former spouse data
        if 'personalInfo' not in data:
            print("ℹ️  No personalInfo section found")
            return True
            
        personal_info = data['personalInfo']
        
        # Check marital status
        marital_status = personal_info.get('maritalStatus', '')
        print(f"📋 Marital Status: {marital_status}")
        
        # Look for former spouse data
        former_spouses_found = False
        
        if 'formerSpouses' in personal_info:
            former_spouses = personal_info['formerSpouses']
            if former_spouses:
                former_spouses_found = True
                print(f"👥 Found {len(former_spouses)} former spouse(s)")
                
                # Test field names in each former spouse
                for i, spouse in enumerate(former_spouses, 1):
                    print(f"\n🔍 Former Spouse {i} Fields:")
                    
                    # Check required fields
                    required_fields = [
                        'surnames', 'givenNames', 'dateOfBirth', 'nationality',
                        'dateOfMarriage', 'dateMarriageEnded', 'howMarriageEnded'
                    ]
                    
                    for field in required_fields:
                        if field in spouse:
                            print(f"  ✅ {field}: {spouse[field]}")
                        else:
                            print(f"  ❌ Missing: {field}")
                    
                    # THE KEY TEST - Check for corrected field name
                    if 'marriageTerminationCountry' in spouse:
                        print(f"  ✅ marriageTerminationCountry: {spouse['marriageTerminationCountry']}")
                    elif 'countryMarriageTerminated' in spouse:
                        print(f"  ⚠️  OLD FIELD NAME: countryMarriageTerminated (should be marriageTerminationCountry)")
                        return False
                    else:
                        print(f"  ⚠️  No marriage termination country field found")
                    
                    # Check birth place
                    if 'placeOfBirth' in spouse:
                        birth_place = spouse['placeOfBirth']
                        print(f"  ✅ placeOfBirth.city: {birth_place.get('city', 'N/A')}")
                        print(f"  ✅ placeOfBirth.countryRegion: {birth_place.get('countryRegion', 'N/A')}")
                    else:
                        print(f"  ⚠️  No placeOfBirth found")
        
        if not former_spouses_found:
            print("ℹ️  No former spouse data found - test passed (no validation needed)")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def search_for_old_field_names(json_file_path: str) -> List[str]:
    """Search for old field names that need updating."""
    
    issues = []
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for problematic field names
        old_fields = [
            'countryMarriageTerminated',
            'country_marriage_terminated'
        ]
        
        for old_field in old_fields:
            if old_field in content:
                issues.append(f"Found old field name: {old_field}")
                
    except Exception as e:
        issues.append(f"Error reading file: {e}")
    
    return issues

def main():
    """Run comprehensive former spouse field name validation."""
    
    print("=" * 60)
    print("🧪 FORMER SPOUSE JSON FIELD VALIDATION")
    print("=" * 60)
    
    # Find JSON files
    data_dir = Path(__file__).parent / "data"
    json_files = list(data_dir.glob("*.json")) if data_dir.exists() else []
    
    if not json_files:
        print("❌ No JSON files found in data/ directory")
        return False
    
    print(f"📁 Testing {len(json_files)} JSON file(s)")
    
    # Test each file
    all_passed = True
    
    for json_file in json_files:
        print(f"\n{'='*40}")
        success = test_json_field_names(str(json_file))
        
        # Check for old field names
        issues = search_for_old_field_names(str(json_file))
        if issues:
            print(f"⚠️  Issues found in {json_file.name}:")
            for issue in issues:
                print(f"    • {issue}")
            success = False
        
        all_passed = all_passed and success
    
    # Final result
    print("\n" + "=" * 60)
    if all_passed:
        print("🎯 RESULT: ✅ ALL TESTS PASSED")
        print("🚀 Former spouse JSON field names are correct!")
    else:
        print("🎯 RESULT: ❌ SOME TESTS FAILED") 
        print("🔧 Review the issues above and update JSON files")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)